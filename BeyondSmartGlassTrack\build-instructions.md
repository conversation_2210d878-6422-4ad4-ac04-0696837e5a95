# Beyond Smart Glass Track - Build Instructions

## 🏗️ Building the Application

### Prerequisites
- Node.js (version 18 or newer)
- npm or yarn package manager

### Build Steps

1. **Install Dependencies**
   ```bash
   cd BeyondSmartGlassTrack
   npm install
   ```

2. **Environment Setup**
   Create `.env.local` file in the root directory:
   ```env
   VITE_SUPABASE_URL=your-supabase-url
   VITE_SUPABASE_ANON_KEY=your-supabase-anon-key
   ```

3. **Development Server**
   ```bash
   npm run dev
   ```
   Opens at: http://localhost:5173

4. **Production Build**
   ```bash
   npm run build
   ```
   Creates optimized files in `dist/` folder

5. **Preview Production Build**
   ```bash
   npm run preview
   ```

### Build Output
- `dist/index.html` - Main HTML file
- `dist/assets/` - CSS, JS, and other assets
- `dist/` - Ready for deployment

### Deployment Options

#### Option 1: Static Hosting (Netlify, Vercel)
1. Upload `dist/` folder contents
2. Set environment variables in hosting platform
3. Configure redirects for SPA routing

#### Option 2: Traditional Web Server
1. Copy `dist/` contents to web server
2. Configure server for SPA routing
3. Set up HTTPS (recommended)

#### Option 3: CDN Deployment
1. Upload to CDN (AWS S3, Azure Blob, etc.)
2. Configure CloudFront/CDN for SPA
3. Set up custom domain

### Environment Variables for Production
```env
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key
VITE_APP_ENV=production
```

### Performance Optimizations
- Code splitting enabled
- Tree shaking for unused code
- Asset optimization
- Gzip compression recommended
- CDN for static assets

### Browser Support
- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

### Security Considerations
- Environment variables for sensitive data
- HTTPS required for production
- CSP headers recommended
- Regular dependency updates

## 🚀 Quick Start (If Node.js is available)

```bash
# Clone and setup
cd BeyondSmartGlassTrack

# Install dependencies
npm install

# Start development
npm run dev

# Build for production
npm run build
```

## 📱 Features Included in Build
- ✅ Responsive design for all devices
- ✅ PWA capabilities
- ✅ Dark/Light mode
- ✅ Multi-language support (EN/AR)
- ✅ Offline functionality
- ✅ Modern browser optimizations

## 🔧 Troubleshooting

### Common Issues:
1. **Node.js not found**: Install from nodejs.org
2. **Permission errors**: Run as administrator
3. **Port conflicts**: Change port in vite.config.js
4. **Build failures**: Clear node_modules and reinstall

### Support:
- Check package.json for scripts
- Review vite.config.js for build settings
- Verify environment variables
- Test in development before building
