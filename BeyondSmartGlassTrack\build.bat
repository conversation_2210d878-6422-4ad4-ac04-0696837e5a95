@echo off
echo ========================================
echo Beyond Smart Glass Track - Build Script
echo ========================================
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed or not in PATH
    echo.
    echo Please install Node.js from: https://nodejs.org
    echo Then run this script again.
    echo.
    pause
    exit /b 1
)

REM Check if npm is available
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm is not available
    echo.
    echo Please ensure npm is installed with Node.js
    echo.
    pause
    exit /b 1
)

echo ✅ Node.js and npm are available
echo.

REM Check if node_modules exists
if not exist "node_modules" (
    echo 📦 Installing dependencies...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ Failed to install dependencies
        pause
        exit /b 1
    )
    echo ✅ Dependencies installed successfully
    echo.
) else (
    echo ✅ Dependencies already installed
    echo.
)

REM Check for environment file
if not exist ".env.local" (
    echo ⚠️  Warning: .env.local file not found
    echo.
    echo Creating sample .env.local file...
    echo VITE_SUPABASE_URL=your-supabase-url > .env.local
    echo VITE_SUPABASE_ANON_KEY=your-supabase-anon-key >> .env.local
    echo.
    echo ✅ Sample .env.local created
    echo Please edit .env.local with your actual Supabase credentials
    echo.
)

echo 🏗️  Building application for production...
npm run build
if %errorlevel% neq 0 (
    echo ❌ Build failed
    pause
    exit /b 1
)

echo.
echo ✅ Build completed successfully!
echo.
echo 📁 Build output is in the 'dist' folder
echo 🌐 You can deploy the contents of 'dist' folder to any web server
echo.
echo To preview the build locally, run: npm run preview
echo.

REM Check if dist folder exists and show contents
if exist "dist" (
    echo 📋 Build contents:
    dir dist /b
    echo.
)

echo 🚀 Build process completed!
echo.
pause
