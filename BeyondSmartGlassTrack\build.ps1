# Beyond Smart Glass Track - PowerShell Build Script

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Beyond Smart Glass Track - Build Script" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Check if Node.js is installed
try {
    $nodeVersion = node --version 2>$null
    Write-Host "✅ Node.js version: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js is not installed or not in PATH" -ForegroundColor Red
    Write-Host ""
    Write-Host "Please install Node.js from: https://nodejs.org" -ForegroundColor Yellow
    Write-Host "Then run this script again." -ForegroundColor Yellow
    Write-Host ""
    Read-Host "Press Enter to exit"
    exit 1
}

# Check if npm is available
try {
    $npmVersion = npm --version 2>$null
    Write-Host "✅ npm version: $npmVersion" -ForegroundColor Green
    Write-Host ""
} catch {
    Write-Host "❌ npm is not available" -ForegroundColor Red
    Write-Host ""
    Write-Host "Please ensure npm is installed with Node.js" -ForegroundColor Yellow
    Write-Host ""
    Read-Host "Press Enter to exit"
    exit 1
}

# Check if node_modules exists
if (-not (Test-Path "node_modules")) {
    Write-Host "📦 Installing dependencies..." -ForegroundColor Yellow
    try {
        npm install
        Write-Host "✅ Dependencies installed successfully" -ForegroundColor Green
        Write-Host ""
    } catch {
        Write-Host "❌ Failed to install dependencies" -ForegroundColor Red
        Read-Host "Press Enter to exit"
        exit 1
    }
} else {
    Write-Host "✅ Dependencies already installed" -ForegroundColor Green
    Write-Host ""
}

# Check for environment file
if (-not (Test-Path ".env.local")) {
    Write-Host "⚠️  Warning: .env.local file not found" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Creating sample .env.local file..." -ForegroundColor Yellow
    
    $envContent = @"
VITE_SUPABASE_URL=your-supabase-url
VITE_SUPABASE_ANON_KEY=your-supabase-anon-key
"@
    
    $envContent | Out-File -FilePath ".env.local" -Encoding UTF8
    Write-Host "✅ Sample .env.local created" -ForegroundColor Green
    Write-Host "Please edit .env.local with your actual Supabase credentials" -ForegroundColor Yellow
    Write-Host ""
}

Write-Host "🏗️  Building application for production..." -ForegroundColor Yellow
try {
    npm run build
    Write-Host ""
    Write-Host "✅ Build completed successfully!" -ForegroundColor Green
} catch {
    Write-Host "❌ Build failed" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""
Write-Host "📁 Build output is in the 'dist' folder" -ForegroundColor Cyan
Write-Host "🌐 You can deploy the contents of 'dist' folder to any web server" -ForegroundColor Cyan
Write-Host ""
Write-Host "To preview the build locally, run: npm run preview" -ForegroundColor Cyan
Write-Host ""

# Check if dist folder exists and show contents
if (Test-Path "dist") {
    Write-Host "📋 Build contents:" -ForegroundColor Cyan
    Get-ChildItem "dist" | ForEach-Object { Write-Host "   $($_.Name)" -ForegroundColor White }
    Write-Host ""
}

Write-Host "🚀 Build process completed!" -ForegroundColor Green
Write-Host ""

# Option to open demo file
if (Test-Path "demo.html") {
    $openDemo = Read-Host "Would you like to open the demo page? (y/n)"
    if ($openDemo -eq "y" -or $openDemo -eq "Y") {
        Start-Process "demo.html"
    }
}

Read-Host "Press Enter to exit"
