<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Beyond Smart Glass Track - Demo</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .card-hover {
            transition: all 0.3s ease;
        }
        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body class="bg-gray-50 font-sans">
    <!-- Header -->
    <header class="gradient-bg text-white shadow-lg">
        <div class="container mx-auto px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <div class="w-10 h-10 bg-white rounded-lg flex items-center justify-center">
                        <i data-lucide="building-2" class="w-6 h-6 text-blue-600"></i>
                    </div>
                    <div>
                        <h1 class="text-2xl font-bold">Beyond Smart Glass Track</h1>
                        <p class="text-blue-100 text-sm">Smart Glass Project Management System</p>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <span class="text-blue-100">Demo Version</span>
                    <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                        <i data-lucide="user" class="w-5 h-5"></i>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Navigation -->
    <nav class="bg-white shadow-sm border-b">
        <div class="container mx-auto px-6">
            <div class="flex space-x-8 overflow-x-auto">
                <a href="#dashboard" class="flex items-center space-x-2 py-4 border-b-2 border-blue-500 text-blue-600">
                    <i data-lucide="home" class="w-4 h-4"></i>
                    <span>Dashboard</span>
                </a>
                <a href="#quotations" class="flex items-center space-x-2 py-4 text-gray-600 hover:text-blue-600">
                    <i data-lucide="file-text" class="w-4 h-4"></i>
                    <span>Quotations</span>
                </a>
                <a href="#contracts" class="flex items-center space-x-2 py-4 text-gray-600 hover:text-blue-600">
                    <i data-lucide="file-check" class="w-4 h-4"></i>
                    <span>Contracts</span>
                </a>
                <a href="#manufacturing" class="flex items-center space-x-2 py-4 text-gray-600 hover:text-blue-600">
                    <i data-lucide="factory" class="w-4 h-4"></i>
                    <span>Manufacturing</span>
                </a>
                <a href="#installations" class="flex items-center space-x-2 py-4 text-gray-600 hover:text-blue-600">
                    <i data-lucide="wrench" class="w-4 h-4"></i>
                    <span>Installations</span>
                </a>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="container mx-auto px-6 py-8">
        <!-- Dashboard Section -->
        <section id="dashboard">
            <!-- Stats Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <div class="bg-white rounded-lg shadow-md p-6 card-hover">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-600">Total Quotations</p>
                            <p class="text-2xl font-bold text-gray-900">45</p>
                            <p class="text-sm text-gray-500">SAR 850,000</p>
                        </div>
                        <div class="p-3 bg-blue-500 rounded-full">
                            <i data-lucide="file-text" class="w-6 h-6 text-white"></i>
                        </div>
                    </div>
                    <div class="mt-4 flex items-center text-sm">
                        <i data-lucide="trending-up" class="w-4 h-4 text-green-500 mr-1"></i>
                        <span class="text-green-500 font-medium">+12%</span>
                        <span class="text-gray-500 ml-1">vs last month</span>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-md p-6 card-hover">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-600">Active Contracts</p>
                            <p class="text-2xl font-bold text-gray-900">28</p>
                            <p class="text-sm text-gray-500">SAR 650,000</p>
                        </div>
                        <div class="p-3 bg-green-500 rounded-full">
                            <i data-lucide="file-check" class="w-6 h-6 text-white"></i>
                        </div>
                    </div>
                    <div class="mt-4 flex items-center text-sm">
                        <i data-lucide="trending-up" class="w-4 h-4 text-green-500 mr-1"></i>
                        <span class="text-green-500 font-medium">+8%</span>
                        <span class="text-gray-500 ml-1">vs last month</span>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-md p-6 card-hover">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-600">Manufacturing Queue</p>
                            <p class="text-2xl font-bold text-gray-900">15</p>
                            <p class="text-sm text-gray-500">2 ready</p>
                        </div>
                        <div class="p-3 bg-orange-500 rounded-full">
                            <i data-lucide="factory" class="w-6 h-6 text-white"></i>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-md p-6 card-hover">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-600">Active Installations</p>
                            <p class="text-2xl font-bold text-gray-900">3</p>
                            <p class="text-sm text-gray-500">8 completed</p>
                        </div>
                        <div class="p-3 bg-purple-500 rounded-full">
                            <i data-lucide="wrench" class="w-6 h-6 text-white"></i>
                        </div>
                    </div>
                    <div class="mt-4 flex items-center text-sm">
                        <i data-lucide="trending-up" class="w-4 h-4 text-green-500 mr-1"></i>
                        <span class="text-green-500 font-medium">+15%</span>
                        <span class="text-gray-500 ml-1">vs last month</span>
                    </div>
                </div>
            </div>

            <!-- Charts and Quick Actions -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Quick Actions -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
                    <div class="space-y-3">
                        <button class="w-full flex items-center justify-start p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                            <i data-lucide="file-text" class="w-5 h-5 text-blue-500 mr-3"></i>
                            <span>Create New Quotation</span>
                        </button>
                        <button class="w-full flex items-center justify-start p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                            <i data-lucide="calendar" class="w-5 h-5 text-green-500 mr-3"></i>
                            <span>Schedule Site Visit</span>
                        </button>
                        <button class="w-full flex items-center justify-start p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                            <i data-lucide="factory" class="w-5 h-5 text-orange-500 mr-3"></i>
                            <span>Add to Manufacturing Queue</span>
                        </button>
                        <button class="w-full flex items-center justify-start p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                            <i data-lucide="package" class="w-5 h-5 text-purple-500 mr-3"></i>
                            <span>Update Inventory</span>
                        </button>
                    </div>
                </div>

                <!-- Inventory Status -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <i data-lucide="package" class="w-5 h-5 mr-2"></i>
                        Inventory Status
                    </h3>
                    <div class="space-y-4">
                        <div class="flex justify-between items-center">
                            <span class="text-sm font-medium">Smart Film Rolls</span>
                            <span class="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">150 units</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm font-medium">Smart Glass Panels</span>
                            <span class="px-2 py-1 bg-green-100 text-green-800 rounded-full text-sm">25 units</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm font-medium">Control Devices</span>
                            <span class="px-2 py-1 bg-orange-100 text-orange-800 rounded-full text-sm">45 units</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm font-medium">Remote Controls</span>
                            <span class="px-2 py-1 bg-purple-100 text-purple-800 rounded-full text-sm">120 units</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="mt-8 bg-white rounded-lg shadow-md p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h3>
                <div class="space-y-4">
                    <div class="flex items-center space-x-4 p-3 bg-gray-50 rounded-lg">
                        <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                            <i data-lucide="file-text" class="w-4 h-4 text-white"></i>
                        </div>
                        <div class="flex-1">
                            <p class="text-sm font-medium">New quotation created for Al-Riyadh Office Building</p>
                            <p class="text-xs text-gray-500">2 hours ago</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-4 p-3 bg-gray-50 rounded-lg">
                        <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                            <i data-lucide="file-check" class="w-4 h-4 text-white"></i>
                        </div>
                        <div class="flex-1">
                            <p class="text-sm font-medium">Contract BSG-CON-2024-0015 signed and approved</p>
                            <p class="text-xs text-gray-500">5 hours ago</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-4 p-3 bg-gray-50 rounded-lg">
                        <div class="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center">
                            <i data-lucide="factory" class="w-4 h-4 text-white"></i>
                        </div>
                        <div class="flex-1">
                            <p class="text-sm font-medium">Manufacturing completed for smart glass panels</p>
                            <p class="text-xs text-gray-500">1 day ago</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-8 mt-12">
        <div class="container mx-auto px-6 text-center">
            <div class="flex items-center justify-center space-x-2 mb-4">
                <div class="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
                    <i data-lucide="building-2" class="w-5 h-5 text-white"></i>
                </div>
                <span class="text-xl font-bold">Beyond Smart Glass Track</span>
            </div>
            <p class="text-gray-400 mb-4">Smart Glass Project Management System</p>
            <div class="flex justify-center space-x-6 text-sm text-gray-400">
                <span>✅ Quotations Management</span>
                <span>✅ Contract Tracking</span>
                <span>✅ Manufacturing Queue</span>
                <span>✅ Installation Progress</span>
                <span>✅ Inventory Control</span>
            </div>
            <div class="mt-6 pt-6 border-t border-gray-700">
                <p class="text-gray-400 text-sm">© 2024 Beyond Smart Glass. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script>
        // Initialize Lucide icons
        lucide.createIcons();

        // Add some interactivity
        document.addEventListener('DOMContentLoaded', function() {
            // Smooth scrolling for navigation links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth'
                        });
                    }
                });
            });

            // Add click handlers for buttons
            document.querySelectorAll('button').forEach(button => {
                button.addEventListener('click', function() {
                    // Simple demo alert
                    const action = this.textContent.trim();
                    alert(`Demo: ${action} functionality would be implemented here.`);
                });
            });
        });
    </script>
</body>
</html>
