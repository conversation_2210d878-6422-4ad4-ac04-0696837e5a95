{"name": "yocto-queue", "version": "0.1.0", "description": "Tiny queue data structure", "license": "MIT", "repository": "sindresorhus/yocto-queue", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "engines": {"node": ">=10"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["queue", "data", "structure", "algorithm", "queues", "queuing", "list", "array", "linkedlist", "fifo", "enqueue", "dequeue", "data-structure"], "devDependencies": {"ava": "^2.4.0", "tsd": "^0.13.1", "xo": "^0.35.0"}}