# Simple PowerShell script to run Beyond Smart Glass Track

Write-Host "Starting Beyond Smart Glass Track..." -ForegroundColor Green

# Check if Node.js is available
try {
    $nodeVersion = node --version 2>$null
    Write-Host "Node.js found: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "Node.js not found. Please install Node.js from https://nodejs.org" -ForegroundColor Red
    exit 1
}

# Check if npm is available
try {
    $npmVersion = npm --version 2>$null
    Write-Host "npm found: $npmVersion" -ForegroundColor Green
} catch {
    Write-Host "npm not found. Please install npm." -ForegroundColor Red
    exit 1
}

# Install dependencies if needed
if (-not (Test-Path "node_modules")) {
    Write-Host "Installing dependencies..." -ForegroundColor Yellow
    npm install
}

# Create env file if it doesn't exist
if (-not (Test-Path ".env.local")) {
    Write-Host "Creating .env.local file..." -ForegroundColor Yellow
    "VITE_SUPABASE_URL=your-supabase-url" | Out-File -FilePath ".env.local"
    "VITE_SUPABASE_ANON_KEY=your-supabase-anon-key" | Add-Content -Path ".env.local"
}

# Start the development server
Write-Host "Starting development server..." -ForegroundColor Green
npm run dev
