import React, { useState, useEffect, Suspense } from 'react';
import { Routes, Route, Navigate, useLocation, Outlet } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import LoginPage from '@/pages/LoginPage';
import Sidebar from '@/components/layout/Sidebar';
import Header from '@/components/layout/Header';
import { useTranslation } from 'react-i18next';
import { supabase } from '@/lib/supabase';

const DashboardPage = React.lazy(() => import('@/pages/SmartGlassDashboard'));
const QuotationsPage = React.lazy(() => import('@/pages/QuotationsPage'));
const ContractsPage = React.lazy(() => import('@/pages/ContractsPage'));
const ManufacturingPage = React.lazy(() => import('@/pages/ManufacturingPage'));
const InstallationsPage = React.lazy(() => import('@/pages/InstallationsPage'));
const SiteVisitsPage = React.lazy(() => import('@/pages/SiteVisitsPage'));
const InventoryPage = React.lazy(() => import('@/pages/InventoryPage'));
const WarrantiesPage = React.lazy(() => import('@/pages/WarrantiesPage'));
const SettingsPage = React.lazy(() => import('@/pages/SettingsPage'));
const UserManagementPage = React.lazy(() => import('@/pages/UserManagementPage'));
const BackupPage = React.lazy(() => import('@/pages/BackupPage'));
const ProfilePage = React.lazy(() => import('@/pages/ProfilePage'));


const App = () => {
  const [session, setSession] = useState(null);
  const [userRole, setUserRole] = useState(() => {
    const savedRole = localStorage.getItem('userRole');
    if (!savedRole) {
      localStorage.setItem('userRole', 'admin'); // تعيين admin كدور افتراضي
      return 'admin';
    }
    return savedRole;
  });
  const location = useLocation();
  const { i18n } = useTranslation();
  const [loadingSession, setLoadingSession] = useState(true);

  useEffect(() => {
    const fetchSessionAndListen = async () => {
      setLoadingSession(true);
      try {
        const { data: { session: currentSession }, error } = await supabase.auth.getSession();
        if (error) {
          console.error("Error getting session:", error.message);
        }
        setSession(currentSession);
        if (currentSession?.user?.user_metadata?.role) {
          const role = currentSession.user.user_metadata.role;
          setUserRole(role);
          localStorage.setItem('userRole', role);
        } else {
          localStorage.removeItem('userRole');
          setUserRole('');
        }
      } catch (e) {
        console.error("Exception in fetchSession:", e);
      } finally {
        setLoadingSession(false);
      }

      const { data: authListener } = supabase.auth.onAuthStateChange(
        (_event, currentSession) => {
          console.log('Auth state changed:', _event, currentSession);
          setLoadingSession(true);
          setSession(currentSession);
          if (currentSession?.user?.user_metadata?.role) {
            const role = currentSession.user.user_metadata.role;
            console.log('User role from metadata:', role);
            setUserRole(role);
            localStorage.setItem('userRole', role);
          } else if (currentSession?.user) {
            // إذا لم يكن هناك دور في metadata، استخدم دور افتراضي
            console.log('No role in metadata, using default role: sales');
            setUserRole('sales');
            localStorage.setItem('userRole', 'sales');
          } else {
            console.log('No session, clearing user role');
            localStorage.removeItem('userRole');
            setUserRole('');
          }
          setLoadingSession(false);
        }
      );
      return () => {
        authListener?.subscription?.unsubscribe();
      };
    };

    const unsubscribe = fetchSessionAndListen();

    return () => {
      if (typeof unsubscribe === 'function') {
        unsubscribe();
      } else {
        unsubscribe.then(cleanup => cleanup && cleanup());
      }
    };

  }, []);


  useEffect(() => {
    const storedLang = localStorage.getItem('i18nextLng') || 'en';
    if (i18n.language !== storedLang) {
      i18n.changeLanguage(storedLang);
    }
    document.documentElement.lang = storedLang;
    document.documentElement.dir = storedLang === 'ar' ? 'rtl' : 'ltr';
  }, [i18n, location]);

  const handleLogin = (roleFromLogin) => {
    console.log('handleLogin called with role:', roleFromLogin);
    if (roleFromLogin) {
        setUserRole(roleFromLogin);
        localStorage.setItem('userRole', roleFromLogin);
    } else {
        // استخدم دور افتراضي إذا لم يتم تمرير دور
        setUserRole('sales');
        localStorage.setItem('userRole', 'sales');
    }
  };

  const handleLogout = async () => {
    const { error } = await supabase.auth.signOut();
    if (error) {
      console.error("Error logging out:", error);
    }
  };

  const handleRoleChange = (newRole) => {
    setUserRole(newRole);
    localStorage.setItem('userRole', newRole);
  };

  const isAuthenticated = !!session;

  const pageVariants = {
    initial: { opacity: 0, x: i18n.dir() === 'rtl' ? -100 : 100 },
    in: { opacity: 1, x: 0 },
    out: { opacity: 0, x: i18n.dir() === 'rtl' ? 100 : -100 },
  };

  const pageTransition = {
    type: 'tween',
    ease: 'anticipate',
    duration: 0.5,
  };

  const PageLoader = () => (
    <div className="flex justify-center items-center h-full w-full fixed inset-0 bg-gray-100 dark:bg-gray-900 z-50">
      <div className="animate-spin rounded-full h-16 w-16 border-t-4 border-b-4 border-blue-500 dark:border-blue-300"></div>
    </div>
  );

  const ProtectedLayout = () => {
    if (loadingSession) return <PageLoader />;
    if (!isAuthenticated) return <Navigate to="/login" state={{ from: location }} replace />;

    return (
      <div className={`flex h-screen bg-gray-100 dark:bg-gray-900 ${i18n.language === 'ar' ? 'font-[Cairo]' : 'font-[Roboto]'}`}>
        <Sidebar userRole={userRole} onLogout={handleLogout} />
        <div className="flex-1 flex flex-col overflow-hidden min-w-0">
          <Header userRole={userRole} onRoleChange={handleRoleChange} />
          <main className="flex-1 overflow-x-hidden overflow-y-auto bg-gray-100 dark:bg-gray-900 p-2 sm:p-4 lg:p-6">
            <AnimatePresence mode="wait">
              <Suspense fallback={<PageLoader />}>
                 <Outlet />
              </Suspense>
            </AnimatePresence>
          </main>
        </div>
      </div>
    );
  };

  const ProtectedRoute = ({ children, allowedRoles }) => {
    if (loadingSession) return <PageLoader />; // Handled by ProtectedLayout, but keep for direct use if any
    if (!isAuthenticated) return <Navigate to="/login" state={{ from: location }} replace />;

    // التحقق من الصلاحيات
    if (allowedRoles && !allowedRoles.includes(userRole)) {
      return <Navigate to="/dashboard" state={{ from: location }} replace />;
    }

    return <motion.div initial="initial" animate="in" exit="out" variants={pageVariants} transition={pageTransition}>{children}</motion.div>;
  };

  if (loadingSession && location.pathname !== '/login' ) {
      return <PageLoader />;
  }


  return (
    <Routes>
      <Route
        path="/login"
        element={
          loadingSession ? <PageLoader /> :
          isAuthenticated ? <Navigate to="/dashboard" replace /> :
          <motion.div className="h-full" initial="initial" animate="in" exit="out" variants={pageVariants} transition={pageTransition}>
            <LoginPage onLogin={handleLogin} />
          </motion.div>
        }
      />

      <Route element={<ProtectedLayout />}>
        <Route path="/" element={<ProtectedRoute allowedRoles={['admin', 'sales', 'manufacturing', 'installation']}><Navigate to="/dashboard" replace /></ProtectedRoute>} />
        <Route path="/dashboard" element={<ProtectedRoute allowedRoles={['admin', 'sales', 'manufacturing', 'installation']}><DashboardPage /></ProtectedRoute>} />
        <Route path="/quotations" element={<ProtectedRoute allowedRoles={['admin', 'sales']}><QuotationsPage /></ProtectedRoute>} />
        <Route path="/contracts" element={<ProtectedRoute allowedRoles={['admin', 'sales']}><ContractsPage /></ProtectedRoute>} />
        <Route path="/manufacturing" element={<ProtectedRoute allowedRoles={['admin', 'manufacturing']}><ManufacturingPage /></ProtectedRoute>} />
        <Route path="/installations" element={<ProtectedRoute allowedRoles={['admin', 'installation']}><InstallationsPage /></ProtectedRoute>} />
        <Route path="/site-visits" element={<ProtectedRoute allowedRoles={['admin', 'sales']}><SiteVisitsPage /></ProtectedRoute>} />
        <Route path="/inventory" element={<ProtectedRoute allowedRoles={['admin', 'manufacturing']}><InventoryPage /></ProtectedRoute>} />
        <Route path="/warranties" element={<ProtectedRoute allowedRoles={['admin', 'installation']}><WarrantiesPage /></ProtectedRoute>} />
        <Route path="/settings" element={<ProtectedRoute allowedRoles={['admin']}><SettingsPage /></ProtectedRoute>} />
        <Route path="/user-management" element={<ProtectedRoute allowedRoles={['admin']}><UserManagementPage /></ProtectedRoute>} />
        <Route path="/backup" element={<ProtectedRoute allowedRoles={['admin']}><BackupPage /></ProtectedRoute>} />
        <Route path="/profile" element={<ProtectedRoute allowedRoles={['admin', 'sales', 'manufacturing', 'installation']}><ProfilePage /></ProtectedRoute>} />

      </Route>

      <Route path="*" element={
        loadingSession ? <PageLoader /> :
        isAuthenticated ? <Navigate to="/dashboard" replace /> : <Navigate to="/login" replace />
      } />
    </Routes>
  );
};

export default App;