import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import {
  FileText,
  Image as ImageIcon,
  Download,
  Eye,
  Trash2,
  CheckCircle,
  FolderOpen
} from 'lucide-react';
import { deleteFile } from '@/lib/fileService';

const FileDisplayManager = ({
  files = [],
  onFilesChange,
  showPreview = true,
  disabled = false,
  projectId
}) => {
  const { t } = useTranslation();
  const { toast } = useToast();

  // تنسيق حجم الملف
  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // معالجة حذف ملف
  const handleRemoveFile = async (fileId) => {
    const fileToRemove = files.find(f => f.id === fileId);
    if (!fileToRemove) return;

    try {
      // حذف من قاعدة البيانات و Supabase Storage
      const result = await deleteFile(fileId);

      if (result.success) {
        // إزالة من القائمة المحلية
        const updatedFiles = files.filter(f => f.id !== fileId);
        onFilesChange(updatedFiles);

        toast({
          title: "File Deleted",
          description: "File has been deleted successfully.",
          className: "bg-green-500 text-white dark:bg-green-600"
        });
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      console.error('Error removing file:', error);
      toast({
        title: "Error",
        description: "Failed to remove file",
        variant: "destructive"
      });
    }
  };

  // معالجة تحميل ملف
  const handleDownloadFile = (file) => {
    if (file.url) {
      const link = document.createElement('a');
      link.href = file.url;
      link.download = file.name;
      link.target = '_blank';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  // معالجة عرض ملف
  const handleViewFile = (file) => {
    if (file.url) {
      window.open(file.url, '_blank');
    }
  };

  if (!files || files.length === 0) {
    return (
      <div className="text-center py-8">
        <FolderOpen className="mx-auto h-12 w-12 text-gray-400" />
        <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
          {t('fileDisplay.noFiles') || "No files uploaded yet"}
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* عنوان الملفات */}
      <div className="flex items-center justify-between">
        <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100">
          {t('fileDisplay.uploadedFiles') || "Uploaded Files"} ({files.length})
        </h4>
      </div>

      {/* شبكة الملفات */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
        <AnimatePresence>
          {files.map((file) => (
            <motion.div
              key={file.id}
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              className="relative bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow"
            >
              {/* معاينة الملف */}
              {showPreview && file.type && file.type.startsWith('image/') && file.url && (
                <div className="aspect-video bg-gray-100 dark:bg-gray-700 rounded-md mb-3 overflow-hidden">
                  <img
                    src={file.url}
                    alt={file.name}
                    className="w-full h-full object-cover cursor-pointer hover:scale-105 transition-transform"
                    onClick={() => handleViewFile(file)}
                    onError={(e) => {
                      e.target.style.display = 'none';
                    }}
                  />
                </div>
              )}

              {/* معلومات الملف */}
              <div className="space-y-2">
                <div className="flex items-start justify-between">
                  <div className="flex items-center space-x-2 min-w-0 flex-1">
                    {file.type && file.type.startsWith('image/') ? (
                      <ImageIcon className="h-5 w-5 text-blue-500 flex-shrink-0" />
                    ) : (
                      <FileText className="h-5 w-5 text-green-500 flex-shrink-0" />
                    )}
                    <div className="min-w-0 flex-1">
                      <p className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                        {file.name}
                      </p>
                      <p className="text-xs text-gray-500">
                        {formatFileSize(file.size)}
                      </p>
                    </div>
                  </div>

                  {/* حالة الرفع */}
                  <div className="flex items-center ml-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  </div>
                </div>

                {/* تاريخ الرفع */}
                {file.uploadedAt && (
                  <p className="text-xs text-gray-400">
                    {t('fileDisplay.uploadedOn') || "Uploaded on"}: {new Date(file.uploadedAt).toLocaleDateString()}
                  </p>
                )}

                {/* أزرار الإجراءات */}
                <div className="flex items-center justify-between pt-2 border-t border-gray-100 dark:border-gray-700">
                  <div className="flex space-x-1">
                    <Button
                      size="sm"
                      variant="ghost"
                      className="h-8 w-8 p-0 text-blue-500 hover:text-blue-700"
                      onClick={() => handleViewFile(file)}
                      title={t('fileDisplay.viewFile') || "View file"}
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                    <Button
                      size="sm"
                      variant="ghost"
                      className="h-8 w-8 p-0 text-green-500 hover:text-green-700"
                      onClick={() => handleDownloadFile(file)}
                      title={t('fileDisplay.downloadFile') || "Download file"}
                    >
                      <Download className="h-4 w-4" />
                    </Button>
                  </div>

                  <Button
                    size="sm"
                    variant="ghost"
                    className="h-8 w-8 p-0 text-red-500 hover:text-red-700"
                    onClick={() => handleRemoveFile(file.id)}
                    disabled={disabled}
                    title={t('fileDisplay.deleteFile') || "Delete file"}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </motion.div>
          ))}
        </AnimatePresence>
      </div>
    </div>
  );
};

export default FileDisplayManager;
