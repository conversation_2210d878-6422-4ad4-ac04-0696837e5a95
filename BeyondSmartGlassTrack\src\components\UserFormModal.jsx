import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Users, Key, Phone, Building, MapPin, Shield, Eye, EyeOff, Loader2 } from 'lucide-react';

const UserFormModal = ({ isOpen, onClose, onSubmit, user, isEditing, t, i18n }) => {
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    confirm_password: '',
    role: 'technician',
    full_name: '',
    phone: '',
    department: '',
    notes: '',
    is_active: true,
    permissions: {
      dashboard: true,
      projects: { view: true, add: false, edit: false, delete: false },
      maintenance: { view: true, add: false, edit: false, delete: false },
      users: { view: false, add: false, edit: false, delete: false },
      reports: { view: true, export: false },
      backup: { view: false, create: false, restore: false }
    }
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showAdvanced, setShowAdvanced] = useState(false);

  const userRoles = [
    { value: 'manager', label: 'Manager', color: 'bg-purple-100 text-purple-800' },
    { value: 'supervisor', label: 'Supervisor', color: 'bg-blue-100 text-blue-800' },
    { value: 'data_entry', label: 'Data Entry', color: 'bg-green-100 text-green-800' },
    { value: 'technician', label: 'Technician', color: 'bg-orange-100 text-orange-800' }
  ];

  const departments = [
    'Management',
    'Projects',
    'Maintenance',
    'Data Entry',
    'Sales',
    'Quality Control'
  ];

  useEffect(() => {
    if (isEditing && user) {
      setFormData({
        email: user.email || '',
        role: user.role || 'technician',
        full_name: user.name || '',
        phone: user.phone || '',
        department: user.department || '',
        notes: user.notes || '',
        is_active: user.is_active !== undefined ? user.is_active : true,
        permissions: user.permissions || {
          dashboard: true,
          projects: { view: true, add: false, edit: false, delete: false },
          maintenance: { view: true, add: false, edit: false, delete: false },
          users: { view: false, add: false, edit: false, delete: false },
          reports: { view: true, export: false },
          backup: { view: false, create: false, restore: false }
        }
      });
    } else {
      setFormData({
        email: '',
        password: '',
        confirm_password: '',
        role: 'technician',
        full_name: '',
        phone: '',
        department: '',
        notes: '',
        is_active: true,
        permissions: {
          dashboard: true,
          projects: { view: true, add: false, edit: false, delete: false },
          maintenance: { view: true, add: false, edit: false, delete: false },
          users: { view: false, add: false, edit: false, delete: false },
          reports: { view: true, export: false },
          backup: { view: false, create: false, restore: false }
        }
      });
    }
  }, [isEditing, user, isOpen]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSwitchChange = (name, checked) => {
    setFormData(prev => ({ ...prev, [name]: checked }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      if (!isEditing && formData.password !== formData.confirm_password) {
        alert('Passwords do not match. Please check and try again.');
        setIsSubmitting(false);
        return;
      }

      if (!isEditing && formData.password.length < 6) {
        alert('Password must be at least 6 characters long.');
        setIsSubmitting(false);
        return;
      }

      await onSubmit(formData);
    } catch (error) {
      console.error('Error submitting form:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-[99999] flex items-center justify-center p-4" style={{ zIndex: 99999 }}>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="absolute inset-0 bg-black/60 backdrop-blur-sm"
        onClick={onClose}
      />

      <motion.div
        initial={{ scale: 0.9, opacity: 0, y: 20 }}
        animate={{ scale: 1, opacity: 1, y: 0 }}
        exit={{ scale: 0.9, opacity: 0, y: 20 }}
        className="relative bg-white dark:bg-gray-800 rounded-lg shadow-2xl w-full max-w-4xl max-h-[95vh] overflow-hidden"
        onClick={e => e.stopPropagation()}
      >
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-2xl font-semibold text-gray-800 dark:text-white">
            {isEditing ? "Edit User" : "Add New User"}
          </h3>
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Label htmlFor="is_active" className="text-sm font-medium">Active</Label>
              <Switch
                id="is_active"
                checked={formData.is_active}
                onCheckedChange={(checked) => handleSwitchChange('is_active', checked)}
              />
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        <div className="overflow-y-auto max-h-[calc(95vh-140px)] p-6">
          <form id="user-form" onSubmit={handleSubmit} className="space-y-6">
            <Card className="border-gray-200 dark:border-gray-700">
              <CardHeader className="pb-3">
                <CardTitle className="text-lg flex items-center">
                  <Users className="h-5 w-5 mr-2 text-blue-500" />
                  Basic Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="full_name" className="flex items-center">
                      <Users className="h-4 w-4 mr-1" />
                      Full Name
                    </Label>
                    <Input
                      id="full_name"
                      name="full_name"
                      value={formData.full_name}
                      onChange={handleInputChange}
                      className="mt-1 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                      placeholder="Enter full name"
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="email" className="flex items-center">
                      <Key className="h-4 w-4 mr-1" />
                      Email Address
                    </Label>
                    <Input
                      id="email"
                      name="email"
                      type="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      required
                      className="mt-1 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                      placeholder="<EMAIL>"
                      disabled={isEditing}
                    />
                  </div>
                </div>

                {!isEditing && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="password" className="flex items-center">
                        <Key className="h-4 w-4 mr-1" />
                        Password
                      </Label>
                      <Input
                        id="password"
                        name="password"
                        type="password"
                        value={formData.password || ''}
                        onChange={handleInputChange}
                        className="mt-1 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                        placeholder="Enter password (min 6 characters)"
                        required={!isEditing}
                        minLength={6}
                      />
                    </div>
                    <div>
                      <Label htmlFor="confirm_password" className="flex items-center">
                        <Key className="h-4 w-4 mr-1" />
                        Confirm Password
                      </Label>
                      <Input
                        id="confirm_password"
                        name="confirm_password"
                        type="password"
                        value={formData.confirm_password || ''}
                        onChange={handleInputChange}
                        className="mt-1 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                        placeholder="Confirm password"
                        required={!isEditing}
                        minLength={6}
                      />
                    </div>
                  </div>
                )}

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="phone" className="flex items-center">
                      <Phone className="h-4 w-4 mr-1" />
                      Phone Number
                    </Label>
                    <Input
                      id="phone"
                      name="phone"
                      type="tel"
                      value={formData.phone}
                      onChange={handleInputChange}
                      className="mt-1 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                      placeholder="+966 50 123 4567"
                    />
                  </div>
                  <div>
                    <Label htmlFor="department" className="flex items-center">
                      <Building className="h-4 w-4 mr-1" />
                      Department
                    </Label>
                    <select
                      id="department"
                      name="department"
                      value={formData.department}
                      onChange={handleInputChange}
                      className="mt-1 block w-full p-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm h-10"
                    >
                      <option value="">Select Department</option>
                      {departments.map(dept => (
                        <option key={dept} value={dept}>{dept}</option>
                      ))}
                    </select>
                  </div>
                </div>

                <div>
                  <Label htmlFor="role" className="flex items-center">
                    <Shield className="h-4 w-4 mr-1" />
                    Role
                  </Label>
                  <select
                    id="role"
                    name="role"
                    value={formData.role}
                    onChange={handleInputChange}
                    className="mt-1 block w-full p-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm h-10"
                  >
                    {userRoles.map(role => (
                      <option key={role.value} value={role.value}>{role.label}</option>
                    ))}
                  </select>
                  <div className="mt-2">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${userRoles.find(r => r.value === formData.role)?.color || 'bg-gray-100 text-gray-800'}`}>
                      {userRoles.find(r => r.value === formData.role)?.label}
                    </span>
                  </div>
                </div>

                <div>
                  <Label htmlFor="notes" className="flex items-center">
                    <MapPin className="h-4 w-4 mr-1" />
                    Notes
                  </Label>
                  <Textarea
                    id="notes"
                    name="notes"
                    value={formData.notes}
                    onChange={handleInputChange}
                    className="mt-1 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                    placeholder="Any additional notes about the user"
                    rows={3}
                  />
                </div>
              </CardContent>
            </Card>
          </form>
        </div>

        <div className="flex justify-end space-x-3 rtl:space-x-reverse p-6 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700/30">
          <Button type="button" variant="ghost" onClick={onClose} className="dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700">
            Cancel
          </Button>
          <Button
            type="submit"
            form="user-form"
            className="bg-blue-600 hover:bg-blue-700 text-white min-w-[120px]"
            disabled={isSubmitting}
          >
            {isSubmitting ? <Loader2 className="h-5 w-5 animate-spin" /> : (isEditing ? "Save Changes" : "Add User")}
          </Button>
        </div>
      </motion.div>
    </div>
  );
};

export default UserFormModal;
