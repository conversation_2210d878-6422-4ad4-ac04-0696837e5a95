
import React from 'react';
import { motion } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

const ProjectBasicDetailsForm = ({ formData, handleChange, itemVariants }) => {
  const { t } = useTranslation();

  return (
    <>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
        <motion.div variants={itemVariants}>
          <Label htmlFor="project_number" className="text-gray-700 dark:text-gray-300">{t('addProject.projectNumber')}</Label>
          <Input id="project_number" name="project_number" value={formData.project_number} onChange={handleChange} required className="mt-1 dark:bg-gray-700 dark:border-gray-600 dark:text-white focus:ring-blue-500 dark:focus:ring-blue-400" />
        </motion.div>
        <motion.div variants={itemVariants}>
          <Label htmlFor="client_name" className="text-gray-700 dark:text-gray-300">{t('addProject.clientName')}</Label>
          <Input id="client_name" name="client_name" value={formData.client_name} onChange={handleChange} required className="mt-1 dark:bg-gray-700 dark:border-gray-600 dark:text-white focus:ring-blue-500 dark:focus:ring-blue-400" />
        </motion.div>
        <motion.div variants={itemVariants}>
          <Label htmlFor="city" className="text-gray-700 dark:text-gray-300">{t('addProject.city')}</Label>
          <Input id="city" name="city" value={formData.city} onChange={handleChange} required className="mt-1 dark:bg-gray-700 dark:border-gray-600 dark:text-white focus:ring-blue-500 dark:focus:ring-blue-400" />
        </motion.div>
        <motion.div variants={itemVariants}>
          <Label htmlFor="start_date" className="text-gray-700 dark:text-gray-300">{t('addProject.startDate')}</Label>
          <Input type="date" id="start_date" name="start_date" value={formData.start_date} onChange={handleChange} required className="mt-1 dark:bg-gray-700 dark:border-gray-600 dark:text-white focus:ring-blue-500 dark:focus:ring-blue-400" />
        </motion.div>
        <motion.div variants={itemVariants}>
          <Label htmlFor="duration_days" className="text-gray-700 dark:text-gray-300">{t('addProject.durationDays')}</Label>
          <Input type="number" id="duration_days" name="duration_days" value={formData.duration_days} onChange={handleChange} className="mt-1 dark:bg-gray-700 dark:border-gray-600 dark:text-white focus:ring-blue-500 dark:focus:ring-blue-400" />
        </motion.div>
        <motion.div variants={itemVariants}>
          <Label htmlFor="status" className="text-gray-700 dark:text-gray-300">{t('allProjects.status')}</Label>
          <select id="status" name="status" value={formData.status} onChange={handleChange} className="mt-1 block w-full p-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm h-10">
            <option value="Not Started">{t('dashboard.notStarted')}</option>
            <option value="In Progress">{t('dashboard.inProgress')}</option>
            <option value="Delayed">{t('dashboard.delayed')}</option>
            <option value="Completed">{t('dashboard.completed')}</option>
          </select>
        </motion.div>
      </div>
    </>
  );
};

export default ProjectBasicDetailsForm;
