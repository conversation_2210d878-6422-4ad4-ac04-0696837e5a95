import React from 'react';
import { motion } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { UploadCloud, FileText, Image as ImageIcon, X } from 'lucide-react';

const ProjectFilesUploadForm = ({ files, handleFileChange, removeFile, itemVariants }) => {
  const { t, i18n } = useTranslation();

  const getFileSize = (size) => {
    if (size < 1024) return `${size} bytes`;
    if (size < 1024 * 1024) return `${(size / 1024).toFixed(2)} KB`;
    return `${(size / (1024 * 1024)).toFixed(2)} MB`;
  };

  return (
    <motion.div variants={itemVariants}>
      <Label htmlFor="files" className="text-gray-700 dark:text-gray-300">{t('addProject.uploadFiles')}</Label>
      <div className="mt-1 flex flex-col items-center justify-center px-6 pt-5 pb-6 border-2 border-gray-300 dark:border-gray-600 border-dashed rounded-md hover:border-blue-500 dark:hover:border-blue-400 transition-colors">
        <UploadCloud className="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" />
        <div className="flex text-sm text-gray-600 dark:text-gray-400 mt-2">
          <label
            htmlFor="file-upload"
            className="relative cursor-pointer bg-white dark:bg-gray-700 rounded-md font-medium text-blue-600 dark:text-blue-400 hover:text-blue-500 dark:hover:text-blue-300 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500 px-2 py-1"
          >
            <span>{t('addProject.selectFilesButton')}</span>
            <input id="file-upload" name="files" type="file" className="sr-only" multiple onChange={handleFileChange} accept=".pdf,.jpg,.jpeg,.png" />
          </label>
          <p className={`ps-1 ${i18n.language === 'ar' ? 'pr-1' : 'pl-1'}`}>{t('addProject.dragAndDrop')}</p>
        </div>
        <p className="text-xs text-gray-500 dark:text-gray-500 mt-1">
          {t('addProject.fileTypes')}
        </p>
      </div>
      {files.length > 0 && (
        <div className="mt-3 space-y-2">
          <p className="text-sm font-medium text-gray-700 dark:text-gray-300">{t('addProject.selectedFiles', { count: files.length })}</p>
          <ul className="space-y-1">
            {files.map((file, index) => (
              <li key={`${file.name}-${index}`} className="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-700/50 p-2 rounded-md">
                <div className="flex items-center min-w-0">
                  {file.type.startsWith('image/') ? <ImageIcon className="h-4 w-4 mr-2 rtl:ml-2 rtl:mr-0 text-blue-500 flex-shrink-0" /> : <FileText className="h-4 w-4 mr-2 rtl:ml-2 rtl:mr-0 text-green-500 flex-shrink-0" />}
                  <span className="truncate" title={file.name}>{file.name}</span>
                  <span className="text-xs text-gray-500 dark:text-gray-400 mx-2 flex-shrink-0">({getFileSize(file.size)})</span>
                </div>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="text-red-500 hover:text-red-700 dark:hover:text-red-400 p-1 h-auto"
                  onClick={() => removeFile(index)}
                  aria-label={t('addProject.removeFile')}
                >
                  <X className="h-4 w-4" />
                </Button>
              </li>
            ))}
          </ul>
        </div>
      )}
    </motion.div>
  );
};

export default ProjectFilesUploadForm;