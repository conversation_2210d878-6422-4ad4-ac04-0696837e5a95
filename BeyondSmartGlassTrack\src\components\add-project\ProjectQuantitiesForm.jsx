
import React from 'react';
import { motion } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

const ProjectQuantitiesForm = ({ formData, handleChange, itemVariants }) => {
  const { t } = useTranslation();
  const quantityItems = [
    { name: 'pvc', step: '1', unit: 'units' },
    { name: 'shutter', step: '1', unit: 'units' },
    { name: 'sgs', step: '1', unit: 'units' },
    { name: 'doors', step: '1', unit: 'units' },
    { name: 'glass', step: '1', unit: 'units' }
  ];

  return (
    <>
      <motion.h3 variants={itemVariants} className="text-lg font-semibold text-gray-700 dark:text-gray-300 pt-4 border-t dark:border-gray-700/50">{t('addProject.quantities')}</motion.h3>
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-x-6 gap-y-4">
        {quantityItems.map(item => (
          <motion.div variants={itemVariants} key={item.name}>
            <Label htmlFor={`quantity_${item.name}`} className="text-gray-700 dark:text-gray-300">{t(`addProject.quantity_${item.name}`) || `${item.name.toUpperCase()} (${item.unit})`}</Label>
            <Input
              type="number"
              step={item.step}
              min="0"
              id={`quantity_${item.name}`}
              name={`quantity_${item.name}`}
              value={formData[`quantity_${item.name}`]}
              onChange={handleChange}
              className="mt-1 dark:bg-gray-700 dark:border-gray-600 dark:text-white focus:ring-blue-500 dark:focus:ring-blue-400"
            />
          </motion.div>
        ))}
      </div>
    </>
  );
};

export default ProjectQuantitiesForm;
