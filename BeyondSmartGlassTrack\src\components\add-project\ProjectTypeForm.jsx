
import React from 'react';
import { motion } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';

const ProjectTypeForm = ({ formData, handleCheckboxChange, itemVariants }) => {
  const { t } = useTranslation();
  const projectTypes = [
    { id: 'project_type_new', value: 'New Installation', labelKey: 'addProject.newInstallation' },
    { id: 'project_type_maintenance', value: 'Maintenance', labelKey: 'addProject.maintenance' }
  ];

  return (
    <>
      <motion.h3 variants={itemVariants} className="text-lg font-semibold text-gray-700 dark:text-gray-300 pt-4 border-t dark:border-gray-700/50">{t('addProject.projectType')}</motion.h3>
      <motion.div variants={itemVariants} className="flex flex-wrap gap-4">
        {projectTypes.map(type => (
          <div key={type.id} className="flex items-center space-x-2 rtl:space-x-reverse">
            <Checkbox
              id={type.id}
              checked={formData.project_type.includes(type.value)}
              onCheckedChange={() => handleCheckboxChange(type.value)}
              className="data-[state=checked]:bg-blue-600 dark:data-[state=checked]:bg-blue-500 border-gray-400 dark:border-gray-500"
            />
            <Label htmlFor={type.id} className="text-gray-700 dark:text-gray-300 font-normal">{t(type.labelKey)}</Label>
          </div>
        ))}
      </motion.div>
    </>
  );
};

export default ProjectTypeForm;
