import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useToast } from '@/components/ui/use-toast';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { FileText, Save, X, Search } from 'lucide-react';
import {
  addContract,
  updateContract,
  generateContractNumber,
  convertQuotationToContract,
  validateContractData
} from '@/lib/contractsService';
import { getAllQuotations, getQuotationsByStatus } from '@/lib/quotationsService';

const ContractModal = ({ isOpen, onClose, contract, onSave }) => {
  const { t } = useTranslation();
  const { toast } = useToast();
  
  const [formData, setFormData] = useState({
    contract_number: '',
    quotation_id: '',
    client_approval_date: '',
    signed_date: '',
    start_date: '',
    expected_completion: '',
    actual_completion: '',
    contract_value: 0,
    paid_amount: 0,
    remaining_amount: 0,
    payment_status: 'unpaid',
    contract_status: 'signed',
    notes: ''
  });
  
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});
  const [availableQuotations, setAvailableQuotations] = useState([]);
  const [selectedQuotation, setSelectedQuotation] = useState(null);
  const [showQuotationSelector, setShowQuotationSelector] = useState(false);

  useEffect(() => {
    if (isOpen) {
      if (contract) {
        // Edit mode
        setFormData({
          ...contract,
          client_approval_date: contract.client_approval_date || '',
          signed_date: contract.signed_date || '',
          start_date: contract.start_date || '',
          expected_completion: contract.expected_completion || '',
          actual_completion: contract.actual_completion || ''
        });
        setSelectedQuotation(contract.quotations);
      } else {
        // Add mode
        resetForm();
        generateNewContractNumber();
        loadAvailableQuotations();
      }
    }
  }, [isOpen, contract]);

  useEffect(() => {
    // Update contract value and remaining amount when quotation is selected
    if (selectedQuotation && !contract) {
      setFormData(prev => ({
        ...prev,
        contract_value: selectedQuotation.final_price,
        remaining_amount: selectedQuotation.final_price
      }));
    }
  }, [selectedQuotation, contract]);

  useEffect(() => {
    // Calculate remaining amount when paid amount changes
    const contractValue = parseFloat(formData.contract_value) || 0;
    const paidAmount = parseFloat(formData.paid_amount) || 0;
    const remaining = contractValue - paidAmount;
    
    let paymentStatus = 'unpaid';
    if (paidAmount >= contractValue && contractValue > 0) {
      paymentStatus = 'paid';
    } else if (paidAmount > 0) {
      paymentStatus = 'partial';
    }

    setFormData(prev => ({
      ...prev,
      remaining_amount: remaining,
      payment_status: paymentStatus
    }));
  }, [formData.contract_value, formData.paid_amount]);

  const resetForm = () => {
    setFormData({
      contract_number: '',
      quotation_id: '',
      client_approval_date: '',
      signed_date: '',
      start_date: '',
      expected_completion: '',
      actual_completion: '',
      contract_value: 0,
      paid_amount: 0,
      remaining_amount: 0,
      payment_status: 'unpaid',
      contract_status: 'signed',
      notes: ''
    });
    setErrors({});
    setSelectedQuotation(null);
    setShowQuotationSelector(false);
  };

  const generateNewContractNumber = async () => {
    try {
      const contractNumber = await generateContractNumber();
      setFormData(prev => ({ ...prev, contract_number: contractNumber }));
    } catch (error) {
      console.error('Error generating contract number:', error);
    }
  };

  const loadAvailableQuotations = async () => {
    try {
      const quotations = await getQuotationsByStatus('approved');
      setAvailableQuotations(quotations);
    } catch (error) {
      console.error('Error loading quotations:', error);
    }
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error for this field
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: null }));
    }
  };

  const handleQuotationSelect = (quotation) => {
    setSelectedQuotation(quotation);
    setFormData(prev => ({
      ...prev,
      quotation_id: quotation.id,
      contract_value: quotation.final_price,
      remaining_amount: quotation.final_price
    }));
    setShowQuotationSelector(false);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // Validate form data
    const validation = validateContractData(formData);

    if (!validation.isValid) {
      const errorObj = {};
      validation.errors.forEach(error => {
        if (error.includes('Quotation ID')) errorObj.quotation_id = error;
        if (error.includes('approval date')) errorObj.client_approval_date = error;
        if (error.includes('Signed date')) errorObj.signed_date = error;
        if (error.includes('completion date')) errorObj.expected_completion = error;
      });
      setErrors(errorObj);
      return;
    }

    setLoading(true);
    
    try {
      const contractData = {
        ...formData,
        contract_value: parseFloat(formData.contract_value),
        paid_amount: parseFloat(formData.paid_amount) || 0,
        remaining_amount: parseFloat(formData.remaining_amount)
      };

      if (contract) {
        await updateContract(contract.id, contractData);
        toast({
          title: t('common.success'),
          description: 'Contract updated successfully',
          className: 'bg-green-500 text-white'
        });
      } else {
        if (selectedQuotation) {
          await convertQuotationToContract(selectedQuotation.id, contractData);
        } else {
          await addContract(contractData);
        }
        toast({
          title: t('common.success'),
          description: 'Contract created successfully',
          className: 'bg-green-500 text-white'
        });
      }

      onSave();
      onClose();
    } catch (error) {
      console.error('Error saving contract:', error);
      toast({
        title: t('common.error'),
        description: 'Failed to save contract',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText size={24} />
            {contract ? 'Edit Contract' : 'Create Contract'}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle>Contract Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="contract_number">Contract Number</Label>
                  <Input
                    id="contract_number"
                    value={formData.contract_number}
                    onChange={(e) => handleInputChange('contract_number', e.target.value)}
                    disabled
                    className="bg-gray-50"
                  />
                </div>
                <div>
                  <Label htmlFor="contract_status">Contract Status</Label>
                  <select
                    id="contract_status"
                    value={formData.contract_status}
                    onChange={(e) => handleInputChange('contract_status', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="signed">Signed</option>
                    <option value="in_progress">In Progress</option>
                    <option value="completed">Completed</option>
                    <option value="cancelled">Cancelled</option>
                  </select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Quotation Selection (only for new contracts) */}
          {!contract && (
            <Card>
              <CardHeader>
                <CardTitle>Quotation Selection</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {selectedQuotation ? (
                  <div className="p-4 bg-blue-50 rounded-lg">
                    <div className="flex justify-between items-start">
                      <div>
                        <h4 className="font-semibold">{selectedQuotation.quotation_number}</h4>
                        <p className="text-sm text-gray-600">Client: {selectedQuotation.client_name}</p>
                        <p className="text-sm text-gray-600">Item: {selectedQuotation.item_type}</p>
                        <p className="text-sm text-gray-600">Value: {formatCurrency(selectedQuotation.final_price)}</p>
                      </div>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => setShowQuotationSelector(true)}
                      >
                        Change
                      </Button>
                    </div>
                  </div>
                ) : (
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setShowQuotationSelector(true)}
                    className="w-full"
                  >
                    <Search size={16} className="mr-2" />
                    Select Quotation
                  </Button>
                )}

                {showQuotationSelector && (
                  <div className="border rounded-lg p-4 max-h-60 overflow-y-auto">
                    <h5 className="font-medium mb-3">Available Approved Quotations:</h5>
                    {availableQuotations.length === 0 ? (
                      <p className="text-gray-500 text-center py-4">No approved quotations available</p>
                    ) : (
                      <div className="space-y-2">
                        {availableQuotations.map(quotation => (
                          <div
                            key={quotation.id}
                            className="p-3 border rounded cursor-pointer hover:bg-gray-50"
                            onClick={() => handleQuotationSelect(quotation)}
                          >
                            <div className="flex justify-between items-center">
                              <div>
                                <p className="font-medium">{quotation.quotation_number}</p>
                                <p className="text-sm text-gray-600">{quotation.client_name} - {quotation.item_type}</p>
                              </div>
                              <p className="font-semibold text-green-600">
                                {formatCurrency(quotation.final_price)}
                              </p>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Dates */}
          <Card>
            <CardHeader>
              <CardTitle>Important Dates</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="client_approval_date">Client Approval Date *</Label>
                  <Input
                    id="client_approval_date"
                    type="date"
                    value={formData.client_approval_date}
                    onChange={(e) => handleInputChange('client_approval_date', e.target.value)}
                    className={errors.client_approval_date ? 'border-red-500' : ''}
                  />
                  {errors.client_approval_date && (
                    <p className="text-red-500 text-sm mt-1">{errors.client_approval_date}</p>
                  )}
                </div>
                <div>
                  <Label htmlFor="signed_date">Signed Date *</Label>
                  <Input
                    id="signed_date"
                    type="date"
                    value={formData.signed_date}
                    onChange={(e) => handleInputChange('signed_date', e.target.value)}
                    className={errors.signed_date ? 'border-red-500' : ''}
                  />
                  {errors.signed_date && (
                    <p className="text-red-500 text-sm mt-1">{errors.signed_date}</p>
                  )}
                </div>
                <div>
                  <Label htmlFor="start_date">Start Date</Label>
                  <Input
                    id="start_date"
                    type="date"
                    value={formData.start_date}
                    onChange={(e) => handleInputChange('start_date', e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="expected_completion">Expected Completion</Label>
                  <Input
                    id="expected_completion"
                    type="date"
                    value={formData.expected_completion}
                    onChange={(e) => handleInputChange('expected_completion', e.target.value)}
                    className={errors.expected_completion ? 'border-red-500' : ''}
                  />
                  {errors.expected_completion && (
                    <p className="text-red-500 text-sm mt-1">{errors.expected_completion}</p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Financial Information */}
          <Card>
            <CardHeader>
              <CardTitle>Financial Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="contract_value">Contract Value (SAR)</Label>
                  <Input
                    id="contract_value"
                    type="number"
                    step="0.01"
                    value={formData.contract_value}
                    onChange={(e) => handleInputChange('contract_value', e.target.value)}
                    disabled={!!selectedQuotation}
                    className={selectedQuotation ? 'bg-gray-50' : ''}
                  />
                </div>
                <div>
                  <Label htmlFor="paid_amount">Paid Amount (SAR)</Label>
                  <Input
                    id="paid_amount"
                    type="number"
                    step="0.01"
                    value={formData.paid_amount}
                    onChange={(e) => handleInputChange('paid_amount', e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="remaining_amount">Remaining Amount (SAR)</Label>
                  <Input
                    id="remaining_amount"
                    type="number"
                    step="0.01"
                    value={formData.remaining_amount}
                    disabled
                    className="bg-gray-50"
                  />
                </div>
              </div>
              
              <div>
                <Label htmlFor="payment_status">Payment Status</Label>
                <Input
                  id="payment_status"
                  value={formData.payment_status}
                  disabled
                  className="bg-gray-50 capitalize"
                />
              </div>
            </CardContent>
          </Card>

          {/* Notes */}
          <Card>
            <CardHeader>
              <CardTitle>Additional Notes</CardTitle>
            </CardHeader>
            <CardContent>
              <Textarea
                placeholder="Enter any additional notes or terms..."
                value={formData.notes}
                onChange={(e) => handleInputChange('notes', e.target.value)}
                rows={3}
              />
            </CardContent>
          </Card>

          {/* Actions */}
          <div className="flex justify-end gap-3">
            <Button type="button" variant="outline" onClick={onClose}>
              <X size={16} className="mr-2" />
              {t('common.cancel')}
            </Button>
            <Button type="submit" disabled={loading} className="bg-blue-600 hover:bg-blue-700">
              <Save size={16} className="mr-2" />
              {loading ? 'Saving...' : (contract ? t('common.update') : t('common.save'))}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default ContractModal;
