import React, { useEffect } from 'react';
import { NavLink } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  Home,
  FileText,
  FileCheck,
  Factory,
  Wrench,
  Calendar,
  Package,
  Shield,
  Settings,
  Users,
  LogOut,
  Building2,
  ChevronLeft,
  ChevronRight,
  Database,
  User
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useTranslation } from 'react-i18next';

const Sidebar = ({ userRole, onLogout }) => {
  const { t, i18n } = useTranslation();

  const handleLogoutClick = () => {
    onLogout();
    // Navigation to /login will be handled by App.jsx's ProtectedRoute logic
    // after session state changes.
  };

  const navItems = [
    { name: t('sidebar.dashboard'), path: '/dashboard', icon: <Home size={20} />, roles: ['admin', 'sales', 'manufacturing', 'installation'] },
    { name: t('sidebar.quotations'), path: '/quotations', icon: <FileText size={20} />, roles: ['admin', 'sales'] },
    { name: t('sidebar.contracts'), path: '/contracts', icon: <FileCheck size={20} />, roles: ['admin', 'sales'] },
    { name: t('sidebar.manufacturing'), path: '/manufacturing', icon: <Factory size={20} />, roles: ['admin', 'manufacturing'] },
    { name: t('sidebar.installations'), path: '/installations', icon: <Wrench size={20} />, roles: ['admin', 'installation'] },
    { name: t('sidebar.siteVisits'), path: '/site-visits', icon: <Calendar size={20} />, roles: ['admin', 'sales'] },
    { name: t('sidebar.inventory'), path: '/inventory', icon: <Package size={20} />, roles: ['admin', 'manufacturing'] },
    { name: t('sidebar.warranties'), path: '/warranties', icon: <Shield size={20} />, roles: ['admin', 'installation'] },
    { name: t('sidebar.settings'), path: '/settings', icon: <Settings size={20} />, roles: ['admin'] },
    { name: t('sidebar.userManagement'), path: '/user-management', icon: <Users size={20} />, roles: ['admin'] },
    { name: t('sidebar.backup'), path: '/backup', icon: <Database size={20} />, roles: ['admin'] },
    { name: t('sidebar.profile'), path: '/profile', icon: <User size={20} />, roles: ['admin', 'sales', 'manufacturing', 'installation'] },
  ];

  // فلترة العناصر بناءً على دور المستخدم
  const filteredNavItems = navItems.filter(item =>
    item.roles.includes(userRole) || userRole === 'admin'
  );

  const sidebarVariants = {
    open: { width: i18n.language === 'ar' ? 280 : 260, transition: { type: 'spring', stiffness: 300, damping: 30 } },
    closed: { width: i18n.language === 'ar' ? 80 : 70, transition: { type: 'spring', stiffness: 300, damping: 30 } },
  };

  // تحديد الحالة الافتراضية بناءً على حجم الشاشة
  const getInitialSidebarState = () => {
    if (typeof window !== 'undefined') {
      return window.innerWidth >= 1024; // مفتوح للشاشات الكبيرة (lg وأكبر)
    }
    return true;
  };

  const [isOpen, setIsOpen] = React.useState(getInitialSidebarState);

  // مراقبة تغيير حجم الشاشة
  useEffect(() => {
    const handleResize = () => {
      const isLargeScreen = window.innerWidth >= 1024;
      if (!isLargeScreen && isOpen) {
        setIsOpen(false); // طي الشريط الجانبي في الشاشات الصغيرة والمتوسطة
      } else if (isLargeScreen && !isOpen) {
        setIsOpen(true); // فتح الشريط الجانبي في الشاشات الكبيرة
      }
    };

    window.addEventListener('resize', handleResize);

    // تحقق من الحالة الحالية عند التحميل
    handleResize();

    return () => window.removeEventListener('resize', handleResize);
  }, [isOpen]);

  return (
    <motion.div
      animate={isOpen ? "open" : "closed"}
      variants={sidebarVariants}
      className={`bg-gradient-to-b from-blue-600 to-blue-800 dark:from-blue-700 dark:to-blue-900 text-white h-full flex flex-col shadow-lg relative ${i18n.language === 'ar' ? 'border-l' : 'border-r'} border-gray-200 dark:border-gray-700`}
      dir={i18n.dir()}
    >
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={`absolute top-4 ${i18n.language === 'ar' ? 'left-4' : 'right-4'} p-1 bg-white/20 hover:bg-white/30 rounded-full z-20 text-white`}
        aria-label={isOpen ? (t('sidebar.closeSidebar') || "Close sidebar") : (t('sidebar.openSidebar') || "Open sidebar")}
      >
        {isOpen ? (i18n.language === 'ar' ? <ChevronRight size={20} /> : <ChevronLeft size={20} />) : (i18n.language === 'ar' ? <ChevronLeft size={20} /> : <ChevronRight size={20} />)}
      </button>
      <div className={`flex items-center justify-center p-6 mt-2 mb-4 ${isOpen ? '' : 'px-3'}`}>
        <Building2 size={isOpen ? 40 : 30} className="text-yellow-400" />
        {isOpen && <h1 className="text-3xl font-bold ml-3 gradient-text">{t('appName')}</h1>}
      </div>
      <nav className="flex-1 px-4 space-y-2">
        {filteredNavItems.map((item) => (
          <NavLink
            key={item.name}
            to={item.path}
            className={({ isActive }) =>
              `flex items-center py-3 px-4 rounded-lg transition-all duration-200 ease-in-out hover:bg-blue-500 dark:hover:bg-blue-600 hover:shadow-md
              ${isActive ? 'bg-blue-700 dark:bg-blue-800 shadow-lg scale-105' : 'hover:translate-x-1'}
              ${!isOpen ? 'justify-center' : ''}`
            }
          >
            <span className={`text-yellow-400 ${isOpen && (i18n.language === 'ar' ? 'ml-3' : 'mr-3')}`}>{item.icon}</span>
            {isOpen && <span className="text-base font-medium">{item.name}</span>}
          </NavLink>
        ))}
      </nav>
      <div className={`p-4 mt-auto ${!isOpen ? 'px-2' : ''}`}>
        <Button
          variant="ghost"
          className={`w-full flex items-center justify-start text-white hover:bg-red-500 dark:hover:bg-red-600 hover:text-white ${!isOpen ? 'justify-center' : ''}`}
          onClick={handleLogoutClick}
        >
          <LogOut size={20} className={`text-red-300 ${isOpen && (i18n.language === 'ar' ? 'ml-3' : 'mr-3')}`} />
          {isOpen && <span className="text-base font-medium">{t('sidebar.logout')}</span>}
        </Button>
      </div>
    </motion.div>
  );
};

export default Sidebar;