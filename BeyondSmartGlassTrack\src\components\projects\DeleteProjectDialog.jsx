import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { AlertTriangle } from 'lucide-react';

const DeleteProjectDialog = ({ isOpen, onClose, onConfirm, projectToDelete }) => {
  const { t } = useTranslation();

  if (!isOpen) return null;

  return (
    <AlertDialog open={isOpen} onOpenChange={onClose}>
      <AlertDialogContent className="dark:bg-gray-800 dark:text-white">
        <AlertDialogHeader>
          <AlertDialogTitle className="flex items-center">
            <AlertTriangle className="text-red-500 mr-2 rtl:ml-2 rtl:mr-0" />
            {t('allProjects.confirmDeleteTitle') || "Confirm Deletion"}
          </AlertDialogTitle>
          <AlertDialogDescription className="dark:text-gray-300">
            {t('allProjects.confirmDeleteMsg', { projectName: projectToDelete?.project_number }) || `Are you sure you want to delete project ${projectToDelete?.project_number}? This action cannot be undone.`}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel className="dark:bg-gray-700 dark:hover:bg-gray-600 dark:border-gray-600">{t('userManagement.cancelButton') || "Cancel"}</AlertDialogCancel>
          <AlertDialogAction onClick={onConfirm} className="bg-red-600 hover:bg-red-700 dark:bg-red-500 dark:hover:bg-red-600 text-white">
            {t('allProjects.deleteButton') || "Delete"}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};

export default DeleteProjectDialog;