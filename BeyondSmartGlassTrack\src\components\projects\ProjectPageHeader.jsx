import React from 'react';
import { motion } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { PlusCircle } from 'lucide-react';

const ProjectPageHeader = ({ itemVariants }) => {
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();

  return (
    <motion.div 
      variants={itemVariants} 
      className="flex flex-col sm:flex-row justify-between items-center mb-6 gap-4"
    >
      <h1 className="text-3xl font-bold text-gray-800 dark:text-white gradient-text">
        {t('sidebar.allProjects')}
      </h1>
      <Button 
        onClick={() => navigate('/add-project')} 
        className="w-full sm:w-auto bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white"
      >
        <PlusCircle className={`h-5 w-5 ${i18n.language === 'ar' ? 'ml-2' : 'mr-2'}`} />
        {t('sidebar.addProject')}
      </Button>
    </motion.div>
  );
};

export default ProjectPageHeader;