import React from 'react';
import { motion } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import { Input } from '@/components/ui/input';
import { Search } from 'lucide-react';

const ProjectSearchBar = ({ searchTerm, handleSearch, itemVariants }) => {
  const { t, i18n } = useTranslation();

  return (
    <motion.div variants={itemVariants} className="mb-6">
      <div className="relative w-full sm:max-w-md">
        <Search className={`absolute ${i18n.language === 'ar' ? 'right-3' : 'left-3'} top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400 dark:text-gray-500`} />
        <Input
          type="text"
          placeholder={t('allProjects.searchPlaceholder') || "Search by No., Client, City..."}
          value={searchTerm}
          onChange={handleSearch}
          className={`w-full ${i18n.language === 'ar' ? 'pr-10' : 'pl-10'} dark:bg-gray-700 dark:border-gray-600 dark:text-white rounded-full shadow-sm focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400`}
        />
      </div>
    </motion.div>
  );
};

export default ProjectSearchBar;