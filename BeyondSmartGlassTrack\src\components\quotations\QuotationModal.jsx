import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useToast } from '@/components/ui/use-toast';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Calculator, Save, X } from 'lucide-react';
import {
  addQuotation,
  updateQuotation,
  generateQuotationNumber,
  calculateFinalPrice,
  validateQuotationData
} from '@/lib/quotationsService';

const QuotationModal = ({ isOpen, onClose, quotation, onSave }) => {
  const { t } = useTranslation();
  const { toast } = useToast();
  
  const [formData, setFormData] = useState({
    quotation_number: '',
    client_name: '',
    client_phone: '',
    client_city: '',
    item_type: 'Smart Film',
    area: '',
    price_per_sqm: '',
    total_price: 0,
    discount_percentage: 0,
    final_price: 0,
    status: 'pending',
    valid_until: '',
    notes: ''
  });
  
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});

  const itemTypes = [
    'Smart Film',
    'Smart Glass',
    'Devices',
    'Remotes',
    'Accessories'
  ];

  useEffect(() => {
    if (isOpen) {
      if (quotation) {
        // Edit mode
        setFormData({
          ...quotation,
          area: quotation.area?.toString() || '',
          price_per_sqm: quotation.price_per_sqm?.toString() || '',
          discount_percentage: quotation.discount_percentage?.toString() || '0',
          valid_until: quotation.valid_until || ''
        });
      } else {
        // Add mode
        resetForm();
        generateNewQuotationNumber();
      }
    }
  }, [isOpen, quotation]);

  useEffect(() => {
    // Recalculate prices when area, price per sqm, or discount changes
    if (formData.area && formData.price_per_sqm) {
      const area = parseFloat(formData.area) || 0;
      const pricePerSqm = parseFloat(formData.price_per_sqm) || 0;
      const discountPercentage = parseFloat(formData.discount_percentage) || 0;
      
      const totalPrice = area * pricePerSqm;
      const finalPrice = calculateFinalPrice(area, pricePerSqm, discountPercentage);
      
      setFormData(prev => ({
        ...prev,
        total_price: totalPrice,
        final_price: finalPrice
      }));
    }
  }, [formData.area, formData.price_per_sqm, formData.discount_percentage]);

  const resetForm = () => {
    setFormData({
      quotation_number: '',
      client_name: '',
      client_phone: '',
      client_city: '',
      item_type: 'Smart Film',
      area: '',
      price_per_sqm: '',
      total_price: 0,
      discount_percentage: 0,
      final_price: 0,
      status: 'pending',
      valid_until: '',
      notes: ''
    });
    setErrors({});
  };

  const generateNewQuotationNumber = async () => {
    try {
      const quotationNumber = await generateQuotationNumber();
      setFormData(prev => ({ ...prev, quotation_number: quotationNumber }));
    } catch (error) {
      console.error('Error generating quotation number:', error);
    }
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error for this field
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: null }));
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // Validate form data
    const validation = validateQuotationData({
      ...formData,
      area: parseFloat(formData.area),
      price_per_sqm: parseFloat(formData.price_per_sqm),
      discount_percentage: parseFloat(formData.discount_percentage)
    });

    if (!validation.isValid) {
      const errorObj = {};
      validation.errors.forEach(error => {
        if (error.includes('Client name')) errorObj.client_name = error;
        if (error.includes('Client phone')) errorObj.client_phone = error;
        if (error.includes('Item type')) errorObj.item_type = error;
        if (error.includes('Area')) errorObj.area = error;
        if (error.includes('Price per sqm')) errorObj.price_per_sqm = error;
        if (error.includes('Discount')) errorObj.discount_percentage = error;
      });
      setErrors(errorObj);
      return;
    }

    setLoading(true);
    
    try {
      const quotationData = {
        ...formData,
        area: parseFloat(formData.area),
        price_per_sqm: parseFloat(formData.price_per_sqm),
        total_price: formData.total_price,
        discount_percentage: parseFloat(formData.discount_percentage) || 0,
        final_price: formData.final_price
      };

      if (quotation) {
        await updateQuotation(quotation.id, quotationData);
        toast({
          title: t('common.success'),
          description: 'Quotation updated successfully',
          className: 'bg-green-500 text-white'
        });
      } else {
        await addQuotation(quotationData);
        toast({
          title: t('common.success'),
          description: 'Quotation created successfully',
          className: 'bg-green-500 text-white'
        });
      }

      onSave();
      onClose();
    } catch (error) {
      console.error('Error saving quotation:', error);
      toast({
        title: t('common.error'),
        description: 'Failed to save quotation',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Calculator size={24} />
            {quotation ? t('quotations.editQuotation') : t('quotations.addQuotation')}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle>Basic Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="quotation_number">Quotation Number</Label>
                  <Input
                    id="quotation_number"
                    value={formData.quotation_number}
                    onChange={(e) => handleInputChange('quotation_number', e.target.value)}
                    disabled
                    className="bg-gray-50"
                  />
                </div>
                <div>
                  <Label htmlFor="valid_until">Valid Until</Label>
                  <Input
                    id="valid_until"
                    type="date"
                    value={formData.valid_until}
                    onChange={(e) => handleInputChange('valid_until', e.target.value)}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Client Information */}
          <Card>
            <CardHeader>
              <CardTitle>Client Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="client_name">Client Name *</Label>
                  <Input
                    id="client_name"
                    value={formData.client_name}
                    onChange={(e) => handleInputChange('client_name', e.target.value)}
                    className={errors.client_name ? 'border-red-500' : ''}
                  />
                  {errors.client_name && (
                    <p className="text-red-500 text-sm mt-1">{errors.client_name}</p>
                  )}
                </div>
                <div>
                  <Label htmlFor="client_phone">Client Phone *</Label>
                  <Input
                    id="client_phone"
                    value={formData.client_phone}
                    onChange={(e) => handleInputChange('client_phone', e.target.value)}
                    className={errors.client_phone ? 'border-red-500' : ''}
                  />
                  {errors.client_phone && (
                    <p className="text-red-500 text-sm mt-1">{errors.client_phone}</p>
                  )}
                </div>
                <div>
                  <Label htmlFor="client_city">Client City</Label>
                  <Input
                    id="client_city"
                    value={formData.client_city}
                    onChange={(e) => handleInputChange('client_city', e.target.value)}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Product & Pricing */}
          <Card>
            <CardHeader>
              <CardTitle>Product & Pricing</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="item_type">Item Type *</Label>
                  <select
                    id="item_type"
                    value={formData.item_type}
                    onChange={(e) => handleInputChange('item_type', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    {itemTypes.map(type => (
                      <option key={type} value={type}>{type}</option>
                    ))}
                  </select>
                </div>
                <div>
                  <Label htmlFor="area">Area (sqm) *</Label>
                  <Input
                    id="area"
                    type="number"
                    step="0.01"
                    value={formData.area}
                    onChange={(e) => handleInputChange('area', e.target.value)}
                    className={errors.area ? 'border-red-500' : ''}
                  />
                  {errors.area && (
                    <p className="text-red-500 text-sm mt-1">{errors.area}</p>
                  )}
                </div>
                <div>
                  <Label htmlFor="price_per_sqm">Price per sqm (SAR) *</Label>
                  <Input
                    id="price_per_sqm"
                    type="number"
                    step="0.01"
                    value={formData.price_per_sqm}
                    onChange={(e) => handleInputChange('price_per_sqm', e.target.value)}
                    className={errors.price_per_sqm ? 'border-red-500' : ''}
                  />
                  {errors.price_per_sqm && (
                    <p className="text-red-500 text-sm mt-1">{errors.price_per_sqm}</p>
                  )}
                </div>
                <div>
                  <Label htmlFor="discount_percentage">Discount (%)</Label>
                  <Input
                    id="discount_percentage"
                    type="number"
                    step="0.01"
                    min="0"
                    max="100"
                    value={formData.discount_percentage}
                    onChange={(e) => handleInputChange('discount_percentage', e.target.value)}
                    className={errors.discount_percentage ? 'border-red-500' : ''}
                  />
                  {errors.discount_percentage && (
                    <p className="text-red-500 text-sm mt-1">{errors.discount_percentage}</p>
                  )}
                </div>
              </div>

              {/* Price Summary */}
              <div className="bg-gray-50 p-4 rounded-lg">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                  <div>
                    <span className="text-gray-600">Total Price:</span>
                    <p className="font-semibold text-lg">{formatCurrency(formData.total_price)}</p>
                  </div>
                  <div>
                    <span className="text-gray-600">Discount:</span>
                    <p className="font-semibold text-lg text-red-600">
                      -{formatCurrency((formData.total_price * (formData.discount_percentage || 0)) / 100)}
                    </p>
                  </div>
                  <div>
                    <span className="text-gray-600">Final Price:</span>
                    <p className="font-bold text-xl text-green-600">{formatCurrency(formData.final_price)}</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Notes */}
          <Card>
            <CardHeader>
              <CardTitle>Additional Notes</CardTitle>
            </CardHeader>
            <CardContent>
              <Textarea
                placeholder="Enter any additional notes or specifications..."
                value={formData.notes}
                onChange={(e) => handleInputChange('notes', e.target.value)}
                rows={3}
              />
            </CardContent>
          </Card>

          {/* Actions */}
          <div className="flex justify-end gap-3">
            <Button type="button" variant="outline" onClick={onClose}>
              <X size={16} className="mr-2" />
              {t('common.cancel')}
            </Button>
            <Button type="submit" disabled={loading} className="bg-blue-600 hover:bg-blue-700">
              <Save size={16} className="mr-2" />
              {loading ? 'Saving...' : (quotation ? t('common.update') : t('common.save'))}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default QuotationModal;
