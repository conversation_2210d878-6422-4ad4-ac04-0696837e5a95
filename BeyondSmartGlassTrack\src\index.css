
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&family=Roboto:wght@400;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;

  --card: 0 0% 100%;
  --card-foreground: 222.2 84% 4.9%;

  --popover: 0 0% 100%;
  --popover-foreground: 222.2 84% 4.9%;

  --primary: 221.2 83.2% 53.3%; /* Blue */
  --primary-foreground: 210 40% 98%;

  --secondary: 210 40% 96.1%; /* Light Gray */
  --secondary-foreground: 222.2 47.4% 11.2%;

  --muted: 210 40% 96.1%;
  --muted-foreground: 215.4 16.3% 46.9%;

  --accent: 210 40% 96.1%;
  --accent-foreground: 222.2 47.4% 11.2%;

  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 210 40% 98%;

  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --ring: 221.2 83.2% 53.3%;

  --radius: 0.5rem;
}

.dark {
  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;

  --card: 222.2 84% 4.9%;
  --card-foreground: 210 40% 98%;

  --popover: 222.2 84% 4.9%;
  --popover-foreground: 210 40% 98%;

  --primary: 217.2 91.2% 59.8%; /* Brighter Blue for dark mode */
  --primary-foreground: 222.2 47.4% 11.2%;

  --secondary: 217.2 32.6% 17.5%; /* Darker Gray */
  --secondary-foreground: 210 40% 98%;

  --muted: 217.2 32.6% 17.5%;
  --muted-foreground: 215 20.2% 65.1%;

  --accent: 217.2 32.6% 17.5%;
  --accent-foreground: 210 40% 98%;

  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 210 40% 98%;

  --border: 217.2 32.6% 17.5%;
  --input: 217.2 32.6% 17.5%;
  --ring: 217.2 91.2% 59.8%;
}

body {
  @apply bg-background text-foreground;
  font-family: 'Roboto', 'Cairo', sans-serif;
}

/* Custom scrollbar for webkit browsers */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--secondary));
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--primary));
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--primary) / 0.8);
}

/* Glassmorphism Card Example */
.glass-card {
  background: rgba(255, 255, 255, 0.1);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border-radius: 1rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
}

.dark .glass-card {
  background: rgba(30, 30, 40, 0.2); /* Darker glass for dark mode */
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Neumorphism Button Example */
.neumorphic-button {
  background: hsl(var(--background));
  border-radius: 0.75rem;
  padding: 0.75rem 1.5rem;
  box-shadow:
    5px 5px 10px hsl(var(--muted)),
    -5px -5px 10px hsl(var(--background) / 0.7);
  transition: all 0.2s ease-in-out;
}

.neumorphic-button:hover {
  box-shadow:
    inset 2px 2px 5px hsl(var(--muted)),
    inset -2px -2px 5px hsl(var(--background) / 0.7);
}

.dark .neumorphic-button {
  box-shadow:
    5px 5px 10px #1a1a2e, /* Darker shadow for dark mode */
    -5px -5px 10px #2c2c44; /* Darker highlight for dark mode */
}

.dark .neumorphic-button:hover {
  box-shadow:
    inset 2px 2px 5px #1a1a2e,
    inset -2px -2px 5px #2c2c44;
}

/* Gradient Text Example */
.gradient-text {
  @apply bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 text-transparent bg-clip-text;
}
