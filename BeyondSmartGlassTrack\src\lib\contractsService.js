import { supabase } from './supabase';

// ==================== CONTRACTS ====================

// إضافة عقد جديد
export const addContract = async (contractData) => {
  try {
    const { data, error } = await supabase
      .from('contracts')
      .insert([{
        ...contractData,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }])
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error adding contract:', error);
    throw error;
  }
};

// الحصول على جميع العقود
export const getAllContracts = async () => {
  try {
    const { data, error } = await supabase
      .from('contracts')
      .select(`
        *,
        quotations (
          quotation_number,
          client_name,
          client_phone,
          client_city,
          item_type,
          area,
          final_price
        )
      `)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('Error getting contracts:', error);
    throw error;
  }
};

// الحصول على عقد بالمعرف
export const getContractById = async (id) => {
  try {
    const { data, error } = await supabase
      .from('contracts')
      .select(`
        *,
        quotations (
          quotation_number,
          client_name,
          client_phone,
          client_city,
          item_type,
          area,
          price_per_sqm,
          total_price,
          discount_percentage,
          final_price,
          notes
        )
      `)
      .eq('id', id)
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error getting contract:', error);
    throw error;
  }
};

// تحديث عقد
export const updateContract = async (id, contractData) => {
  try {
    const { data, error } = await supabase
      .from('contracts')
      .update({
        ...contractData,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error updating contract:', error);
    throw error;
  }
};

// حذف عقد
export const deleteContract = async (id) => {
  try {
    const { error } = await supabase
      .from('contracts')
      .delete()
      .eq('id', id);

    if (error) throw error;
    return true;
  } catch (error) {
    console.error('Error deleting contract:', error);
    throw error;
  }
};

// تحويل عرض سعر إلى عقد
export const convertQuotationToContract = async (quotationId, contractData) => {
  try {
    // Get quotation details
    const { data: quotation, error: quotationError } = await supabase
      .from('quotations')
      .select('*')
      .eq('id', quotationId)
      .single();

    if (quotationError) throw quotationError;

    // Generate contract number
    const contractNumber = await generateContractNumber();

    // Create contract
    const newContract = {
      contract_number: contractNumber,
      quotation_id: quotationId,
      contract_value: quotation.final_price,
      remaining_amount: quotation.final_price,
      payment_status: 'unpaid',
      contract_status: 'signed',
      ...contractData
    };

    const { data: contract, error: contractError } = await supabase
      .from('contracts')
      .insert([newContract])
      .select()
      .single();

    if (contractError) throw contractError;

    // Update quotation status to approved
    const { error: updateError } = await supabase
      .from('quotations')
      .update({ status: 'approved' })
      .eq('id', quotationId);

    if (updateError) throw updateError;

    return contract;
  } catch (error) {
    console.error('Error converting quotation to contract:', error);
    throw error;
  }
};

// البحث في العقود
export const searchContracts = async (searchTerm) => {
  try {
    const { data, error } = await supabase
      .from('contracts')
      .select(`
        *,
        quotations (
          quotation_number,
          client_name,
          client_phone,
          client_city,
          item_type,
          area,
          final_price
        )
      `)
      .or(`contract_number.ilike.%${searchTerm}%`)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('Error searching contracts:', error);
    throw error;
  }
};

// فلترة العقود حسب الحالة
export const getContractsByStatus = async (status) => {
  try {
    const { data, error } = await supabase
      .from('contracts')
      .select(`
        *,
        quotations (
          quotation_number,
          client_name,
          client_phone,
          client_city,
          item_type,
          area,
          final_price
        )
      `)
      .eq('contract_status', status)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('Error getting contracts by status:', error);
    throw error;
  }
};

// الحصول على إحصائيات العقود
export const getContractsAnalytics = async () => {
  try {
    const { data, error } = await supabase
      .from('contracts')
      .select('contract_status, payment_status, contract_value, paid_amount, created_at');

    if (error) throw error;

    const analytics = {
      total: data.length,
      signed: data.filter(c => c.contract_status === 'signed').length,
      inProgress: data.filter(c => c.contract_status === 'in_progress').length,
      completed: data.filter(c => c.contract_status === 'completed').length,
      cancelled: data.filter(c => c.contract_status === 'cancelled').length,
      totalValue: data.reduce((sum, c) => sum + (parseFloat(c.contract_value) || 0), 0),
      paidAmount: data.reduce((sum, c) => sum + (parseFloat(c.paid_amount) || 0), 0),
      pendingAmount: data.reduce((sum, c) => sum + (parseFloat(c.contract_value) - parseFloat(c.paid_amount) || 0), 0),
      paymentStats: {
        paid: data.filter(c => c.payment_status === 'paid').length,
        partial: data.filter(c => c.payment_status === 'partial').length,
        unpaid: data.filter(c => c.payment_status === 'unpaid').length
      },
      monthlyData: getMonthlyContractsData(data)
    };

    return analytics;
  } catch (error) {
    console.error('Error getting contracts analytics:', error);
    throw error;
  }
};

// دالة مساعدة لحساب البيانات الشهرية
const getMonthlyContractsData = (contracts) => {
  const monthlyData = {};
  
  contracts.forEach(contract => {
    const month = new Date(contract.created_at).toISOString().slice(0, 7); // YYYY-MM
    if (!monthlyData[month]) {
      monthlyData[month] = {
        count: 0,
        value: 0,
        completed: 0,
        completedValue: 0
      };
    }
    
    monthlyData[month].count++;
    monthlyData[month].value += parseFloat(contract.contract_value) || 0;
    
    if (contract.contract_status === 'completed') {
      monthlyData[month].completed++;
      monthlyData[month].completedValue += parseFloat(contract.contract_value) || 0;
    }
  });

  return Object.entries(monthlyData)
    .map(([month, data]) => ({
      month,
      ...data
    }))
    .sort((a, b) => a.month.localeCompare(b.month));
};

// تحديث حالة العقد
export const updateContractStatus = async (id, status) => {
  try {
    const { data, error } = await supabase
      .from('contracts')
      .update({
        contract_status: status,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error updating contract status:', error);
    throw error;
  }
};

// تحديث حالة الدفع
export const updatePaymentStatus = async (id, paidAmount) => {
  try {
    // Get current contract
    const { data: contract, error: getError } = await supabase
      .from('contracts')
      .select('contract_value')
      .eq('id', id)
      .single();

    if (getError) throw getError;

    const contractValue = parseFloat(contract.contract_value);
    const paid = parseFloat(paidAmount);
    const remaining = contractValue - paid;

    let paymentStatus = 'unpaid';
    if (paid >= contractValue) {
      paymentStatus = 'paid';
    } else if (paid > 0) {
      paymentStatus = 'partial';
    }

    const { data, error } = await supabase
      .from('contracts')
      .update({
        paid_amount: paid,
        remaining_amount: remaining,
        payment_status: paymentStatus,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error updating payment status:', error);
    throw error;
  }
};

// إنشاء رقم عقد جديد
export const generateContractNumber = async () => {
  try {
    const { data, error } = await supabase
      .from('contracts')
      .select('contract_number')
      .order('created_at', { ascending: false })
      .limit(1);

    if (error) throw error;

    const currentYear = new Date().getFullYear();
    const prefix = `BSG-CON-${currentYear}-`;
    
    if (data && data.length > 0) {
      const lastNumber = data[0].contract_number;
      const lastSequence = parseInt(lastNumber.split('-').pop()) || 0;
      const newSequence = (lastSequence + 1).toString().padStart(4, '0');
      return `${prefix}${newSequence}`;
    } else {
      return `${prefix}0001`;
    }
  } catch (error) {
    console.error('Error generating contract number:', error);
    return `BSG-CON-${new Date().getFullYear()}-0001`;
  }
};

// التحقق من صحة بيانات العقد
export const validateContractData = (contractData) => {
  const errors = [];

  if (!contractData.quotation_id) {
    errors.push('Quotation ID is required');
  }

  if (!contractData.client_approval_date) {
    errors.push('Client approval date is required');
  }

  if (!contractData.signed_date) {
    errors.push('Signed date is required');
  }

  if (contractData.start_date && contractData.expected_completion) {
    const startDate = new Date(contractData.start_date);
    const endDate = new Date(contractData.expected_completion);
    if (endDate <= startDate) {
      errors.push('Expected completion date must be after start date');
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};
