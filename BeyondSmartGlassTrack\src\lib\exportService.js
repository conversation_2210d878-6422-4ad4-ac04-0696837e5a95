// خدمة تصدير التقارير والمشاريع بصيغة PDF وExcel

import jsPDF from 'jspdf';
import 'jspdf-autotable';
import * as XLSX from 'xlsx';
import { saveAs } from 'file-saver';

// تصدير المشاريع بصيغة PDF
export const exportProjectsToPDF = (projects, title, t, isRTL = false) => {
  // إنشاء مستند PDF جديد
  const doc = new jsPDF({
    orientation: 'landscape',
    unit: 'mm',
    format: 'a4'
  });

  // إضافة دعم اللغة العربية
  if (isRTL) {
    doc.setR2L(true);
  }

  // إضافة عنوان التقرير
  doc.setFontSize(18);
  const pageWidth = doc.internal.pageSize.getWidth();
  doc.text(title, pageWidth / 2, 15, { align: 'center' });

  // إضافة التاريخ
  doc.setFontSize(10);
  const today = new Date().toLocaleDateString(isRTL ? 'ar-SA' : 'en-US');
  doc.text(`${t('exportService.date') || 'Date'}: ${today}`, pageWidth - 20, 10, { align: isRTL ? 'left' : 'right' });

  // تحضير البيانات للجدول
  const tableData = projects.map(project => [
    project.project_number || '',
    project.client_name || '',
    project.city || '',
    project.start_date ? new Date(project.start_date).toLocaleDateString(isRTL ? 'ar-SA' : 'en-US') : '',
    project.status || '',
    project.quantity_pvc || '0',
    project.quantity_shutter || '0',
    project.quantity_sgs || '0',
    project.quantity_doors || '0',
    project.quantity_glass || '0'
  ]);

  // تحديد رؤوس الأعمدة
  const headers = [
    [
      t('addProject.projectNumber') || 'Project Number',
      t('addProject.clientName') || 'Client Name',
      t('addProject.city') || 'City',
      t('addProject.startDate') || 'Start Date',
      t('allProjects.status') || 'Status',
      t('dashboard.pvc') || 'PVC (m²)',
      t('dashboard.shutter') || 'Shutter (units)',
      t('dashboard.sgs') || 'SGS (m)',
      t('dashboard.doors') || 'Doors (units)',
      t('dashboard.glass') || 'Glass (m²)'
    ]
  ];

  // إنشاء الجدول
  doc.autoTable({
    head: headers,
    body: tableData,
    startY: 25,
    theme: 'grid',
    styles: {
      fontSize: 8,
      cellPadding: 2,
      overflow: 'linebreak',
      halign: isRTL ? 'right' : 'left'
    },
    headStyles: {
      fillColor: [41, 128, 185],
      textColor: 255,
      fontStyle: 'bold'
    },
    alternateRowStyles: {
      fillColor: [240, 240, 240]
    }
  });

  // إضافة ترويسة وتذييل
  const pageCount = doc.internal.getNumberOfPages();
  for (let i = 1; i <= pageCount; i++) {
    doc.setPage(i);
    // تذييل الصفحة
    doc.setFontSize(8);
    doc.text(
      `${t('exportService.page') || 'Page'} ${i} ${t('exportService.of') || 'of'} ${pageCount}`,
      pageWidth / 2,
      doc.internal.pageSize.getHeight() - 10,
      { align: 'center' }
    );
    // ترويسة الصفحة - اسم التطبيق
    doc.text('SultanTrack', 20, 10);
  }

  // حفظ الملف
  doc.save(`${title.replace(/\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.pdf`);
};

// تصدير مشروع واحد بصيغة PDF
export const exportProjectToPDF = (project, visits, t, isRTL = false) => {
  if (!project) return;

  // إنشاء مستند PDF جديد
  const doc = new jsPDF({
    orientation: 'portrait',
    unit: 'mm',
    format: 'a4'
  });

  // إضافة دعم اللغة العربية
  if (isRTL) {
    doc.setR2L(true);
  }

  // إضافة عنوان التقرير
  doc.setFontSize(18);
  const pageWidth = doc.internal.pageSize.getWidth();
  const title = `${t('trackProject.projectReport') || 'Project Report'}: ${project.project_number}`;
  doc.text(title, pageWidth / 2, 15, { align: 'center' });

  // إضافة التاريخ
  doc.setFontSize(10);
  const today = new Date().toLocaleDateString(isRTL ? 'ar-SA' : 'en-US');
  doc.text(`${t('exportService.date') || 'Date'}: ${today}`, pageWidth - 20, 10, { align: isRTL ? 'left' : 'right' });

  // إضافة معلومات المشروع
  doc.setFontSize(12);
  doc.text(`${t('addProject.clientName') || 'Client Name'}: ${project.client_name || ''}`, 20, 30);
  doc.text(`${t('addProject.city') || 'City'}: ${project.city || ''}`, 20, 37);
  doc.text(`${t('addProject.startDate') || 'Start Date'}: ${project.start_date ? new Date(project.start_date).toLocaleDateString(isRTL ? 'ar-SA' : 'en-US') : ''}`, 20, 44);
  doc.text(`${t('allProjects.status') || 'Status'}: ${project.status || ''}`, 20, 51);

  // إضافة جدول الكميات
  doc.setFontSize(14);
  doc.text(t('trackProject.quantities') || 'Quantities', 20, 65);

  const quantitiesData = [
    [t('dashboard.pvc') || 'PVC (m²)', project.quantity_pvc || '0'],
    [t('dashboard.shutter') || 'Shutter (units)', project.quantity_shutter || '0'],
    [t('dashboard.sgs') || 'SGS (m)', project.quantity_sgs || '0'],
    [t('dashboard.doors') || 'Doors (units)', project.quantity_doors || '0'],
    [t('dashboard.glass') || 'Glass (m²)', project.quantity_glass || '0']
  ];

  doc.autoTable({
    head: [[t('trackProject.item') || 'Item', t('trackProject.quantity') || 'Quantity']],
    body: quantitiesData,
    startY: 70,
    theme: 'grid',
    styles: {
      fontSize: 10,
      cellPadding: 3,
      halign: isRTL ? 'right' : 'left'
    },
    headStyles: {
      fillColor: [41, 128, 185],
      textColor: 255,
      fontStyle: 'bold'
    }
  });

  // إضافة جدول الزيارات
  if (visits && visits.length > 0) {
    doc.setFontSize(14);
    doc.text(t('trackProject.visitHistory') || 'Visit History', 20, doc.autoTable.previous.finalY + 15);

    const visitsData = visits.map(visit => [
      visit.visit_date ? new Date(visit.visit_date).toLocaleDateString(isRTL ? 'ar-SA' : 'en-US') : '',
      visit.pvc_completed || '0',
      visit.shutter_completed || '0',
      visit.sgs_completed || '0',
      visit.doors_completed || '0',
      visit.glass_completed || '0',
      visit.notes || ''
    ]);

    doc.autoTable({
      head: [[
        t('trackProject.visitDate') || 'Visit Date',
        t('dashboard.pvc') || 'PVC',
        t('dashboard.shutter') || 'Shutter',
        t('dashboard.sgs') || 'SGS',
        t('dashboard.doors') || 'Doors',
        t('dashboard.glass') || 'Glass',
        t('trackProject.notes') || 'Notes'
      ]],
      body: visitsData,
      startY: doc.autoTable.previous.finalY + 20,
      theme: 'grid',
      styles: {
        fontSize: 8,
        cellPadding: 2,
        overflow: 'linebreak',
        halign: isRTL ? 'right' : 'left'
      },
      headStyles: {
        fillColor: [41, 128, 185],
        textColor: 255,
        fontStyle: 'bold'
      },
      columnStyles: {
        6: { cellWidth: 50 } // عرض أكبر لعمود الملاحظات
      }
    });
  }

  // إضافة ترويسة وتذييل
  const pageCount = doc.internal.getNumberOfPages();
  for (let i = 1; i <= pageCount; i++) {
    doc.setPage(i);
    // تذييل الصفحة
    doc.setFontSize(8);
    doc.text(
      `${t('exportService.page') || 'Page'} ${i} ${t('exportService.of') || 'of'} ${pageCount}`,
      pageWidth / 2,
      doc.internal.pageSize.getHeight() - 10,
      { align: 'center' }
    );
    // ترويسة الصفحة - اسم التطبيق
    doc.text('SultanTrack', 20, 10);
  }

  // حفظ الملف
  doc.save(`Project_${project.project_number}_${new Date().toISOString().split('T')[0]}.pdf`);
};

// تصدير المشاريع بصيغة Excel
export const exportProjectsToExcel = (projects, title, t) => {
  // تحضير البيانات
  const data = projects.map(project => ({
    [t('addProject.projectNumber') || 'Project Number']: project.project_number || '',
    [t('addProject.clientName') || 'Client Name']: project.client_name || '',
    [t('addProject.city') || 'City']: project.city || '',
    [t('addProject.startDate') || 'Start Date']: project.start_date ? new Date(project.start_date).toLocaleDateString() : '',
    [t('allProjects.status') || 'Status']: project.status || '',
    [t('dashboard.pvc') || 'PVC (m²)']: project.quantity_pvc || '0',
    [t('dashboard.shutter') || 'Shutter (units)']: project.quantity_shutter || '0',
    [t('dashboard.sgs') || 'SGS (m)']: project.quantity_sgs || '0',
    [t('dashboard.doors') || 'Doors (units)']: project.quantity_doors || '0',
    [t('dashboard.glass') || 'Glass (m²)']: project.quantity_glass || '0'
  }));

  // إنشاء ورقة عمل
  const worksheet = XLSX.utils.json_to_sheet(data);

  // إنشاء مصنف عمل
  const workbook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(workbook, worksheet, 'Projects');

  // تصدير المصنف
  const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
  const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
  saveAs(blob, `${title.replace(/\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.xlsx`);
};

// تصدير مشروع واحد بصيغة Excel
export const exportProjectToExcel = (project, visits, t) => {
  if (!project) return;

  // تحضير بيانات المشروع
  const projectData = [{
    [t('addProject.projectNumber') || 'Project Number']: project.project_number || '',
    [t('addProject.clientName') || 'Client Name']: project.client_name || '',
    [t('addProject.city') || 'City']: project.city || '',
    [t('addProject.startDate') || 'Start Date']: project.start_date ? new Date(project.start_date).toLocaleDateString() : '',
    [t('allProjects.status') || 'Status']: project.status || '',
    [t('dashboard.pvc') || 'PVC (m²)']: project.quantity_pvc || '0',
    [t('dashboard.shutter') || 'Shutter (units)']: project.quantity_shutter || '0',
    [t('dashboard.sgs') || 'SGS (m)']: project.quantity_sgs || '0',
    [t('dashboard.doors') || 'Doors (units)']: project.quantity_doors || '0',
    [t('dashboard.glass') || 'Glass (m²)']: project.quantity_glass || '0'
  }];

  // تحضير بيانات الزيارات
  const visitsData = visits.map(visit => ({
    [t('trackProject.visitDate') || 'Visit Date']: visit.visit_date ? new Date(visit.visit_date).toLocaleDateString() : '',
    [t('dashboard.pvc') || 'PVC (m²)']: visit.pvc_completed || '0',
    [t('dashboard.shutter') || 'Shutter (units)']: visit.shutter_completed || '0',
    [t('dashboard.sgs') || 'SGS (m)']: visit.sgs_completed || '0',
    [t('dashboard.doors') || 'Doors (units)']: visit.doors_completed || '0',
    [t('dashboard.glass') || 'Glass (m²)']: visit.glass_completed || '0',
    [t('trackProject.notes') || 'Notes']: visit.notes || ''
  }));

  // إنشاء أوراق العمل
  const projectWorksheet = XLSX.utils.json_to_sheet(projectData);
  const visitsWorksheet = XLSX.utils.json_to_sheet(visitsData);

  // إنشاء مصنف عمل
  const workbook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(workbook, projectWorksheet, 'Project Details');
  XLSX.utils.book_append_sheet(workbook, visitsWorksheet, 'Visits');

  // تصدير المصنف
  const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
  const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
  saveAs(blob, `Project_${project.project_number}_${new Date().toISOString().split('T')[0]}.xlsx`);
};

export default {
  exportProjectsToPDF,
  exportProjectToPDF,
  exportProjectsToExcel,
  exportProjectToExcel
};
