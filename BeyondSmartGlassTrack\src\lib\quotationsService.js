import { supabase } from './supabase';

// ==================== QUOTATIONS ====================

// إضافة عرض سعر جديد
export const addQuotation = async (quotationData) => {
  try {
    const { data, error } = await supabase
      .from('quotations')
      .insert([{
        ...quotationData,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }])
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error adding quotation:', error);
    throw error;
  }
};

// الحصول على جميع عروض الأسعار
export const getAllQuotations = async () => {
  try {
    const { data, error } = await supabase
      .from('quotations')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('Error getting quotations:', error);
    throw error;
  }
};

// الحصول على عرض سعر بالمعرف
export const getQuotationById = async (id) => {
  try {
    const { data, error } = await supabase
      .from('quotations')
      .select('*')
      .eq('id', id)
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error getting quotation:', error);
    throw error;
  }
};

// تحديث عرض سعر
export const updateQuotation = async (id, quotationData) => {
  try {
    const { data, error } = await supabase
      .from('quotations')
      .update({
        ...quotationData,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error updating quotation:', error);
    throw error;
  }
};

// حذف عرض سعر
export const deleteQuotation = async (id) => {
  try {
    const { error } = await supabase
      .from('quotations')
      .delete()
      .eq('id', id);

    if (error) throw error;
    return true;
  } catch (error) {
    console.error('Error deleting quotation:', error);
    throw error;
  }
};

// البحث في عروض الأسعار
export const searchQuotations = async (searchTerm) => {
  try {
    const { data, error } = await supabase
      .from('quotations')
      .select('*')
      .or(`quotation_number.ilike.%${searchTerm}%,client_name.ilike.%${searchTerm}%,client_city.ilike.%${searchTerm}%`)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('Error searching quotations:', error);
    throw error;
  }
};

// فلترة عروض الأسعار حسب الحالة
export const getQuotationsByStatus = async (status) => {
  try {
    const { data, error } = await supabase
      .from('quotations')
      .select('*')
      .eq('status', status)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('Error getting quotations by status:', error);
    throw error;
  }
};

// الحصول على إحصائيات عروض الأسعار
export const getQuotationsAnalytics = async () => {
  try {
    const { data, error } = await supabase
      .from('quotations')
      .select('status, final_price, created_at');

    if (error) throw error;

    const analytics = {
      total: data.length,
      pending: data.filter(q => q.status === 'pending').length,
      approved: data.filter(q => q.status === 'approved').length,
      rejected: data.filter(q => q.status === 'rejected').length,
      totalValue: data.reduce((sum, q) => sum + (parseFloat(q.final_price) || 0), 0),
      approvedValue: data
        .filter(q => q.status === 'approved')
        .reduce((sum, q) => sum + (parseFloat(q.final_price) || 0), 0),
      monthlyData: getMonthlyQuotationsData(data)
    };

    return analytics;
  } catch (error) {
    console.error('Error getting quotations analytics:', error);
    throw error;
  }
};

// دالة مساعدة لحساب البيانات الشهرية
const getMonthlyQuotationsData = (quotations) => {
  const monthlyData = {};
  
  quotations.forEach(quotation => {
    const month = new Date(quotation.created_at).toISOString().slice(0, 7); // YYYY-MM
    if (!monthlyData[month]) {
      monthlyData[month] = {
        count: 0,
        value: 0,
        approved: 0,
        approvedValue: 0
      };
    }
    
    monthlyData[month].count++;
    monthlyData[month].value += parseFloat(quotation.final_price) || 0;
    
    if (quotation.status === 'approved') {
      monthlyData[month].approved++;
      monthlyData[month].approvedValue += parseFloat(quotation.final_price) || 0;
    }
  });

  return Object.entries(monthlyData)
    .map(([month, data]) => ({
      month,
      ...data
    }))
    .sort((a, b) => a.month.localeCompare(b.month));
};

// تحديث حالة عرض السعر
export const updateQuotationStatus = async (id, status) => {
  try {
    const { data, error } = await supabase
      .from('quotations')
      .update({
        status,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error updating quotation status:', error);
    throw error;
  }
};

// إنشاء رقم عرض سعر جديد
export const generateQuotationNumber = async () => {
  try {
    const { data, error } = await supabase
      .from('quotations')
      .select('quotation_number')
      .order('created_at', { ascending: false })
      .limit(1);

    if (error) throw error;

    const currentYear = new Date().getFullYear();
    const prefix = `BSG-${currentYear}-`;
    
    if (data && data.length > 0) {
      const lastNumber = data[0].quotation_number;
      const lastSequence = parseInt(lastNumber.split('-').pop()) || 0;
      const newSequence = (lastSequence + 1).toString().padStart(4, '0');
      return `${prefix}${newSequence}`;
    } else {
      return `${prefix}0001`;
    }
  } catch (error) {
    console.error('Error generating quotation number:', error);
    return `BSG-${new Date().getFullYear()}-0001`;
  }
};

// حساب السعر النهائي مع الخصم
export const calculateFinalPrice = (area, pricePerSqm, discountPercentage = 0) => {
  const totalPrice = area * pricePerSqm;
  const discountAmount = (totalPrice * discountPercentage) / 100;
  return totalPrice - discountAmount;
};

// التحقق من صحة بيانات عرض السعر
export const validateQuotationData = (quotationData) => {
  const errors = [];

  if (!quotationData.client_name || quotationData.client_name.trim() === '') {
    errors.push('Client name is required');
  }

  if (!quotationData.client_phone || quotationData.client_phone.trim() === '') {
    errors.push('Client phone is required');
  }

  if (!quotationData.item_type || quotationData.item_type.trim() === '') {
    errors.push('Item type is required');
  }

  if (!quotationData.area || quotationData.area <= 0) {
    errors.push('Area must be greater than 0');
  }

  if (!quotationData.price_per_sqm || quotationData.price_per_sqm <= 0) {
    errors.push('Price per sqm must be greater than 0');
  }

  if (quotationData.discount_percentage && (quotationData.discount_percentage < 0 || quotationData.discount_percentage > 100)) {
    errors.push('Discount percentage must be between 0 and 100');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};
