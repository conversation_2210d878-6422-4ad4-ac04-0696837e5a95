// خدمة تصدير PDF بسيطة

import jsPDF from 'jspdf';
import 'jspdf-autotable';

// تصدير المشاريع بصيغة PDF
export const exportProjectsToPDF = (projects) => {
  try {
    // إنشاء مستند PDF جديد
    const doc = new jsPDF('landscape');
    
    // إضافة عنوان التقرير
    doc.setFontSize(18);
    doc.text("Projects Report", doc.internal.pageSize.getWidth() / 2, 15, { align: 'center' });
    
    // إضافة التاريخ
    doc.setFontSize(10);
    const today = new Date().toLocaleDateString();
    doc.text(`Date: ${today}`, doc.internal.pageSize.getWidth() - 20, 10, { align: 'right' });
    
    // تحضير البيانات للجدول
    const tableData = projects.map(project => [
      project.project_number || '',
      project.client_name || '',
      project.city || '',
      project.start_date ? new Date(project.start_date).toLocaleDateString() : '',
      project.status || '',
      project.quantity_pvc || '0',
      project.quantity_shutter || '0',
      project.quantity_sgs || '0',
      project.quantity_doors || '0',
      project.quantity_glass || '0'
    ]);
    
    // تحديد رؤوس الأعمدة
    const headers = [
      [
        'Project Number',
        'Client Name',
        'City',
        'Start Date',
        'Status',
        'PVC (m²)',
        'Shutter (units)',
        'SGS (m)',
        'Doors (units)',
        'Glass (m²)'
      ]
    ];
    
    // إنشاء الجدول
    doc.autoTable({
      head: headers,
      body: tableData,
      startY: 25,
      theme: 'grid',
      styles: {
        fontSize: 8,
        cellPadding: 2,
        overflow: 'linebreak'
      },
      headStyles: {
        fillColor: [41, 128, 185],
        textColor: 255,
        fontStyle: 'bold'
      },
      alternateRowStyles: {
        fillColor: [240, 240, 240]
      }
    });
    
    // إضافة ترويسة وتذييل
    const pageCount = doc.internal.getNumberOfPages();
    for (let i = 1; i <= pageCount; i++) {
      doc.setPage(i);
      // تذييل الصفحة
      doc.setFontSize(8);
      doc.text(
        `Page ${i} of ${pageCount}`,
        doc.internal.pageSize.getWidth() / 2,
        doc.internal.pageSize.getHeight() - 10,
        { align: 'center' }
      );
      // ترويسة الصفحة - اسم التطبيق
      doc.text('SultanTrack', 20, 10);
    }
    
    // حفظ الملف
    doc.save(`Projects_Report_${new Date().toISOString().split('T')[0]}.pdf`);
    
    return true;
  } catch (error) {
    console.error('Error exporting to PDF:', error);
    return false;
  }
};

// تصدير طلبات الصيانة بصيغة PDF
export const exportMaintenanceRequestsToPDF = (requests) => {
  try {
    // إنشاء مستند PDF جديد
    const doc = new jsPDF('landscape');
    
    // إضافة عنوان التقرير
    doc.setFontSize(18);
    doc.text("Maintenance Requests Report", doc.internal.pageSize.getWidth() / 2, 15, { align: 'center' });
    
    // إضافة التاريخ
    doc.setFontSize(10);
    const today = new Date().toLocaleDateString();
    doc.text(`Date: ${today}`, doc.internal.pageSize.getWidth() - 20, 10, { align: 'right' });
    
    // تحضير البيانات للجدول
    const tableData = requests.map(request => [
      request.projects?.project_number || 'N/A',
      request.projects?.client_name || '',
      request.issue_type || '',
      request.description || '',
      request.request_date ? new Date(request.request_date).toLocaleDateString() : '',
      request.status || ''
    ]);
    
    // تحديد رؤوس الأعمدة
    const headers = [
      [
        'Project Number',
        'Client Name',
        'Issue Type',
        'Description',
        'Request Date',
        'Status'
      ]
    ];
    
    // إنشاء الجدول
    doc.autoTable({
      head: headers,
      body: tableData,
      startY: 25,
      theme: 'grid',
      styles: {
        fontSize: 8,
        cellPadding: 2,
        overflow: 'linebreak'
      },
      headStyles: {
        fillColor: [41, 128, 185],
        textColor: 255,
        fontStyle: 'bold'
      },
      alternateRowStyles: {
        fillColor: [240, 240, 240]
      },
      columnStyles: {
        3: { cellWidth: 60 } // عرض أكبر لعمود الوصف
      }
    });
    
    // إضافة ترويسة وتذييل
    const pageCount = doc.internal.getNumberOfPages();
    for (let i = 1; i <= pageCount; i++) {
      doc.setPage(i);
      // تذييل الصفحة
      doc.setFontSize(8);
      doc.text(
        `Page ${i} of ${pageCount}`,
        doc.internal.pageSize.getWidth() / 2,
        doc.internal.pageSize.getHeight() - 10,
        { align: 'center' }
      );
      // ترويسة الصفحة - اسم التطبيق
      doc.text('SultanTrack', 20, 10);
    }
    
    // حفظ الملف
    doc.save(`Maintenance_Requests_Report_${new Date().toISOString().split('T')[0]}.pdf`);
    
    return true;
  } catch (error) {
    console.error('Error exporting to PDF:', error);
    return false;
  }
};

// تصدير المشاريع تحت الضمان بصيغة PDF
export const exportWarrantyProjectsToPDF = (projects) => {
  try {
    // إنشاء مستند PDF جديد
    const doc = new jsPDF('landscape');
    
    // إضافة عنوان التقرير
    doc.setFontSize(18);
    doc.text("Warranty Projects Report", doc.internal.pageSize.getWidth() / 2, 15, { align: 'center' });
    
    // إضافة التاريخ
    doc.setFontSize(10);
    const today = new Date().toLocaleDateString();
    doc.text(`Date: ${today}`, doc.internal.pageSize.getWidth() - 20, 10, { align: 'right' });
    
    // تحضير البيانات للجدول
    const tableData = projects.map(project => {
      const completionDate = project.completion_date ? new Date(project.completion_date) : null;
      const warrantyEnd = completionDate ? new Date(completionDate.setFullYear(completionDate.getFullYear() + 5)) : null;
      
      return [
        project.project_number || '',
        project.client_name || '',
        project.city || '',
        project.completion_date ? new Date(project.completion_date).toLocaleDateString() : '',
        warrantyEnd ? warrantyEnd.toLocaleDateString() : ''
      ];
    });
    
    // تحديد رؤوس الأعمدة
    const headers = [
      [
        'Project Number',
        'Client Name',
        'City',
        'Completion Date',
        'Warranty End Date'
      ]
    ];
    
    // إنشاء الجدول
    doc.autoTable({
      head: headers,
      body: tableData,
      startY: 25,
      theme: 'grid',
      styles: {
        fontSize: 8,
        cellPadding: 2,
        overflow: 'linebreak'
      },
      headStyles: {
        fillColor: [41, 128, 185],
        textColor: 255,
        fontStyle: 'bold'
      },
      alternateRowStyles: {
        fillColor: [240, 240, 240]
      }
    });
    
    // إضافة ترويسة وتذييل
    const pageCount = doc.internal.getNumberOfPages();
    for (let i = 1; i <= pageCount; i++) {
      doc.setPage(i);
      // تذييل الصفحة
      doc.setFontSize(8);
      doc.text(
        `Page ${i} of ${pageCount}`,
        doc.internal.pageSize.getWidth() / 2,
        doc.internal.pageSize.getHeight() - 10,
        { align: 'center' }
      );
      // ترويسة الصفحة - اسم التطبيق
      doc.text('SultanTrack', 20, 10);
    }
    
    // حفظ الملف
    doc.save(`Warranty_Projects_Report_${new Date().toISOString().split('T')[0]}.pdf`);
    
    return true;
  } catch (error) {
    console.error('Error exporting to PDF:', error);
    return false;
  }
};

export default {
  exportProjectsToPDF,
  exportMaintenanceRequestsToPDF,
  exportWarrantyProjectsToPDF
};
