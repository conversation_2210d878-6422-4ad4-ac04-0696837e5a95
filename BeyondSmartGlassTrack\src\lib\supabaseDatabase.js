import { supabase } from './supabase';

// دالة لإنشاء كلمة مرور عشوائية
const generateRandomPassword = () => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
  let password = '';
  for (let i = 0; i < 12; i++) {
    password += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return password;
};

// ==================== المشاريع ====================

// إضافة مشروع جديد
export const addProject = async (projectData) => {
  try {
    const { data, error } = await supabase
      .from('projects')
      .insert([{
        ...projectData,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }])
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error adding project:', error);
    throw error;
  }
};

// الحصول على جميع المشاريع
export const getAllProjects = async () => {
  try {
    const { data, error } = await supabase
      .from('projects')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('Error getting projects:', error);
    throw error;
  }
};

// الحصول على مشروع بالمعرف
export const getProjectById = async (id) => {
  try {
    const { data, error } = await supabase
      .from('projects')
      .select('*')
      .eq('id', id)
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error getting project:', error);
    throw error;
  }
};

// تحديث مشروع
export const updateProject = async (id, projectData) => {
  try {
    const { data, error } = await supabase
      .from('projects')
      .update({
        ...projectData,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error updating project:', error);
    throw error;
  }
};

// حذف مشروع
export const deleteProject = async (id) => {
  try {
    const { error } = await supabase
      .from('projects')
      .delete()
      .eq('id', id);

    if (error) throw error;
    return true;
  } catch (error) {
    console.error('Error deleting project:', error);
    throw error;
  }
};

// ==================== المستخدمين ====================

// إضافة مستخدم جديد
export const addUser = async (userData) => {
  try {
    // التحقق من وجود البريد الإلكتروني مسبقاً
    const { data: existingUser, error: checkError } = await supabase
      .from('users')
      .select('email')
      .eq('email', userData.email)
      .single();

    if (existingUser) {
      throw new Error(`البريد الإلكتروني ${userData.email} موجود بالفعل في النظام`);
    }

    // استخدام كلمة المرور المرسلة أو إنشاء كلمة مرور عشوائية
    const password = userData.password || generateRandomPassword();
    const isTemporaryPassword = !userData.password;

    // أولاً، إنشاء المستخدم في نظام المصادقة
    const { data: authData, error: authError } = await supabase.auth.signUp({
      email: userData.email,
      password: password,
      options: {
        data: {
          role: userData.role,
          name: userData.name
        }
      }
    });

    if (authError) {
      // إذا كان المستخدم موجود بالفعل في نظام المصادقة، نتجاهل الخطأ
      if (!authError.message.includes('already registered')) {
        throw authError;
      }
    }

    // ثانياً، إضافة المستخدم إلى جدول users
    const { data, error } = await supabase
      .from('users')
      .insert([{
        name: userData.name,
        email: userData.email,
        phone: userData.phone,
        role: userData.role,
        department: userData.department,
        is_active: userData.is_active,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }])
      .select()
      .single();

    if (error) throw error;

    // إرجاع بيانات المستخدم مع كلمة المرور إذا كانت مؤقتة
    return {
      ...data,
      temporaryPassword: isTemporaryPassword ? password : null,
      password: password
    };
  } catch (error) {
    console.error('Error adding user:', error);
    throw error;
  }
};

// الحصول على جميع المستخدمين
export const getAllUsers = async () => {
  try {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('Error getting users:', error);
    throw error;
  }
};

// تحديث مستخدم
export const updateUser = async (id, userData) => {
  try {
    // فلترة البيانات للحقول الموجودة في قاعدة البيانات فقط
    const allowedFields = {
      name: userData.name,
      phone: userData.phone,
      role: userData.role,
      department: userData.department,
      is_active: userData.is_active,
      updated_at: new Date().toISOString()
    };

    // إزالة الحقول غير المعرفة أو null
    Object.keys(allowedFields).forEach(key => {
      if (allowedFields[key] === undefined) {
        delete allowedFields[key];
      }
    });

    const { data, error } = await supabase
      .from('users')
      .update(allowedFields)
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error updating user:', error);
    throw error;
  }
};

// حذف مستخدم
export const deleteUser = async (id) => {
  try {
    const { error } = await supabase
      .from('users')
      .delete()
      .eq('id', id);

    if (error) throw error;
    return true;
  } catch (error) {
    console.error('Error deleting user:', error);
    throw error;
  }
};

// إنشاء المستخدمين التجريبيين إذا لم يكونوا موجودين
export const ensureTestUsersExist = async () => {
  const testUsers = [
    {
      email: '<EMAIL>',
      password: 'password',
      role: 'manager',
      name: 'Manager User'
    },
    {
      email: '<EMAIL>',
      password: 'password',
      role: 'supervisor',
      name: 'Supervisor User'
    },
    {
      email: '<EMAIL>',
      password: 'password',
      role: 'data_entry',
      name: 'Data Entry User'
    },
    {
      email: '<EMAIL>',
      password: 'password',
      role: 'technician',
      name: 'Technician User'
    }
  ];

  for (const user of testUsers) {
    try {
      // التحقق من وجود المستخدم في جدول users
      const { data: existingUser } = await supabase
        .from('users')
        .select('email')
        .eq('email', user.email)
        .single();

      if (!existingUser) {
        // إنشاء المستخدم في نظام المصادقة
        const { error: authError } = await supabase.auth.signUp({
          email: user.email,
          password: user.password,
          options: {
            data: {
              role: user.role,
              name: user.name
            }
          }
        });

        if (authError && !authError.message.includes('already registered')) {
          console.error(`Error creating auth user ${user.email}:`, authError);
          continue;
        }

        // إضافة المستخدم إلى جدول users
        const { error: dbError } = await supabase
          .from('users')
          .insert([{
            name: user.name,
            email: user.email,
            role: user.role,
            is_active: true,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          }]);

        if (dbError) {
          console.error(`Error adding user ${user.email} to database:`, dbError);
        } else {
          console.log(`Test user ${user.email} created successfully`);
        }
      }
    } catch (error) {
      console.error(`Error processing user ${user.email}:`, error);
    }
  }
};

// ==================== طلبات الصيانة ====================

// إضافة طلب صيانة جديد
export const addMaintenanceRequest = async (requestData) => {
  try {
    const { data, error } = await supabase
      .from('maintenance_requests')
      .insert([{
        ...requestData,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }])
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error adding maintenance request:', error);
    throw error;
  }
};

// الحصول على جميع طلبات الصيانة
export const getAllMaintenanceRequests = async () => {
  try {
    // جلب طلبات الصيانة أولاً
    const { data: requests, error: requestsError } = await supabase
      .from('maintenance_requests')
      .select('*')
      .order('created_at', { ascending: false });

    if (requestsError) throw requestsError;

    // جلب جميع المشاريع للربط
    const { data: projects, error: projectsError } = await supabase
      .from('projects')
      .select('id, project_number, client_name');

    if (projectsError) {
      console.warn('Could not fetch projects for linking:', projectsError);
    }

    // ربط البيانات يدوياً
    const requestsWithProjects = (requests || []).map(request => {
      let projectInfo = null;

      // إذا كان project_id موجود وليس "new" أو null
      if (request.project_id && request.project_id !== 'new' && projects) {
        const project = projects.find(p => p.id === request.project_id);
        if (project) {
          projectInfo = {
            project_number: project.project_number,
            client_name: project.client_name
          };
        }
      }

      return {
        ...request,
        projects: projectInfo
      };
    });

    return requestsWithProjects;
  } catch (error) {
    console.error('Error getting maintenance requests:', error);
    throw error;
  }
};

// تحديث طلب صيانة
export const updateMaintenanceRequest = async (id, requestData) => {
  try {
    const { data, error } = await supabase
      .from('maintenance_requests')
      .update({
        ...requestData,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error updating maintenance request:', error);
    throw error;
  }
};

// حذف طلب صيانة
export const deleteMaintenanceRequest = async (id) => {
  try {
    const { error } = await supabase
      .from('maintenance_requests')
      .delete()
      .eq('id', id);

    if (error) throw error;
    return true;
  } catch (error) {
    console.error('Error deleting maintenance request:', error);
    throw error;
  }
};

// ==================== الزيارات ====================

// إضافة زيارة جديدة
export const addProjectVisit = async (visitData) => {
  try {
    const { data, error } = await supabase
      .from('project_visits')
      .insert([{
        ...visitData,
        created_at: new Date().toISOString()
      }])
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error adding visit:', error);
    throw error;
  }
};

// الحصول على زيارات مشروع
export const getProjectVisits = async (projectId) => {
  try {
    const { data, error } = await supabase
      .from('project_visits')
      .select('*')
      .eq('project_id', projectId)
      .order('visit_date', { ascending: false });

    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('Error getting project visits:', error);
    throw error;
  }
};

// الحصول على جميع الزيارات
export const getAllVisits = async () => {
  try {
    const { data, error } = await supabase
      .from('project_visits')
      .select('*')
      .order('visit_date', { ascending: false });

    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('Error getting all visits:', error);
    throw error;
  }
};

// ==================== النسخ الاحتياطي ====================

// إنشاء نسخة احتياطية
export const createBackup = async (backupData) => {
  try {
    const { data, error } = await supabase
      .from('backups')
      .insert([{
        ...backupData,
        created_at: new Date().toISOString()
      }])
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error creating backup:', error);
    throw error;
  }
};

// الحصول على جميع النسخ الاحتياطية
export const getAllBackups = async () => {
  try {
    const { data, error } = await supabase
      .from('backups')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('Error getting backups:', error);
    throw error;
  }
};

// الحصول على نسخة احتياطية بالمعرف
export const getBackupById = async (id) => {
  try {
    const { data, error } = await supabase
      .from('backups')
      .select('*')
      .eq('id', id)
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error getting backup:', error);
    throw error;
  }
};

// حذف نسخة احتياطية
export const deleteBackup = async (id) => {
  try {
    const { error } = await supabase
      .from('backups')
      .delete()
      .eq('id', id);

    if (error) throw error;
    return true;
  } catch (error) {
    console.error('Error deleting backup:', error);
    throw error;
  }
};
