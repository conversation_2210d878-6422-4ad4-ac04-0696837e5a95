import { supabase } from './supabase';

// إضافة زيارة جديدة
export const addVisit = async (visitData) => {
  try {
    const { data, error } = await supabase
      .from('project_visits')
      .insert([{
        project_id: visitData.project_id,
        visit_date: visitData.visit_date,
        project_status: visitData.project_status,
        material_shortages: visitData.material_shortages,
        materials_installed: visitData.materials_installed,
        notes: visitData.notes,
        created_at: new Date().toISOString()
      }])
      .select()
      .single();

    if (error) throw error;
    return { success: true, data };
  } catch (error) {
    console.error('Error adding visit:', error);
    return { success: false, error: error.message };
  }
};

// تحديث زيارة موجودة
export const updateVisit = async (visitId, visitData) => {
  try {
    const { data, error } = await supabase
      .from('project_visits')
      .update({
        visit_date: visitData.visit_date,
        project_status: visitData.project_status,
        material_shortages: visitData.material_shortages,
        materials_installed: visitData.materials_installed,
        notes: visitData.notes,
        updated_at: new Date().toISOString()
      })
      .eq('id', visitId)
      .select()
      .single();

    if (error) throw error;
    return { success: true, data };
  } catch (error) {
    console.error('Error updating visit:', error);
    return { success: false, error: error.message };
  }
};

// الحصول على جميع الزيارات لجميع المشاريع
export const getAllVisits = async () => {
  try {
    const { data, error } = await supabase
      .from('project_visits')
      .select('*')
      .order('visit_date', { ascending: false });

    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('Error fetching all visits:', error);
    throw error;
  }
};

// الحصول على جميع زيارات مشروع معين
export const getProjectVisits = async (projectId) => {
  try {
    const { data, error } = await supabase
      .from('project_visits')
      .select('*')
      .eq('project_id', projectId)
      .order('visit_date', { ascending: false });

    if (error) throw error;
    return { success: true, data: data || [] };
  } catch (error) {
    console.error('Error fetching project visits:', error);
    return { success: false, error: error.message, data: [] };
  }
};

// حذف زيارة
export const deleteVisit = async (visitId) => {
  try {
    const { error } = await supabase
      .from('project_visits')
      .delete()
      .eq('id', visitId);

    if (error) throw error;
    return { success: true };
  } catch (error) {
    console.error('Error deleting visit:', error);
    return { success: false, error: error.message };
  }
};

// الحصول على زيارة واحدة
export const getVisitById = async (visitId) => {
  try {
    const { data, error } = await supabase
      .from('project_visits')
      .select('*')
      .eq('id', visitId)
      .single();

    if (error) throw error;
    return { success: true, data };
  } catch (error) {
    console.error('Error fetching visit:', error);
    return { success: false, error: error.message };
  }
};

// إحصائيات الزيارات لمشروع معين
export const getProjectVisitsStats = async (projectId) => {
  try {
    const { data, error } = await supabase
      .from('project_visits')
      .select('project_status, visit_date')
      .eq('project_id', projectId);

    if (error) throw error;

    const stats = {
      totalVisits: data.length,
      lastVisitDate: data.length > 0 ? data.sort((a, b) => new Date(b.visit_date) - new Date(a.visit_date))[0].visit_date : null,
      statusHistory: data.reduce((acc, visit) => {
        acc[visit.project_status] = (acc[visit.project_status] || 0) + 1;
        return acc;
      }, {})
    };

    return { success: true, data: stats };
  } catch (error) {
    console.error('Error fetching visits stats:', error);
    return { success: false, error: error.message };
  }
};

// البحث في الزيارات
export const searchVisits = async (projectId, searchTerm) => {
  try {
    let query = supabase
      .from('project_visits')
      .select('*')
      .eq('project_id', projectId);

    if (searchTerm) {
      query = query.or(`notes.ilike.%${searchTerm}%,project_status.ilike.%${searchTerm}%`);
    }

    const { data, error } = await query.order('visit_date', { ascending: false });

    if (error) throw error;
    return { success: true, data: data || [] };
  } catch (error) {
    console.error('Error searching visits:', error);
    return { success: false, error: error.message, data: [] };
  }
};

// تصدير زيارات مشروع إلى JSON
export const exportProjectVisits = async (projectId) => {
  try {
    const result = await getProjectVisits(projectId);
    if (!result.success) throw new Error(result.error);

    const visitsData = result.data.map(visit => ({
      تاريخ_الزيارة: visit.visit_date,
      حالة_المشروع: visit.project_status,
      النواقص: visit.material_shortages,
      الملاحظات: visit.notes,
      تاريخ_الإنشاء: visit.created_at
    }));

    return { success: true, data: visitsData };
  } catch (error) {
    console.error('Error exporting visits:', error);
    return { success: false, error: error.message };
  }
};
