
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { useToast } from '@/components/ui/use-toast';
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { PlusCircle } from 'lucide-react';
import { addProject, getAllProjects } from '@/lib/supabaseDatabase';
import { supabase } from '@/lib/supabase';
import FileUploadManager from '@/components/FileUpload/FileUploadManager';
import { uploadFileToStorage } from '@/lib/fileService';


const AddProjectPage = () => {
  const { t, i18n } = useTranslation();
  const { toast } = useToast();
  const [formData, setFormData] = useState({
    contract_number: '',
    job_order: '',
    client_name: '',
    city: '',
    start_date: '',
    duration_weeks: '',
    quantity_pvc: '',
    quantity_shutter: '',
    quantity_sgs: '',
    quantity_doors: '',
    quantity_glass: '',
    project_type: [], // Changed to array
    status: 'Not Started',
    notes: '',
    completion_date: null,
  });
  const [files, setFiles] = useState([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [currentProjectId, setCurrentProjectId] = useState(null);



  const handleChange = (e) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleCheckboxChange = (typeValue) => {
    setFormData(prev => {
      const currentTypes = prev.project_type || [];
      const newTypes = currentTypes.includes(typeValue)
        ? currentTypes.filter(t => t !== typeValue)
        : [...currentTypes, typeValue];
      return { ...prev, project_type: newTypes };
    });
  };

  // معالجة تغيير الملفات
  const handleFilesChange = (newFiles) => {
    setFiles(newFiles);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // التحقق من صحة البيانات
      if (!formData.contract_number || !formData.client_name || !formData.city) {
        toast({
          title: "Validation Error",
          description: "Contract number, client name, and city are required fields.",
          variant: "destructive",
        });
        setIsSubmitting(false);
        return;
      }

      // تحويل البيانات إلى الأنواع المناسبة
      const projectData = {
        project_number: formData.contract_number,
        client_name: formData.client_name,
        client_phone: null, // يمكن إضافة حقل رقم الهاتف لاحقاً
        client_email: null, // يمكن إضافة حقل البريد الإلكتروني لاحقاً
        project_type: formData.project_type.join(', ') || null,
        project_location: formData.city,
        project_description: formData.notes || null,
        start_date: formData.start_date || null,
        end_date: null, // يمكن حسابه من start_date + duration_days
        status: formData.status === 'Not Started' ? 'pending' :
                formData.status === 'In Progress' ? 'in_progress' :
                formData.status === 'Completed' ? 'completed' : 'pending',
        total_cost: null, // يمكن إضافة حقل التكلفة لاحقاً
        paid_amount: 0,
        remaining_amount: null,
        materials: {
          quantity_pvc: parseInt(formData.quantity_pvc) || null, // وحدات - رقم صحيح
          quantity_shutter: parseInt(formData.quantity_shutter) || null, // وحدات - رقم صحيح
          quantity_sgs: parseInt(formData.quantity_sgs) || null, // وحدات - رقم صحيح
          quantity_doors: parseInt(formData.quantity_doors) || null, // وحدات - رقم صحيح
          quantity_glass: parseFloat(formData.quantity_glass) || null, // متر مربع - رقم عشري
          duration_weeks: parseInt(formData.duration_weeks) || null,
          job_order: formData.job_order || null // حفظ رقم أمر العمل في materials مؤقتاً
        },
        notes: formData.notes || null,
      };

      // إضافة المشروع إلى Supabase
      const newProject = await addProject(projectData);
      console.log('Project added successfully to Supabase:', newProject);

      // تحديث معرف المشروع الحالي لرفع الملفات
      setCurrentProjectId(newProject.id);

      // رفع الملفات غير المرفوعة إلى Supabase Storage
      const unuploadedFiles = files.filter(file => !file.uploaded && file.file);
      if (unuploadedFiles.length > 0) {
        for (const fileData of unuploadedFiles) {
          try {
            const uploadResult = await uploadFileToStorage(fileData.file, newProject.id);
            if (uploadResult.success) {
              console.log(`File ${fileData.name} uploaded successfully:`, uploadResult.data);
            }
          } catch (fileError) {
            console.error(`Error uploading file ${fileData.name}:`, fileError);
            // لا نوقف العملية إذا فشل رفع ملف واحد
          }
        }
      }

      // عرض رسالة نجاح
      toast({
        title: t('addProject.successTitle') || "Project Added Successfully",
        description: t('addProject.successMessage', { projectName: formData.contract_number }) || `Project ${formData.contract_number} has been added.`,
        className: "bg-green-500 text-white dark:bg-green-600",
      });

      // إعادة تعيين النموذج
      setFormData({
        contract_number: '', job_order: '', client_name: '', city: '', start_date: '', duration_weeks: '',
        quantity_pvc: '', quantity_shutter: '', quantity_sgs: '', quantity_doors: '', quantity_glass: '',
        project_type: [], status: 'Not Started', notes: '', completion_date: null,
      });
      setFiles([]);
      setCurrentProjectId(null);

    } catch (error) {
      console.error('Error adding project:', error);
      toast({
        title: t('addProject.errorTitle') || "Error Adding Project",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: { y: 0, opacity: 1, transition: { type: 'spring', stiffness: 100 } },
  };

  return (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={{ visible: { transition: { staggerChildren: 0.05 } } }}
      className="p-4 md:p-6 max-w-4xl mx-auto"
      dir={i18n.dir()}
    >
      <motion.h1 variants={itemVariants} className="text-3xl font-bold text-gray-800 dark:text-white mb-8 gradient-text text-center">
        {t('sidebar.addProject')}
      </motion.h1>

      <Card className="shadow-xl dark:bg-gray-800/80 border-gray-200 dark:border-gray-700/50 backdrop-blur-md">
        <CardHeader>
          <CardTitle className="text-xl text-gray-700 dark:text-gray-200">{t('addProject.formTitle') || "New Project Details"}</CardTitle>
        </CardHeader>
        <form onSubmit={handleSubmit}>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
              <motion.div variants={itemVariants}>
                <Label htmlFor="contract_number" className="text-gray-700 dark:text-gray-300">{t('addProject.contractNumber') || "Contract Number"}</Label>
                <Input
                  id="contract_number"
                  name="contract_number"
                  value={formData.contract_number}
                  onChange={handleChange}
                  required
                  placeholder={t('addProject.contractNumberPlaceholder') || "Enter contract number"}
                  className="mt-1 dark:bg-gray-700 dark:border-gray-600 dark:text-white focus:ring-blue-500 dark:focus:ring-blue-400"
                />
              </motion.div>
              <motion.div variants={itemVariants}>
                <Label htmlFor="job_order" className="text-gray-700 dark:text-gray-300">{t('addProject.jobOrder') || "Job Order"}</Label>
                <Input
                  id="job_order"
                  name="job_order"
                  value={formData.job_order}
                  onChange={handleChange}
                  placeholder={t('addProject.jobOrderPlaceholder') || "Enter job order number"}
                  className="mt-1 dark:bg-gray-700 dark:border-gray-600 dark:text-white focus:ring-blue-500 dark:focus:ring-blue-400"
                />
              </motion.div>
              <motion.div variants={itemVariants}>
                <Label htmlFor="client_name" className="text-gray-700 dark:text-gray-300">{t('addProject.clientName') || "Client Name"}</Label>
                <Input id="client_name" name="client_name" value={formData.client_name} onChange={handleChange} required className="mt-1 dark:bg-gray-700 dark:border-gray-600 dark:text-white focus:ring-blue-500 dark:focus:ring-blue-400" />
              </motion.div>
              <motion.div variants={itemVariants}>
                <Label htmlFor="city" className="text-gray-700 dark:text-gray-300">{t('addProject.city') || "City"}</Label>
                <Input id="city" name="city" value={formData.city} onChange={handleChange} required className="mt-1 dark:bg-gray-700 dark:border-gray-600 dark:text-white focus:ring-blue-500 dark:focus:ring-blue-400" />
              </motion.div>
              <motion.div variants={itemVariants}>
                <Label htmlFor="start_date" className="text-gray-700 dark:text-gray-300">{t('addProject.startDate') || "Start Date"}</Label>
                <Input type="date" id="start_date" name="start_date" value={formData.start_date} onChange={handleChange} required className="mt-1 dark:bg-gray-700 dark:border-gray-600 dark:text-white focus:ring-blue-500 dark:focus:ring-blue-400" />
              </motion.div>
              <motion.div variants={itemVariants}>
                <Label htmlFor="duration_weeks" className="text-gray-700 dark:text-gray-300">{t('addProject.durationDays') || "Project Duration (Weeks)"}</Label>
                <Input type="number" id="duration_weeks" name="duration_weeks" value={formData.duration_weeks} onChange={handleChange} className="mt-1 dark:bg-gray-700 dark:border-gray-600 dark:text-white focus:ring-blue-500 dark:focus:ring-blue-400" />
              </motion.div>
               <motion.div variants={itemVariants}>
                <Label htmlFor="status" className="text-gray-700 dark:text-gray-300">{t('allProjects.status') || "Status"}</Label>
                <select id="status" name="status" value={formData.status} onChange={handleChange} className="mt-1 block w-full p-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm h-10">
                    <option value="Not Started">{t('dashboard.notStarted') || "Not Started"}</option>
                    <option value="In Progress">{t('dashboard.inProgress') || "In Progress"}</option>
                    <option value="Delayed">{t('dashboard.delayed') || "Delayed"}</option>
                    <option value="Completed">{t('dashboard.completed') || "Completed"}</option>
                 </select>
              </motion.div>
            </div>

            <motion.h3 variants={itemVariants} className="text-lg font-semibold text-gray-700 dark:text-gray-300 pt-4 border-t dark:border-gray-700/50">{t('addProject.quantities') || "Quantities"}</motion.h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-x-6 gap-y-4">
              {[
                { name: 'pvc', step: '1', unit: 'units' },
                { name: 'shutter', step: '1', unit: 'units' },
                { name: 'sgs', step: '1', unit: 'units' },
                { name: 'doors', step: '1', unit: 'units' },
                { name: 'glass', step: '1', unit: 'units' }
              ].map(item => (
                <motion.div variants={itemVariants} key={item.name}>
                  <Label htmlFor={`quantity_${item.name}`} className="text-gray-700 dark:text-gray-300">{t(`addProject.quantity_${item.name}`) || `${item.name.toUpperCase()} (${item.unit})`}</Label>
                  <Input
                    type="number"
                    step={item.step}
                    min="0"
                    id={`quantity_${item.name}`}
                    name={`quantity_${item.name}`}
                    value={formData[`quantity_${item.name}`]}
                    onChange={handleChange}
                    className="mt-1 dark:bg-gray-700 dark:border-gray-600 dark:text-white focus:ring-blue-500 dark:focus:ring-blue-400"
                  />
                </motion.div>
              ))}
            </div>

            <motion.h3 variants={itemVariants} className="text-lg font-semibold text-gray-700 dark:text-gray-300 pt-4 border-t dark:border-gray-700/50">{t('addProject.projectType') || "Project Type"}</motion.h3>
            <motion.div variants={itemVariants} className="flex flex-wrap gap-4">
              {[
                { id: 'project_type_new', value: 'New Installation', label: t('addProject.newInstallation') || "New Installation" },
                { id: 'project_type_maintenance', value: 'Maintenance', label: t('addProject.maintenance') || "Maintenance" }
              ].map(type => (
                <div key={type.id} className="flex items-center space-x-2 rtl:space-x-reverse">
                  <Checkbox
                    id={type.id}
                    checked={formData.project_type.includes(type.value)}
                    onCheckedChange={() => handleCheckboxChange(type.value)}
                    className="data-[state=checked]:bg-blue-600 dark:data-[state=checked]:bg-blue-500 border-gray-400 dark:border-gray-500"
                  />
                  <Label htmlFor={type.id} className="text-gray-700 dark:text-gray-300 font-normal">{type.label}</Label>
                </div>
              ))}
            </motion.div>

            <motion.div variants={itemVariants}>
              <Label htmlFor="notes" className="text-gray-700 dark:text-gray-300">{t('addProject.notes') || "Notes"}</Label>
              <Textarea id="notes" name="notes" value={formData.notes} onChange={handleChange} className="mt-1 dark:bg-gray-700 dark:border-gray-600 dark:text-white focus:ring-blue-500 dark:focus:ring-blue-400 min-h-[100px]" />
            </motion.div>

            <motion.div variants={itemVariants}>
              <Label className="text-gray-700 dark:text-gray-300 mb-2 block">
                {t('addProject.uploadFiles') || "Upload Files (PDF, Images)"}
              </Label>
              <FileUploadManager
                projectId={currentProjectId}
                files={files}
                onFilesChange={handleFilesChange}
                maxFiles={10}
                maxFileSize={10 * 1024 * 1024} // 10MB
                allowedTypes={['.pdf', '.jpg', '.jpeg', '.png', '.doc', '.docx']}
                showPreview={true}
                disabled={isSubmitting}
              />
            </motion.div>



          </CardContent>
          <CardFooter>
            <motion.div variants={itemVariants} className="w-full">
              <Button type="submit" className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white py-3 text-lg shadow-md hover:shadow-lg transition-shadow" disabled={isSubmitting}>
                <PlusCircle className={`h-5 w-5 ${i18n.language === 'ar' ? 'ml-2' : 'mr-2'}`} />
                {isSubmitting ? (t('addProject.submitting') || "Submitting...") : (t('addProject.submitButton') || "Add Project")}
              </Button>
            </motion.div>
          </CardFooter>
        </form>
      </Card>
    </motion.div>
  );
};

export default AddProjectPage;
