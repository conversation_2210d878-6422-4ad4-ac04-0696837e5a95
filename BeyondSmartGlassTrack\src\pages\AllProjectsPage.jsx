import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { motion } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import { useToast } from '@/components/ui/use-toast';
import ProjectPageHeader from '@/components/projects/ProjectPageHeader';
import ProjectSearchBar from '@/components/projects/ProjectSearchBar';
import ProjectTable from '@/components/projects/ProjectTable';
import DeleteProjectDialog from '@/components/projects/DeleteProjectDialog';
import { ListX, FileText, Download } from 'lucide-react';
import { getAllProjects, deleteProject } from '@/lib/supabaseDatabase';
import { calculateProgressForProjects } from '@/utils/progressCalculator';
import { Button } from '@/components/ui/button';
import jsPDF from 'jspdf';
import 'jspdf-autotable';
import * as XLSX from 'xlsx';
import { saveAs } from 'file-saver';

const AllProjectsPage = () => {
  const { t, i18n } = useTranslation();
  const [projects, setProjects] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(true);
  const [sortConfig, setSortConfig] = useState({ key: 'created_at', direction: 'descending' });
  const { toast } = useToast();
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [projectToDelete, setProjectToDelete] = useState(null);

  const fetchProjects = useCallback(async () => {
    setLoading(true);
    try {
      // الحصول على جميع المشاريع من قاعدة البيانات المحلية
      const data = await getAllProjects();

      // حساب نسبة التقدم لكل مشروع
      const projectsWithProgress = await calculateProgressForProjects(data);

      // ترتيب المشاريع حسب الإعدادات الحالية
      const sortedData = [...projectsWithProgress].sort((a, b) => {
        if (a[sortConfig.key] < b[sortConfig.key]) {
          return sortConfig.direction === 'ascending' ? -1 : 1;
        }
        if (a[sortConfig.key] > b[sortConfig.key]) {
          return sortConfig.direction === 'ascending' ? 1 : -1;
        }
        return 0;
      });

      setProjects(sortedData || []);
    } catch (error) {
      console.error('Error fetching projects:', error);
      toast({
        title: t('allProjects.fetchErrorTitle') || "Error",
        description: t('allProjects.fetchErrorMsg') || "Could not fetch projects.",
        variant: "destructive"
      });
      setProjects([]);
    } finally {
      setLoading(false);
    }
  }, [sortConfig.key, sortConfig.direction, toast, t]);

  useEffect(() => {
    fetchProjects();
  }, [fetchProjects]);

  const handleSearch = (event) => {
    setSearchTerm(event.target.value);
  };

  const requestSort = (key) => {
    let direction = 'ascending';
    if (sortConfig.key === key && sortConfig.direction === 'ascending') {
      direction = 'descending';
    }
    setSortConfig({ key, direction });
  };

  const filteredProjects = useMemo(() => {
    return projects.filter(project =>
      (project.project_number?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
      (project.client_name?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
      (project.city?.toLowerCase() || '').includes(searchTerm.toLowerCase())
    );
  }, [projects, searchTerm]);

  const confirmDeleteProject = (project) => {
    setProjectToDelete(project);
    setShowDeleteConfirm(true);
  };

  const handleDeleteProject = async () => {
    if (!projectToDelete) return;
    setLoading(true);

    try {
      // حذف المشروع من قاعدة البيانات المحلية
      await deleteProject(projectToDelete.id);

      toast({
        title: t('allProjects.deleteSuccessTitle') || "Project Deleted",
        description: t('allProjects.deleteSuccessMsg', { projectName: projectToDelete.project_number }) || `Project ${projectToDelete.project_number} has been deleted.`
      });

      // إعادة تحميل المشاريع مع حساب نسبة التقدم
      await fetchProjects();
    } catch (error) {
      console.error('Error deleting project:', error);
      toast({
        title: t('allProjects.deleteErrorTitle') || "Error Deleting Project",
        description: error.message || "Could not delete project.",
        variant: "destructive"
      });
    } finally {
      setShowDeleteConfirm(false);
      setProjectToDelete(null);
      setLoading(false);
    }
  };

  // وظيفة تصدير المشاريع بصيغة Excel
  const handleExportToExcel = () => {
    try {
      // تحضير البيانات
      const data = filteredProjects.map(project => ({
        'Project Number': project.project_number || '',
        'Client Name': project.client_name || '',
        'City': project.city || '',
        'Start Date': project.start_date ? new Date(project.start_date).toLocaleDateString() : '',
        'Status': project.status || '',
        'Progress %': project.progress_percentage || '0',
        'PVC (units)': project.quantity_pvc || '0',
        'Shutter (units)': project.quantity_shutter || '0',
        'SGS (units)': project.quantity_sgs || '0',
        'Doors (units)': project.quantity_doors || '0',
        'Glass (units)': project.quantity_glass || '0'
      }));

      // إنشاء ورقة عمل
      const worksheet = XLSX.utils.json_to_sheet(data);

      // إنشاء مصنف عمل
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Projects');

      // تصدير المصنف
      const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
      const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      saveAs(blob, `Projects_Report_${new Date().toISOString().split('T')[0]}.xlsx`);

      toast({
        title: "Excel Export",
        description: "Excel file has been generated successfully.",
        className: "bg-green-500 text-white dark:bg-green-600"
      });
    } catch (error) {
      console.error('Error exporting to Excel:', error);
      toast({
        title: "Export Error",
        description: "Could not generate Excel file. Please try again.",
        variant: "destructive"
      });
    }
  };

  // وظيفة تصدير المشاريع بصيغة PDF
  const handleExportToPDF = () => {
    try {
      // إنشاء مستند PDF جديد
      const doc = new jsPDF('landscape');

      // إضافة عنوان التقرير
      doc.setFontSize(18);
      doc.text("Projects Report", doc.internal.pageSize.getWidth() / 2, 15, { align: 'center' });

      // إضافة التاريخ
      doc.setFontSize(10);
      const today = new Date().toLocaleDateString();
      doc.text(`Date: ${today}`, doc.internal.pageSize.getWidth() - 20, 10, { align: 'right' });

      // تحضير البيانات للجدول
      const tableData = filteredProjects.map(project => [
        project.project_number || '',
        project.client_name || '',
        project.city || '',
        project.start_date ? new Date(project.start_date).toLocaleDateString() : '',
        project.status || '',
        `${project.progress_percentage || 0}%`,
        project.quantity_pvc || '0',
        project.quantity_shutter || '0',
        project.quantity_sgs || '0',
        project.quantity_doors || '0',
        project.quantity_glass || '0'
      ]);

      // تحديد رؤوس الأعمدة
      const headers = [
        [
          'Project Number',
          'Client Name',
          'City',
          'Start Date',
          'Status',
          'Progress %',
          'PVC (units)',
          'Shutter (units)',
          'SGS (units)',
          'Doors (units)',
          'Glass (units)'
        ]
      ];

      // إنشاء الجدول
      doc.autoTable({
        head: headers,
        body: tableData,
        startY: 25,
        theme: 'grid',
        styles: {
          fontSize: 8,
          cellPadding: 2,
          overflow: 'linebreak'
        },
        headStyles: {
          fillColor: [41, 128, 185],
          textColor: 255,
          fontStyle: 'bold'
        },
        alternateRowStyles: {
          fillColor: [240, 240, 240]
        }
      });

      // إضافة ترويسة وتذييل
      const pageCount = doc.internal.getNumberOfPages();
      for (let i = 1; i <= pageCount; i++) {
        doc.setPage(i);
        // تذييل الصفحة
        doc.setFontSize(8);
        doc.text(
          `Page ${i} of ${pageCount}`,
          doc.internal.pageSize.getWidth() / 2,
          doc.internal.pageSize.getHeight() - 10,
          { align: 'center' }
        );
        // ترويسة الصفحة - اسم التطبيق
        doc.text('SultanTrack', 20, 10);
      }

      // حفظ الملف
      doc.save(`Projects_Report_${new Date().toISOString().split('T')[0]}.pdf`);

      toast({
        title: "PDF Export",
        description: "PDF file has been generated successfully.",
        className: "bg-green-500 text-white dark:bg-green-600"
      });
    } catch (error) {
      console.error('Error exporting to PDF:', error);
      toast({
        title: "Export Error",
        description: "Could not generate PDF file. Please try again.",
        variant: "destructive"
      });
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: { y: 0, opacity: 1, transition: { type: 'spring', stiffness: 100 } },
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1, transition: { staggerChildren: 0.05 } },
  };

  return (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={containerVariants}
      className="p-4 md:p-6"
      dir={i18n.dir()}
    >
      <ProjectPageHeader itemVariants={itemVariants} />

      <motion.div variants={itemVariants} className="flex flex-col sm:flex-row justify-between items-center mb-6 gap-4">
        <ProjectSearchBar searchTerm={searchTerm} handleSearch={handleSearch} itemVariants={null} />

        <div className="flex gap-2">
          <Button
            onClick={handleExportToPDF}
            variant="outline"
            className="flex items-center gap-2 border-red-500 text-red-500 hover:bg-red-500 hover:text-white"
          >
            <FileText className="h-4 w-4" />
            Export PDF
          </Button>

          <Button
            onClick={handleExportToExcel}
            variant="outline"
            className="flex items-center gap-2 border-green-500 text-green-500 hover:bg-green-500 hover:text-white"
          >
            <Download className="h-4 w-4" />
            Export Excel
          </Button>
        </div>
      </motion.div>

      {loading ? (
        <motion.p variants={itemVariants} className="text-center text-gray-500 dark:text-gray-400 py-10 text-lg">{t('allProjects.loading') || "Loading projects..."}</motion.p>
      ) : (
        filteredProjects.length > 0 ? (
          <ProjectTable
            projects={filteredProjects}
            sortConfig={sortConfig}
            requestSort={requestSort}
            confirmDeleteProject={confirmDeleteProject}
            itemVariants={itemVariants}
          />
        ) : (
          <motion.div variants={itemVariants} className="text-center text-gray-500 dark:text-gray-400 py-10 bg-white dark:bg-gray-800/80 shadow-xl rounded-lg backdrop-blur-md border border-gray-200 dark:border-gray-700/50">
            <div className="flex flex-col items-center justify-center">
              <ListX size={48} className="mb-2 text-gray-400 dark:text-gray-500" />
              <p className="text-lg">{t('allProjects.noProjectsFound') || "No projects found."}</p>
              <p className="text-sm">{searchTerm ? (t('allProjects.noProjectsMatchSearch') || "Try adjusting your search term.") : (t('allProjects.tryAddingProjects') || "Try adding some projects to see them here.")}</p>
            </div>
          </motion.div>
        )
      )}

      <DeleteProjectDialog
        isOpen={showDeleteConfirm}
        onClose={() => setShowDeleteConfirm(false)}
        onConfirm={handleDeleteProject}
        projectToDelete={projectToDelete}
      />
    </motion.div>
  );
};

export default AllProjectsPage;