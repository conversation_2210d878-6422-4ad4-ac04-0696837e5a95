import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import { useToast } from '@/components/ui/use-toast';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  Plus, 
  Search, 
  Filter, 
  FileText, 
  Edit, 
  Trash2, 
  Eye,
  CheckCircle,
  Clock,
  AlertCircle,
  XCircle,
  DollarSign
} from 'lucide-react';
import {
  getAllContracts,
  deleteContract,
  updateContractStatus,
  updatePaymentStatus,
  searchContracts,
  getContractsByStatus
} from '@/lib/contractsService';
import ContractModal from '@/components/contracts/ContractModal';
import DeleteConfirmDialog from '@/components/ui/DeleteConfirmDialog';

const ContractsPage = () => {
  const { t } = useTranslation();
  const { toast } = useToast();
  
  const [contracts, setContracts] = useState([]);
  const [filteredContracts, setFilteredContracts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedContract, setSelectedContract] = useState(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [contractToDelete, setContractToDelete] = useState(null);

  useEffect(() => {
    loadContracts();
  }, []);

  useEffect(() => {
    filterContracts();
  }, [contracts, searchTerm, statusFilter]);

  const loadContracts = async () => {
    try {
      setLoading(true);
      const data = await getAllContracts();
      setContracts(data);
    } catch (error) {
      console.error('Error loading contracts:', error);
      toast({
        title: t('common.error'),
        description: 'Failed to load contracts',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  const filterContracts = () => {
    let filtered = contracts;

    // Filter by status
    if (statusFilter !== 'all') {
      filtered = filtered.filter(c => c.contract_status === statusFilter);
    }

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(c =>
        c.contract_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
        c.quotations?.client_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        c.quotations?.quotation_number.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    setFilteredContracts(filtered);
  };

  const handleAddContract = () => {
    setSelectedContract(null);
    setIsModalOpen(true);
  };

  const handleEditContract = (contract) => {
    setSelectedContract(contract);
    setIsModalOpen(true);
  };

  const handleDeleteContract = (contract) => {
    setContractToDelete(contract);
    setDeleteDialogOpen(true);
  };

  const confirmDelete = async () => {
    try {
      await deleteContract(contractToDelete.id);
      toast({
        title: t('common.success'),
        description: 'Contract deleted successfully',
        className: 'bg-green-500 text-white'
      });
      loadContracts();
    } catch (error) {
      toast({
        title: t('common.error'),
        description: 'Failed to delete contract',
        variant: 'destructive'
      });
    } finally {
      setDeleteDialogOpen(false);
      setContractToDelete(null);
    }
  };

  const handleStatusChange = async (contractId, newStatus) => {
    try {
      await updateContractStatus(contractId, newStatus);
      toast({
        title: t('common.success'),
        description: 'Contract status updated successfully',
        className: 'bg-green-500 text-white'
      });
      loadContracts();
    } catch (error) {
      toast({
        title: t('common.error'),
        description: 'Failed to update contract status',
        variant: 'destructive'
      });
    }
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      signed: { color: 'bg-blue-500', icon: FileText, text: t('contracts.signed') },
      in_progress: { color: 'bg-yellow-500', icon: Clock, text: t('contracts.inProgress') },
      completed: { color: 'bg-green-500', icon: CheckCircle, text: t('contracts.completed') },
      cancelled: { color: 'bg-red-500', icon: XCircle, text: t('contracts.cancelled') }
    };

    const config = statusConfig[status] || statusConfig.signed;
    const Icon = config.icon;

    return (
      <Badge className={`${config.color} text-white`}>
        <Icon size={12} className="mr-1" />
        {config.text}
      </Badge>
    );
  };

  const getPaymentBadge = (paymentStatus) => {
    const statusConfig = {
      paid: { color: 'bg-green-500', text: t('contracts.paid') },
      partial: { color: 'bg-yellow-500', text: t('contracts.partial') },
      unpaid: { color: 'bg-red-500', text: t('contracts.unpaid') }
    };

    const config = statusConfig[paymentStatus] || statusConfig.unpaid;

    return (
      <Badge className={`${config.color} text-white`}>
        <DollarSign size={12} className="mr-1" />
        {config.text}
      </Badge>
    );
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount);
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">{t('common.loading')}</div>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="space-y-6"
    >
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            {t('contracts.title')}
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Manage smart glass contracts and agreements
          </p>
        </div>
        <Button onClick={handleAddContract} className="bg-blue-600 hover:bg-blue-700">
          <Plus size={20} className="mr-2" />
          Add Contract
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <Input
                  placeholder="Search contracts..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">All Status</option>
                <option value="signed">Signed</option>
                <option value="in_progress">In Progress</option>
                <option value="completed">Completed</option>
                <option value="cancelled">Cancelled</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Contracts List */}
      <div className="grid gap-4">
        {filteredContracts.length === 0 ? (
          <Card>
            <CardContent className="p-8 text-center">
              <FileText size={48} className="mx-auto text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                No contracts found
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                {searchTerm || statusFilter !== 'all' 
                  ? 'No contracts match your filters' 
                  : 'Get started by creating your first contract'
                }
              </p>
              {!searchTerm && statusFilter === 'all' && (
                <Button onClick={handleAddContract} className="bg-blue-600 hover:bg-blue-700">
                  <Plus size={20} className="mr-2" />
                  Add Contract
                </Button>
              )}
            </CardContent>
          </Card>
        ) : (
          filteredContracts.map((contract) => (
            <Card key={contract.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex flex-col lg:flex-row justify-between items-start gap-4">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-3">
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                        {contract.contract_number}
                      </h3>
                      {getStatusBadge(contract.contract_status)}
                      {getPaymentBadge(contract.payment_status)}
                    </div>
                    
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
                      <div>
                        <span className="text-gray-500">Client:</span>
                        <p className="font-medium">{contract.quotations?.client_name || 'N/A'}</p>
                      </div>
                      <div>
                        <span className="text-gray-500">Quotation:</span>
                        <p className="font-medium">{contract.quotations?.quotation_number || 'N/A'}</p>
                      </div>
                      <div>
                        <span className="text-gray-500">Contract Value:</span>
                        <p className="font-medium text-green-600">
                          {formatCurrency(contract.contract_value)}
                        </p>
                      </div>
                      <div>
                        <span className="text-gray-500">Remaining:</span>
                        <p className="font-medium text-red-600">
                          {formatCurrency(contract.remaining_amount)}
                        </p>
                      </div>
                      <div>
                        <span className="text-gray-500">Signed Date:</span>
                        <p className="font-medium">{formatDate(contract.signed_date)}</p>
                      </div>
                      <div>
                        <span className="text-gray-500">Start Date:</span>
                        <p className="font-medium">{formatDate(contract.start_date)}</p>
                      </div>
                      <div>
                        <span className="text-gray-500">Expected Completion:</span>
                        <p className="font-medium">{formatDate(contract.expected_completion)}</p>
                      </div>
                      <div>
                        <span className="text-gray-500">Item Type:</span>
                        <p className="font-medium">{contract.quotations?.item_type || 'N/A'}</p>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleEditContract(contract)}
                    >
                      <Edit size={16} />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDeleteContract(contract)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 size={16} />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* Contract Modal */}
      <ContractModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        contract={selectedContract}
        onSave={loadContracts}
      />

      {/* Delete Confirmation Dialog */}
      <DeleteConfirmDialog
        isOpen={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
        onConfirm={confirmDelete}
        title="Delete Contract"
        description={`Are you sure you want to delete contract ${contractToDelete?.contract_number}? This action cannot be undone.`}
      />
    </motion.div>
  );
};

export default ContractsPage;
