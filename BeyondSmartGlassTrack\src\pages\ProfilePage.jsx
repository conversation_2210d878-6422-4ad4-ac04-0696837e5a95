import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from '@/components/ui/use-toast';
import { useTranslation } from 'react-i18next';
import { 
  User, 
  Mail, 
  Phone, 
  MapPin, 
  Calendar, 
  Shield, 
  Edit3, 
  Save, 
  X,
  Camera,
  Building,
  Clock
} from 'lucide-react';
import { supabase } from '@/lib/supabase';

const ProfilePage = () => {
  const { t, i18n } = useTranslation();
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);
  const [editing, setEditing] = useState(false);
  const [saving, setSaving] = useState(false);
  const [userProfile, setUserProfile] = useState({
    name: '',
    email: '',
    phone: '',
    role: '',
    department: '',
    city: '',
    bio: '',
    avatar_url: '',
    created_at: '',
    last_login: ''
  });
  const [editForm, setEditForm] = useState({});

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: { y: 0, opacity: 1, transition: { type: 'spring', stiffness: 100 } },
  };

  // جلب بيانات المستخدم
  useEffect(() => {
    fetchUserProfile();
  }, []);

  const fetchUserProfile = async () => {
    setLoading(true);
    try {
      // إذا لم يكن هناك مستخدم مسجل دخول، استخدم بيانات افتراضية
      const { data: { user }, error: authError } = await supabase.auth.getUser();

      if (authError) {
        console.log('Auth error:', authError);
        // استخدام بيانات افتراضية للاختبار
        const defaultProfile = {
          name: 'مستخدم تجريبي',
          email: '<EMAIL>',
          phone: '',
          role: localStorage.getItem('userRole') || 'manager',
          department: '',
          city: '',
          bio: '',
          avatar_url: '',
          created_at: new Date().toISOString(),
          last_login: new Date().toISOString()
        };
        setUserProfile(defaultProfile);
        setEditForm(defaultProfile);
        setLoading(false);
        return;
      }

      if (user) {
        // جلب البيانات من جدول المستخدمين
        const { data: profileData, error: profileError } = await supabase
          .from('users')
          .select('*')
          .eq('email', user.email)
          .single();

        if (profileError && profileError.code !== 'PGRST116') {
          console.log('Profile error:', profileError);
        }

        const profile = {
          name: profileData?.name || user.user_metadata?.name || 'مستخدم',
          email: user.email || '',
          phone: profileData?.phone || '',
          role: profileData?.role || user.user_metadata?.role || localStorage.getItem('userRole') || 'manager',
          department: profileData?.department || '',
          city: profileData?.city || '',
          bio: profileData?.bio || '',
          avatar_url: profileData?.avatar_url || '',
          created_at: profileData?.created_at || user.created_at,
          last_login: user.last_sign_in_at || ''
        };

        setUserProfile(profile);
        setEditForm(profile);
      } else {
        // استخدام بيانات افتراضية إذا لم يكن هناك مستخدم
        const defaultProfile = {
          name: 'مستخدم تجريبي',
          email: '<EMAIL>',
          phone: '',
          role: localStorage.getItem('userRole') || 'manager',
          department: '',
          city: '',
          bio: '',
          avatar_url: '',
          created_at: new Date().toISOString(),
          last_login: new Date().toISOString()
        };
        setUserProfile(defaultProfile);
        setEditForm(defaultProfile);
      }
    } catch (error) {
      console.error('Error fetching profile:', error);
      // استخدام بيانات افتراضية في حالة الخطأ
      const defaultProfile = {
        name: 'مستخدم تجريبي',
        email: '<EMAIL>',
        phone: '',
        role: localStorage.getItem('userRole') || 'manager',
        department: '',
        city: '',
        bio: '',
        avatar_url: '',
        created_at: new Date().toISOString(),
        last_login: new Date().toISOString()
      };
      setUserProfile(defaultProfile);
      setEditForm(defaultProfile);
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = () => {
    setEditing(true);
    setEditForm({ ...userProfile });
  };

  const handleCancel = () => {
    setEditing(false);
    setEditForm({ ...userProfile });
  };

  const handleSave = async () => {
    setSaving(true);
    try {
      // تحديث البيانات محلياً
      setUserProfile({ ...editForm });
      setEditing(false);

      // تحديث الدور في localStorage
      localStorage.setItem('userRole', editForm.role);

      // محاولة تحديث قاعدة البيانات إذا كان المستخدم مسجل دخول
      try {
        const { data: { user }, error: authError } = await supabase.auth.getUser();

        if (!authError && user) {
          // تحديث البيانات في جدول المستخدمين
          const { error: updateError } = await supabase
            .from('users')
            .upsert({
              email: user.email,
              name: editForm.name,
              phone: editForm.phone,
              department: editForm.department,
              city: editForm.city,
              bio: editForm.bio,
              role: editForm.role,
              updated_at: new Date().toISOString()
            }, {
              onConflict: 'email'
            });

          if (updateError) {
            console.log('Database update error:', updateError);
          }

          // تحديث metadata في Auth
          const { error: metadataError } = await supabase.auth.updateUser({
            data: {
              name: editForm.name,
              role: editForm.role
            }
          });

          if (metadataError) {
            console.log('Metadata update error:', metadataError);
          }
        }
      } catch (dbError) {
        console.log('Database operation failed:', dbError);
      }

      toast({
        title: t('profile.updateSuccessTitle') || "تم التحديث بنجاح",
        description: t('profile.updateSuccessMsg') || "تم تحديث بيانات الملف الشخصي بنجاح.",
        className: "bg-green-500 text-white dark:bg-green-600"
      });
    } catch (error) {
      console.error('Error updating profile:', error);
      toast({
        title: t('profile.updateErrorTitle') || "خطأ في التحديث",
        description: error.message,
        variant: "destructive"
      });
    } finally {
      setSaving(false);
    }
  };

  const handleInputChange = (field, value) => {
    setEditForm(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const formatDate = (dateString) => {
    if (!dateString) return t('common.notSpecified') || 'غير محدد';
    return new Date(dateString).toLocaleDateString(i18n.language === 'ar' ? 'ar-SA' : 'en-US');
  };

  const getRoleIcon = (role) => {
    switch (role) {
      case 'manager': return '👑';
      case 'supervisor': return '👨‍💼';
      case 'data_entry': return '📝';
      case 'technician': return '🔧';
      default: return '👤';
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-4 border-b-4 border-blue-500"></div>
      </div>
    );
  }

  return (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={{ visible: { transition: { staggerChildren: 0.1 } } }}
      className="p-4 md:p-6 max-w-4xl mx-auto"
      dir={i18n.dir()}
    >
      {/* Header */}
      <motion.div variants={itemVariants} className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8 gap-4">
        <div className="flex items-center gap-4">
          <div className="relative">
            <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white text-2xl font-bold">
              {userProfile.name ? userProfile.name.charAt(0).toUpperCase() : '👤'}
            </div>
            <Button
              size="icon"
              variant="outline"
              className="absolute -bottom-1 -right-1 h-6 w-6 rounded-full bg-white dark:bg-gray-800"
              onClick={() => {
                toast({
                  title: t('profile.avatarUploadTitle') || "رفع الصورة",
                  description: t('profile.avatarUploadMsg') || "ميزة رفع الصورة ستكون متاحة قريباً",
                  className: "bg-blue-500 text-white dark:bg-blue-600"
                });
              }}
            >
              <Camera className="h-3 w-3" />
            </Button>
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-800 dark:text-white">
              {t('profile.title') || 'الملف الشخصي'}
            </h1>
            <p className="text-gray-600 dark:text-gray-400 flex items-center gap-2">
              {getRoleIcon(userProfile.role)}
              {t(`userManagement.roles.${userProfile.role}`) || userProfile.role}
            </p>
          </div>
        </div>
        
        {!editing ? (
          <Button onClick={handleEdit} className="bg-blue-500 hover:bg-blue-600 text-white">
            <Edit3 className={`h-4 w-4 ${i18n.language === 'ar' ? 'ml-2' : 'mr-2'}`} />
            {t('profile.editButton') || 'تعديل'}
          </Button>
        ) : (
          <div className="flex gap-2">
            <Button onClick={handleSave} disabled={saving} className="bg-green-500 hover:bg-green-600 text-white">
              {saving ? (
                <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
              ) : (
                <Save className={`h-4 w-4 ${i18n.language === 'ar' ? 'ml-2' : 'mr-2'}`} />
              )}
              {t('profile.saveButton') || 'حفظ'}
            </Button>
            <Button onClick={handleCancel} variant="outline">
              <X className={`h-4 w-4 ${i18n.language === 'ar' ? 'ml-2' : 'mr-2'}`} />
              {t('profile.cancelButton') || 'إلغاء'}
            </Button>
          </div>
        )}
      </motion.div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* معلومات شخصية */}
        <motion.div variants={itemVariants} className="lg:col-span-2">
          <Card className="shadow-lg dark:bg-gray-800">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5 text-blue-500" />
                {t('profile.personalInfo') || 'المعلومات الشخصية'}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="name">{t('profile.fullName') || 'الاسم الكامل'}</Label>
                  {editing ? (
                    <Input
                      id="name"
                      value={editForm.name}
                      onChange={(e) => handleInputChange('name', e.target.value)}
                      placeholder={t('profile.fullNamePlaceholder') || 'أدخل الاسم الكامل'}
                    />
                  ) : (
                    <p className="text-gray-700 dark:text-gray-300 p-2 bg-gray-50 dark:bg-gray-700 rounded">
                      {userProfile.name || t('common.notSpecified')}
                    </p>
                  )}
                </div>

                <div>
                  <Label htmlFor="email">{t('profile.email') || 'البريد الإلكتروني'}</Label>
                  <div className="flex items-center gap-2">
                    <Mail className="h-4 w-4 text-gray-500" />
                    <p className="text-gray-700 dark:text-gray-300 p-2 bg-gray-50 dark:bg-gray-700 rounded flex-1">
                      {userProfile.email}
                    </p>
                  </div>
                </div>

                <div>
                  <Label htmlFor="phone">{t('profile.phone') || 'رقم الهاتف'}</Label>
                  {editing ? (
                    <Input
                      id="phone"
                      value={editForm.phone}
                      onChange={(e) => handleInputChange('phone', e.target.value)}
                      placeholder={t('profile.phonePlaceholder') || 'أدخل رقم الهاتف'}
                    />
                  ) : (
                    <div className="flex items-center gap-2">
                      <Phone className="h-4 w-4 text-gray-500" />
                      <p className="text-gray-700 dark:text-gray-300 p-2 bg-gray-50 dark:bg-gray-700 rounded flex-1">
                        {userProfile.phone || t('common.notSpecified')}
                      </p>
                    </div>
                  )}
                </div>

                <div>
                  <Label htmlFor="city">{t('profile.city') || 'المدينة'}</Label>
                  {editing ? (
                    <Input
                      id="city"
                      value={editForm.city}
                      onChange={(e) => handleInputChange('city', e.target.value)}
                      placeholder={t('profile.cityPlaceholder') || 'أدخل المدينة'}
                    />
                  ) : (
                    <div className="flex items-center gap-2">
                      <MapPin className="h-4 w-4 text-gray-500" />
                      <p className="text-gray-700 dark:text-gray-300 p-2 bg-gray-50 dark:bg-gray-700 rounded flex-1">
                        {userProfile.city || t('common.notSpecified')}
                      </p>
                    </div>
                  )}
                </div>

                <div>
                  <Label htmlFor="department">{t('profile.department') || 'القسم'}</Label>
                  {editing ? (
                    <Input
                      id="department"
                      value={editForm.department}
                      onChange={(e) => handleInputChange('department', e.target.value)}
                      placeholder={t('profile.departmentPlaceholder') || 'أدخل القسم'}
                    />
                  ) : (
                    <div className="flex items-center gap-2">
                      <Building className="h-4 w-4 text-gray-500" />
                      <p className="text-gray-700 dark:text-gray-300 p-2 bg-gray-50 dark:bg-gray-700 rounded flex-1">
                        {userProfile.department || t('common.notSpecified')}
                      </p>
                    </div>
                  )}
                </div>

                <div>
                  <Label htmlFor="role">{t('profile.role') || 'الدور'}</Label>
                  <div className="flex items-center gap-2">
                    <Shield className="h-4 w-4 text-gray-500" />
                    <p className="text-gray-700 dark:text-gray-300 p-2 bg-gray-50 dark:bg-gray-700 rounded flex-1">
                      {getRoleIcon(userProfile.role)} {t(`userManagement.roles.${userProfile.role}`) || userProfile.role}
                    </p>
                  </div>
                </div>
              </div>

              <div>
                <Label htmlFor="bio">{t('profile.bio') || 'نبذة شخصية'}</Label>
                {editing ? (
                  <Textarea
                    id="bio"
                    value={editForm.bio}
                    onChange={(e) => handleInputChange('bio', e.target.value)}
                    placeholder={t('profile.bioPlaceholder') || 'أدخل نبذة شخصية...'}
                    rows={3}
                  />
                ) : (
                  <p className="text-gray-700 dark:text-gray-300 p-3 bg-gray-50 dark:bg-gray-700 rounded min-h-[80px]">
                    {userProfile.bio || t('profile.noBio') || 'لم يتم إضافة نبذة شخصية بعد.'}
                  </p>
                )}
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* معلومات الحساب */}
        <motion.div variants={itemVariants}>
          <Card className="shadow-lg dark:bg-gray-800">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5 text-green-500" />
                {t('profile.accountInfo') || 'معلومات الحساب'}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  {t('profile.memberSince') || 'عضو منذ'}
                </Label>
                <div className="flex items-center gap-2 mt-1">
                  <Calendar className="h-4 w-4 text-gray-500" />
                  <p className="text-gray-700 dark:text-gray-300">
                    {formatDate(userProfile.created_at)}
                  </p>
                </div>
              </div>

              <div>
                <Label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  {t('profile.lastLogin') || 'آخر تسجيل دخول'}
                </Label>
                <div className="flex items-center gap-2 mt-1">
                  <Clock className="h-4 w-4 text-gray-500" />
                  <p className="text-gray-700 dark:text-gray-300">
                    {formatDate(userProfile.last_login)}
                  </p>
                </div>
              </div>

              <div className="pt-4 border-t border-gray-200 dark:border-gray-600">
                <Label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  {t('profile.accountStatus') || 'حالة الحساب'}
                </Label>
                <div className="flex items-center gap-2 mt-1">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <p className="text-green-600 dark:text-green-400 font-medium">
                    {t('profile.active') || 'نشط'}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </motion.div>
  );
};

export default ProfilePage;
