import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/components/ui/use-toast';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  ArrowLeft,
  Edit,
  Save,
  X,
  FileText,
  Calendar,
  MapPin,
  User,
  Clock,
  Package
} from 'lucide-react';
import { getProjectById, updateProject } from '@/lib/supabaseDatabase';
import { getProjectFiles } from '@/lib/fileService';
import FileUploadManager from '@/components/FileUpload/FileUploadManager';

const ProjectDetailsPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { toast } = useToast();

  const [project, setProject] = useState(null);
  const [files, setFiles] = useState([]);
  const [isEditing, setIsEditing] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [formData, setFormData] = useState({});

  // تحميل بيانات المشروع والملفات
  useEffect(() => {
    const loadProjectData = async () => {
      try {
        setIsLoading(true);

        // تحميل بيانات المشروع
        const projectResult = await getProjectById(id);
        if (projectResult.success && projectResult.data) {
          setProject(projectResult.data);
          setFormData({
            client_name: projectResult.data.client_name || '',
            city: projectResult.data.city || '',
            start_date: projectResult.data.start_date || '',
            duration_weeks: projectResult.data.materials?.duration_weeks || '',
            quantity_pvc: projectResult.data.materials?.quantity_pvc || '',
            quantity_shutter: projectResult.data.materials?.quantity_shutter || '',
            quantity_sgs: projectResult.data.materials?.quantity_sgs || '',
            quantity_doors: projectResult.data.materials?.quantity_doors || '',
            quantity_glass: projectResult.data.materials?.quantity_glass || '',
            manufacturing_number: projectResult.data.materials?.manufacturing_number || '',
            status: projectResult.data.status || 'Not Started',
            notes: projectResult.data.notes || ''
          });

          // تحميل ملفات المشروع
          const filesResult = await getProjectFiles(id);
          if (filesResult.success) {
            const formattedFiles = filesResult.data.map(file => ({
              id: file.id,
              name: file.file_name,
              size: file.file_size,
              type: file.file_type,
              url: file.file_url,
              bucket: file.bucket_name,
              path: file.file_path,
              uploaded: true,
              uploadedAt: file.created_at
            }));
            setFiles(formattedFiles);
          }
        } else {
          toast({
            title: "Error",
            description: "Project not found",
            variant: "destructive"
          });
          navigate('/all-projects');
        }
      } catch (error) {
        console.error('Error loading project:', error);
        toast({
          title: "Error",
          description: "Failed to load project details",
          variant: "destructive"
        });
      } finally {
        setIsLoading(false);
      }
    };

    if (id) {
      loadProjectData();
    }
  }, [id, navigate, toast]);

  // معالجة تغيير البيانات
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // معالجة حفظ التعديلات
  const handleSave = async () => {
    try {
      setIsSaving(true);

      const updatedData = {
        client_name: formData.client_name,
        city: formData.city,
        start_date: formData.start_date,
        status: formData.status,
        notes: formData.notes,
        materials: {
          ...project.materials,
          duration_weeks: parseInt(formData.duration_weeks) || null,
          quantity_pvc: parseFloat(formData.quantity_pvc) || null,
          quantity_shutter: parseFloat(formData.quantity_shutter) || null,
          quantity_sgs: parseFloat(formData.quantity_sgs) || null,
          quantity_doors: parseFloat(formData.quantity_doors) || null,
          quantity_glass: parseFloat(formData.quantity_glass) || null,
          manufacturing_number: formData.manufacturing_number || null
        }
      };

      const result = await updateProject(id, updatedData);

      if (result.success) {
        setProject(result.data);
        setIsEditing(false);
        toast({
          title: "Success",
          description: "Project updated successfully",
          className: "bg-green-500 text-white"
        });
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      console.error('Error updating project:', error);
      toast({
        title: "Error",
        description: "Failed to update project",
        variant: "destructive"
      });
    } finally {
      setIsSaving(false);
    }
  };

  // معالجة تغيير الملفات
  const handleFilesChange = (newFiles) => {
    setFiles(newFiles);
  };

  // تنسيق التاريخ
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString();
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (!project) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-4">
            Project Not Found
          </h2>
          <Button onClick={() => navigate('/all-projects')}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Projects
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            onClick={() => navigate('/all-projects')}
            className="flex items-center"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Projects
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">
              {project.project_number}
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              {project.client_name}
            </p>
          </div>
        </div>

        <div className="flex space-x-2">
          {isEditing ? (
            <>
              <Button
                variant="outline"
                onClick={() => setIsEditing(false)}
                disabled={isSaving}
              >
                <X className="h-4 w-4 mr-2" />
                Cancel
              </Button>
              <Button
                onClick={handleSave}
                disabled={isSaving}
                className="bg-green-600 hover:bg-green-700"
              >
                <Save className="h-4 w-4 mr-2" />
                {isSaving ? 'Saving...' : 'Save Changes'}
              </Button>
            </>
          ) : (
            <Button
              onClick={() => setIsEditing(true)}
              className="bg-blue-600 hover:bg-blue-700"
            >
              <Edit className="h-4 w-4 mr-2" />
              Edit Project
            </Button>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* معلومات المشروع الأساسية */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <FileText className="h-5 w-5 mr-2" />
              Project Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Project Number
                </Label>
                <p className="text-lg font-semibold">{project.project_number}</p>
              </div>

              <div>
                <Label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Manufacturing Number
                </Label>
                {isEditing ? (
                  <Input
                    name="manufacturing_number"
                    value={formData.manufacturing_number}
                    onChange={handleInputChange}
                    placeholder="Enter manufacturing number"
                  />
                ) : (
                  <p className="text-lg">{project.materials?.manufacturing_number || 'N/A'}</p>
                )}
              </div>

              <div>
                <Label className="text-sm font-medium text-gray-600 dark:text-gray-400 flex items-center">
                  <User className="h-4 w-4 mr-1" />
                  Client Name
                </Label>
                {isEditing ? (
                  <Input
                    name="client_name"
                    value={formData.client_name}
                    onChange={handleInputChange}
                    placeholder="Enter client name"
                  />
                ) : (
                  <p className="text-lg">{project.client_name}</p>
                )}
              </div>

              <div>
                <Label className="text-sm font-medium text-gray-600 dark:text-gray-400 flex items-center">
                  <MapPin className="h-4 w-4 mr-1" />
                  City
                </Label>
                {isEditing ? (
                  <Input
                    name="city"
                    value={formData.city}
                    onChange={handleInputChange}
                    placeholder="Enter city"
                  />
                ) : (
                  <p className="text-lg">{project.city}</p>
                )}
              </div>

              <div>
                <Label className="text-sm font-medium text-gray-600 dark:text-gray-400 flex items-center">
                  <Calendar className="h-4 w-4 mr-1" />
                  Start Date
                </Label>
                {isEditing ? (
                  <Input
                    type="date"
                    name="start_date"
                    value={formData.start_date}
                    onChange={handleInputChange}
                  />
                ) : (
                  <p className="text-lg">{formatDate(project.start_date)}</p>
                )}
              </div>

              <div>
                <Label className="text-sm font-medium text-gray-600 dark:text-gray-400 flex items-center">
                  <Clock className="h-4 w-4 mr-1" />
                  Duration (Weeks)
                </Label>
                {isEditing ? (
                  <Input
                    type="number"
                    name="duration_weeks"
                    value={formData.duration_weeks}
                    onChange={handleInputChange}
                    placeholder="Enter duration in weeks"
                  />
                ) : (
                  <p className="text-lg">{project.materials?.duration_weeks || 'N/A'} weeks</p>
                )}
              </div>
            </div>

            <div>
              <Label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Status
              </Label>
              {isEditing ? (
                <select
                  name="status"
                  value={formData.status}
                  onChange={handleInputChange}
                  className="w-full p-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-md"
                >
                  <option value="Not Started">Not Started</option>
                  <option value="In Progress">In Progress</option>
                  <option value="Delayed">Delayed</option>
                  <option value="Completed">Completed</option>
                </select>
              ) : (
                <p className="text-lg">
                  <span className={`px-2 py-1 rounded-full text-sm ${
                    project.status === 'Completed' ? 'bg-green-100 text-green-800' :
                    project.status === 'In Progress' ? 'bg-blue-100 text-blue-800' :
                    project.status === 'Delayed' ? 'bg-red-100 text-red-800' :
                    'bg-gray-100 text-gray-800'
                  }`}>
                    {project.status}
                  </span>
                </p>
              )}
            </div>

            <div>
              <Label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Notes
              </Label>
              {isEditing ? (
                <Textarea
                  name="notes"
                  value={formData.notes}
                  onChange={handleInputChange}
                  placeholder="Enter project notes"
                  rows={3}
                />
              ) : (
                <p className="text-lg">{project.notes || 'No notes'}</p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* الكميات */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Package className="h-5 w-5 mr-2" />
              Quantities
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              {[
                { key: 'quantity_pvc', label: 'PVC', unit: 'units' },
                { key: 'quantity_shutter', label: 'Shutter', unit: 'units' },
                { key: 'quantity_sgs', label: 'SGS', unit: 'units' },
                { key: 'quantity_doors', label: 'Doors', unit: 'units' },
                { key: 'quantity_glass', label: 'Glass', unit: 'units' }
              ].map(({ key, label, unit }) => (
                <div key={key}>
                  <Label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    {label} ({unit})
                  </Label>
                  {isEditing ? (
                    <Input
                      type="number"
                      name={key}
                      value={formData[key]}
                      onChange={handleInputChange}
                      placeholder={`Enter ${label.toLowerCase()} quantity`}
                    />
                  ) : (
                    <p className="text-lg">{project.materials?.[key] || 0} {unit}</p>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* إدارة الملفات */}
      <Card className="mt-6">
        <CardHeader>
          <CardTitle>Project Files</CardTitle>
        </CardHeader>
        <CardContent>
          <FileUploadManager
            projectId={id}
            files={files}
            onFilesChange={handleFilesChange}
            maxFiles={20}
            maxFileSize={10 * 1024 * 1024} // 10MB
            allowedTypes={['.pdf', '.jpg', '.jpeg', '.png', '.doc', '.docx']}
            showPreview={true}
            disabled={false}
          />
        </CardContent>
      </Card>
    </div>
  );
};

export default ProjectDetailsPage;
