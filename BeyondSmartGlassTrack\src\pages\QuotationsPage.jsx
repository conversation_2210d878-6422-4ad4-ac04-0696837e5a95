import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import { useToast } from '@/components/ui/use-toast';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  Plus, 
  Search, 
  Filter, 
  FileText, 
  Edit, 
  Trash2, 
  Eye,
  CheckCircle,
  XCircle,
  Clock
} from 'lucide-react';
import {
  getAllQuotations,
  deleteQuotation,
  updateQuotationStatus,
  searchQuotations,
  getQuotationsByStatus
} from '@/lib/quotationsService';
import QuotationModal from '@/components/quotations/QuotationModal';
import DeleteConfirmDialog from '@/components/ui/DeleteConfirmDialog';

const QuotationsPage = () => {
  const { t } = useTranslation();
  const { toast } = useToast();
  
  const [quotations, setQuotations] = useState([]);
  const [filteredQuotations, setFilteredQuotations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedQuotation, setSelectedQuotation] = useState(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [quotationToDelete, setQuotationToDelete] = useState(null);

  useEffect(() => {
    loadQuotations();
  }, []);

  useEffect(() => {
    filterQuotations();
  }, [quotations, searchTerm, statusFilter]);

  const loadQuotations = async () => {
    try {
      setLoading(true);
      const data = await getAllQuotations();
      setQuotations(data);
    } catch (error) {
      console.error('Error loading quotations:', error);
      toast({
        title: t('common.error'),
        description: 'Failed to load quotations',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  const filterQuotations = () => {
    let filtered = quotations;

    // Filter by status
    if (statusFilter !== 'all') {
      filtered = filtered.filter(q => q.status === statusFilter);
    }

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(q =>
        q.quotation_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
        q.client_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        q.client_city.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    setFilteredQuotations(filtered);
  };

  const handleAddQuotation = () => {
    setSelectedQuotation(null);
    setIsModalOpen(true);
  };

  const handleEditQuotation = (quotation) => {
    setSelectedQuotation(quotation);
    setIsModalOpen(true);
  };

  const handleDeleteQuotation = (quotation) => {
    setQuotationToDelete(quotation);
    setDeleteDialogOpen(true);
  };

  const confirmDelete = async () => {
    try {
      await deleteQuotation(quotationToDelete.id);
      toast({
        title: t('common.success'),
        description: 'Quotation deleted successfully',
        className: 'bg-green-500 text-white'
      });
      loadQuotations();
    } catch (error) {
      toast({
        title: t('common.error'),
        description: 'Failed to delete quotation',
        variant: 'destructive'
      });
    } finally {
      setDeleteDialogOpen(false);
      setQuotationToDelete(null);
    }
  };

  const handleStatusChange = async (quotationId, newStatus) => {
    try {
      await updateQuotationStatus(quotationId, newStatus);
      toast({
        title: t('common.success'),
        description: 'Quotation status updated successfully',
        className: 'bg-green-500 text-white'
      });
      loadQuotations();
    } catch (error) {
      toast({
        title: t('common.error'),
        description: 'Failed to update quotation status',
        variant: 'destructive'
      });
    }
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      pending: { color: 'bg-yellow-500', icon: Clock, text: t('quotations.pending') },
      approved: { color: 'bg-green-500', icon: CheckCircle, text: t('quotations.approved') },
      rejected: { color: 'bg-red-500', icon: XCircle, text: t('quotations.rejected') }
    };

    const config = statusConfig[status] || statusConfig.pending;
    const Icon = config.icon;

    return (
      <Badge className={`${config.color} text-white`}>
        <Icon size={12} className="mr-1" />
        {config.text}
      </Badge>
    );
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">{t('common.loading')}</div>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="space-y-6"
    >
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            {t('quotations.title')}
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Manage smart glass quotations and pricing
          </p>
        </div>
        <Button onClick={handleAddQuotation} className="bg-blue-600 hover:bg-blue-700">
          <Plus size={20} className="mr-2" />
          {t('quotations.addQuotation')}
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <Input
                  placeholder="Search quotations..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">All Status</option>
                <option value="pending">Pending</option>
                <option value="approved">Approved</option>
                <option value="rejected">Rejected</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Quotations List */}
      <div className="grid gap-4">
        {filteredQuotations.length === 0 ? (
          <Card>
            <CardContent className="p-8 text-center">
              <FileText size={48} className="mx-auto text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                No quotations found
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                {searchTerm || statusFilter !== 'all' 
                  ? 'No quotations match your filters' 
                  : 'Get started by creating your first quotation'
                }
              </p>
              {!searchTerm && statusFilter === 'all' && (
                <Button onClick={handleAddQuotation} className="bg-blue-600 hover:bg-blue-700">
                  <Plus size={20} className="mr-2" />
                  {t('quotations.addQuotation')}
                </Button>
              )}
            </CardContent>
          </Card>
        ) : (
          filteredQuotations.map((quotation) => (
            <Card key={quotation.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex flex-col sm:flex-row justify-between items-start gap-4">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                        {quotation.quotation_number}
                      </h3>
                      {getStatusBadge(quotation.status)}
                    </div>
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
                      <div>
                        <span className="text-gray-500">Client:</span>
                        <p className="font-medium">{quotation.client_name}</p>
                      </div>
                      <div>
                        <span className="text-gray-500">City:</span>
                        <p className="font-medium">{quotation.client_city}</p>
                      </div>
                      <div>
                        <span className="text-gray-500">Item Type:</span>
                        <p className="font-medium">{quotation.item_type}</p>
                      </div>
                      <div>
                        <span className="text-gray-500">Final Price:</span>
                        <p className="font-medium text-green-600">
                          {formatCurrency(quotation.final_price)}
                        </p>
                      </div>
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleEditQuotation(quotation)}
                    >
                      <Edit size={16} />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDeleteQuotation(quotation)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 size={16} />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* Quotation Modal */}
      <QuotationModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        quotation={selectedQuotation}
        onSave={loadQuotations}
      />

      {/* Delete Confirmation Dialog */}
      <DeleteConfirmDialog
        isOpen={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
        onConfirm={confirmDelete}
        title="Delete Quotation"
        description={`Are you sure you want to delete quotation ${quotationToDelete?.quotation_number}? This action cannot be undone.`}
      />
    </motion.div>
  );
};

export default QuotationsPage;
