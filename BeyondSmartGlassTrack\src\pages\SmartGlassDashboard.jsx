import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line
} from 'recharts';
import { 
  FileText, 
  FileCheck, 
  Factory, 
  Wrench, 
  Package,
  Shield,
  RefreshCw,
  TrendingUp,
  Calendar,
  DollarSign,
  Users,
  AlertTriangle
} from 'lucide-react';

const SmartGlassDashboard = () => {
  const { t } = useTranslation();
  
  const [loading, setLoading] = useState(false);
  const [timeFilter, setTimeFilter] = useState('month');

  // Mock data for demonstration
  const [dashboardData, setDashboardData] = useState({
    quotations: {
      total: 45,
      pending: 12,
      approved: 28,
      rejected: 5,
      totalValue: 850000,
      approvedValue: 650000
    },
    contracts: {
      total: 28,
      signed: 25,
      inProgress: 15,
      completed: 8,
      cancelled: 2,
      totalValue: 650000,
      paidAmount: 420000
    },
    manufacturing: {
      queued: 8,
      cutting: 3,
      manufacturing: 4,
      ready: 2
    },
    installations: {
      scheduled: 5,
      inProgress: 3,
      completed: 8,
      onHold: 1
    },
    inventory: {
      smartFilm: 150,
      smartGlass: 25,
      devices: 45,
      remotes: 120
    }
  });

  const monthlyData = [
    { month: 'Jan', quotations: 8, contracts: 6, revenue: 120000 },
    { month: 'Feb', quotations: 12, contracts: 9, revenue: 180000 },
    { month: 'Mar', quotations: 15, contracts: 11, revenue: 220000 },
    { month: 'Apr', quotations: 10, contracts: 8, revenue: 160000 },
    { month: 'May', quotations: 18, contracts: 14, revenue: 280000 },
    { month: 'Jun', quotations: 22, contracts: 16, revenue: 320000 }
  ];

  const statusData = [
    { name: 'Pending', value: dashboardData.quotations.pending, color: '#f59e0b' },
    { name: 'Approved', value: dashboardData.quotations.approved, color: '#10b981' },
    { name: 'Rejected', value: dashboardData.quotations.rejected, color: '#ef4444' }
  ];

  const COLORS = ['#f59e0b', '#10b981', '#ef4444'];

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'SAR',
      minimumFractionDigits: 0
    }).format(amount);
  };

  const StatCard = ({ title, value, icon: Icon, color, subtitle, trend }) => (
    <Card className="hover:shadow-lg transition-shadow">
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{title}</p>
            <p className="text-2xl font-bold text-gray-900 dark:text-white">{value}</p>
            {subtitle && (
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">{subtitle}</p>
            )}
          </div>
          <div className={`p-3 rounded-full ${color}`}>
            <Icon size={24} className="text-white" />
          </div>
        </div>
        {trend && (
          <div className="flex items-center mt-4 text-sm">
            <TrendingUp size={16} className="text-green-500 mr-1" />
            <span className="text-green-500 font-medium">{trend}</span>
            <span className="text-gray-500 ml-1">vs last month</span>
          </div>
        )}
      </CardContent>
    </Card>
  );

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="space-y-6"
    >
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            {t('dashboard.title')}
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Beyond Smart Glass operations overview
          </p>
        </div>
        <div className="flex gap-2">
          <select
            value={timeFilter}
            onChange={(e) => setTimeFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="week">This Week</option>
            <option value="month">This Month</option>
            <option value="year">This Year</option>
          </select>
          <Button variant="outline" size="sm">
            <RefreshCw size={16} className="mr-2" />
            {t('dashboard.refresh')}
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Total Quotations"
          value={dashboardData.quotations.total}
          icon={FileText}
          color="bg-blue-500"
          subtitle={formatCurrency(dashboardData.quotations.totalValue)}
          trend="+12%"
        />
        <StatCard
          title="Active Contracts"
          value={dashboardData.contracts.total}
          icon={FileCheck}
          color="bg-green-500"
          subtitle={formatCurrency(dashboardData.contracts.totalValue)}
          trend="+8%"
        />
        <StatCard
          title="Manufacturing Queue"
          value={dashboardData.manufacturing.queued + dashboardData.manufacturing.cutting + dashboardData.manufacturing.manufacturing}
          icon={Factory}
          color="bg-orange-500"
          subtitle={`${dashboardData.manufacturing.ready} ready`}
        />
        <StatCard
          title="Active Installations"
          value={dashboardData.installations.inProgress}
          icon={Wrench}
          color="bg-purple-500"
          subtitle={`${dashboardData.installations.completed} completed`}
          trend="+15%"
        />
      </div>

      {/* Charts Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Monthly Trends */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp size={20} />
              Monthly Performance
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={monthlyData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip formatter={(value, name) => [
                  name === 'revenue' ? formatCurrency(value) : value,
                  name === 'revenue' ? 'Revenue' : name.charAt(0).toUpperCase() + name.slice(1)
                ]} />
                <Legend />
                <Line type="monotone" dataKey="quotations" stroke="#3b82f6" strokeWidth={2} />
                <Line type="monotone" dataKey="contracts" stroke="#10b981" strokeWidth={2} />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Quotation Status */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText size={20} />
              Quotation Status
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={statusData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {statusData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions & Inventory */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <Button className="w-full justify-start" variant="outline">
              <FileText size={16} className="mr-2" />
              Create New Quotation
            </Button>
            <Button className="w-full justify-start" variant="outline">
              <Calendar size={16} className="mr-2" />
              Schedule Site Visit
            </Button>
            <Button className="w-full justify-start" variant="outline">
              <Factory size={16} className="mr-2" />
              Add to Manufacturing Queue
            </Button>
            <Button className="w-full justify-start" variant="outline">
              <Package size={16} className="mr-2" />
              Update Inventory
            </Button>
          </CardContent>
        </Card>

        {/* Inventory Status */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Package size={20} />
              Inventory Status
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">Smart Film Rolls</span>
              <Badge variant="outline">{dashboardData.inventory.smartFilm} units</Badge>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">Smart Glass Panels</span>
              <Badge variant="outline">{dashboardData.inventory.smartGlass} units</Badge>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">Control Devices</span>
              <Badge variant="outline">{dashboardData.inventory.devices} units</Badge>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">Remote Controls</span>
              <Badge variant="outline">{dashboardData.inventory.remotes} units</Badge>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Revenue Chart */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DollarSign size={20} />
            Revenue Trends
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={monthlyData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis tickFormatter={(value) => formatCurrency(value)} />
              <Tooltip formatter={(value) => [formatCurrency(value), 'Revenue']} />
              <Bar dataKey="revenue" fill="#10b981" />
            </BarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default SmartGlassDashboard;
