import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useParams, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useToast } from '@/components/ui/use-toast';
import { ArrowLeft, CheckCircle, FileText, Download, User, MapPin, Calendar, Building, Plus } from 'lucide-react';
import ProgressCircle from '@/components/projects/ProgressCircle';
import { getProjectById, getProjectVisits } from '@/lib/supabaseDatabase';
import { getProjectVisits as getVisits, addVisit, updateVisit } from '@/lib/visitsService';
import VisitModal from '@/components/visits/VisitModal';
import VisitsList from '@/components/visits/VisitsList';
import { exportProjectToPDF, exportProjectToExcel } from '@/lib/exportService';

const TrackProjectPage = () => {
  const { projectId } = useParams();
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const { toast } = useToast();

  const [project, setProject] = useState(null);
  const [visits, setVisits] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showVisitModal, setShowVisitModal] = useState(false);
  const [editingVisit, setEditingVisit] = useState(null);
  const [visitsLoading, setVisitsLoading] = useState(false);

  useEffect(() => {
    fetchProjectDetails();
    fetchVisits();
  }, [projectId]);

  const fetchProjectDetails = async () => {
    setLoading(true);
    try {
      // الحصول على تفاصيل المشروع من Supabase
      const project = await getProjectById(projectId);

      if (!project) {
        toast({
          title: t('trackProject.fetchErrorTitle') || "Error",
          description: t('trackProject.fetchProjectErrorMsg') || "Could not fetch project details.",
          variant: "destructive"
        });
        navigate('/projects');
        return;
      }

      setProject(project);
    } catch (error) {
      console.error('Error fetching project details:', error);
      toast({
        title: t('trackProject.fetchErrorTitle') || "Error",
        description: t('trackProject.fetchProjectErrorMsg') || "Could not fetch project details.",
        variant: "destructive"
      });
      navigate('/projects');
    } finally {
      setLoading(false);
    }
  };

  const fetchVisits = async () => {
    try {
      // الحصول على زيارات المشروع من Supabase
      const visits = await getProjectVisits(projectId);

      // ترتيب الزيارات حسب تاريخ الزيارة (الأحدث أولاً)
      const sortedVisits = visits.sort((a, b) =>
        new Date(b.visit_date) - new Date(a.visit_date)
      );

      setVisits(sortedVisits || []);
    } catch (error) {
      console.error('Error fetching project visits:', error);
      toast({
        title: t('trackProject.fetchErrorTitle') || "Error",
        description: t('trackProject.fetchVisitsErrorMsg') || "Could not fetch project visits.",
        variant: "destructive"
      });
    }
  };



  // حساب إجمالي الخامات المركبة من جميع الزيارات
  const calculateTotalInstalled = (materialType) => {
    return visits.reduce((sum, visit) => {
      const materialsInstalled = visit.materials_installed || {};
      return sum + (materialsInstalled[`quantity_${materialType}`] || 0);
    }, 0);
  };

  // دالة لتلوين حالة المشروع
  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'completed':
      case 'مكتمل':
        return 'bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400';
      case 'in progress':
      case 'قيد التنفيذ':
      case 'inprogress':
        return 'bg-blue-100 text-blue-700 dark:bg-blue-900/20 dark:text-blue-400';
      case 'delayed':
      case 'متأخر':
        return 'bg-red-100 text-red-700 dark:bg-red-900/20 dark:text-red-400';
      case 'not started':
      case 'لم يبدأ':
      case 'notstarted':
        return 'bg-gray-100 text-gray-700 dark:bg-gray-900/20 dark:text-gray-400';
      case 'pending':
      case 'معلق':
        return 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'on hold':
      case 'متوقف مؤقتاً':
      case 'onhold':
        return 'bg-orange-100 text-orange-700 dark:bg-orange-900/20 dark:text-orange-400';
      default:
        return 'bg-gray-100 text-gray-700 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  // دالة لترجمة حالة المشروع
  const getStatusTranslation = (status) => {
    const statusMap = {
      'completed': t('dashboard.completed') || 'مكتمل',
      'in progress': t('dashboard.inProgress') || 'قيد التنفيذ',
      'inprogress': t('dashboard.inProgress') || 'قيد التنفيذ',
      'delayed': t('dashboard.delayed') || 'متأخر',
      'not started': t('dashboard.notStarted') || 'لم يبدأ',
      'notstarted': t('dashboard.notStarted') || 'لم يبدأ',
      'pending': t('dashboard.pending') || 'معلق',
      'on hold': t('dashboard.onHold') || 'متوقف مؤقتاً',
      'onhold': t('dashboard.onHold') || 'متوقف مؤقتاً',
      'مكتمل': t('dashboard.completed') || 'مكتمل',
      'قيد التنفيذ': t('dashboard.inProgress') || 'قيد التنفيذ',
      'متأخر': t('dashboard.delayed') || 'متأخر',
      'لم يبدأ': t('dashboard.notStarted') || 'لم يبدأ',
      'معلق': t('dashboard.pending') || 'معلق',
      'متوقف مؤقتاً': t('dashboard.onHold') || 'متوقف مؤقتاً'
    };
    return statusMap[status?.toLowerCase()] || status || t('common.notSpecified') || 'غير محدد';
  };

  // دوال إدارة الزيارات الجديدة
  const handleAddVisitNew = () => {
    setEditingVisit(null);
    setShowVisitModal(true);
  };

  const handleEditVisit = (visit) => {
    setEditingVisit(visit);
    setShowVisitModal(true);
  };

  const handleSaveVisit = async (visitData) => {
    try {
      let result;
      if (visitData.id) {
        // تحديث زيارة موجودة
        result = await updateVisit(visitData.id, visitData);
      } else {
        // إضافة زيارة جديدة
        result = await addVisit(visitData);
      }

      if (result.success) {
        await fetchVisits(); // إعادة تحميل الزيارات
        toast({
          title: "نجح الحفظ",
          description: visitData.id ? "تم تحديث الزيارة بنجاح" : "تم إضافة الزيارة بنجاح",
          className: "bg-green-500 text-white"
        });
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      console.error('Error saving visit:', error);
      toast({
        title: "خطأ في الحفظ",
        description: "تعذر حفظ الزيارة. يرجى المحاولة مرة أخرى.",
        variant: "destructive"
      });
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: { y: 0, opacity: 1, transition: { type: 'spring', stiffness: 100 } },
  };

  if (loading) return <p className="text-center p-10">{t('trackProject.loading') || "Loading project data..."}</p>;
  if (!project) return <p className="text-center p-10">{t('trackProject.notFound') || "Project not found."}</p>;

  const quantities = [
    { key: 'pvc', label: t('dashboard.pvc') },
    { key: 'shutter', label: t('dashboard.shutter') },
    { key: 'sgs', label: t('dashboard.sgs') },
    { key: 'doors', label: t('dashboard.doors') },
    { key: 'glass', label: t('dashboard.glass') || "Glass" },
  ];

  return (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={{ visible: { transition: { staggerChildren: 0.1 } } }}
      className="p-4 md:p-6"
      dir={i18n.dir()}
    >
      <motion.div variants={itemVariants} className="flex flex-col md:flex-row items-start md:items-center justify-between mb-8 gap-4">
        <h1 className="text-3xl font-bold text-gray-800 dark:text-white gradient-text">
          {t('sidebar.trackProject')}: {project.project_number}
        </h1>

        <div className="flex flex-wrap gap-2">
          <Button
            onClick={() => {
              try {
                exportProjectToPDF(project, visits, t, i18n.language === 'ar');
              } catch (error) {
                console.error('Error exporting to PDF:', error);
              }
            }}
            variant="outline"
            className="flex items-center gap-2 border-red-500 text-red-500 hover:bg-red-500 hover:text-white"
            size="sm"
          >
            <FileText className="h-4 w-4" />
            {t('exportService.exportPDF') || 'تصدير PDF'}
          </Button>

          <Button
            onClick={() => {
              try {
                exportProjectToExcel(project, visits, t);
              } catch (error) {
                console.error('Error exporting to Excel:', error);
              }
            }}
            variant="outline"
            className="flex items-center gap-2 border-green-500 text-green-500 hover:bg-green-500 hover:text-white"
            size="sm"
          >
            <Download className="h-4 w-4" />
            {t('exportService.exportExcel') || 'تصدير Excel'}
          </Button>

          <Button
            variant="outline"
            onClick={() => navigate('/projects')}
            className="dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700"
            size="sm"
          >
            <ArrowLeft className={`h-4 w-4 ${i18n.language === 'ar' ? 'ml-2' : 'mr-2'}`} />
            {t('trackProject.backToProjects') || "Back to Projects"}
          </Button>
        </div>
      </motion.div>

      {/* إحصائيات سريعة */}
      <motion.div variants={itemVariants} className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <Card className="bg-gradient-to-r from-blue-500 to-blue-600 text-white shadow-lg">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-blue-100 text-sm">{t('trackProject.totalVisits') || 'إجمالي الزيارات'}</p>
                <p className="text-2xl font-bold">{visits.length}</p>
              </div>
              <Calendar className="h-8 w-8 text-blue-200" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-green-500 to-green-600 text-white shadow-lg">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-green-100 text-sm">{t('trackProject.completionPercentage') || 'نسبة الإنجاز'}</p>
                <p className="text-2xl font-bold">
                  {quantities.length > 0 ? Math.round(
                    quantities.reduce((sum, q) => {
                      const totalPlanned = project.materials?.[`quantity_${q.key}`] || project[`quantity_${q.key}`] || 0;
                      const totalInstalled = calculateTotalInstalled(q.key);
                      return sum + (totalPlanned > 0 ? (totalInstalled / totalPlanned) * 100 : 0);
                    }, 0) / quantities.length
                  ) : 0}%
                </p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-200" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-purple-500 to-purple-600 text-white shadow-lg">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-purple-100 text-sm">{t('trackProject.projectDays') || 'أيام المشروع'}</p>
                <p className="text-2xl font-bold">
                  {Math.ceil((new Date() - new Date(project.start_date)) / (1000 * 60 * 60 * 24))}
                </p>
              </div>
              <Calendar className="h-8 w-8 text-purple-200" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-orange-500 to-orange-600 text-white shadow-lg">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-orange-100 text-sm">{t('trackProject.lastVisit') || 'آخر زيارة'}</p>
                <p className="text-lg font-bold">
                  {visits.length > 0
                    ? new Date(visits[0].visit_date).toLocaleDateString('ar-SA', { month: 'short', day: 'numeric' })
                    : t('trackProject.noVisits') || 'لا توجد'
                  }
                </p>
              </div>
              <User className="h-8 w-8 text-orange-200" />
            </div>
          </CardContent>
        </Card>
      </motion.div>

      <motion.div variants={itemVariants} className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        <Card className="lg:col-span-1 shadow-lg dark:bg-gray-800/80 border-gray-200 dark:border-gray-700/50 backdrop-blur-md">
          <CardHeader>
            <CardTitle className="text-xl text-gray-700 dark:text-gray-200">{t('trackProject.projectInfo') || "Project Information"}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3 text-sm text-gray-600 dark:text-gray-300">
            <div className="flex items-center">
              <User className="h-4 w-4 mr-2 text-blue-500" />
              <span><strong>{t('addProject.clientName') || "Client"}:</strong> {project.client_name}</span>
            </div>
            <div className="flex items-center">
              <MapPin className="h-4 w-4 mr-2 text-green-500" />
              <span><strong>{t('addProject.city') || "City"}:</strong> {project.city || project.project_location || project.address || 'غير محدد'}</span>
            </div>
            <div className="flex items-center">
              <Calendar className="h-4 w-4 mr-2 text-purple-500" />
              <span><strong>{t('addProject.startDate') || "Start Date"}:</strong> {new Date(project.start_date).toLocaleDateString()}</span>
            </div>
            {project.manufacturing_number && (
              <div className="flex items-center">
                <Building className="h-4 w-4 mr-2 text-orange-500" />
                <span><strong>{t('addProject.manufacturingNumber') || "Manufacturing No"}:</strong> {project.manufacturing_number}</span>
              </div>
            )}
            <div className="flex items-center">
              <CheckCircle className="h-4 w-4 mr-2 text-gray-500" />
              <span><strong>{t('allProjects.status') || "Status"}:</strong>
                <span className={`font-semibold ml-2 px-2 py-1 rounded-full text-xs ${getStatusColor(project.status)}`}>
                  {getStatusTranslation(project.status)}
                </span>
              </span>
            </div>
          </CardContent>
        </Card>

        <Card className="lg:col-span-2 shadow-lg dark:bg-gray-800/80 border-gray-200 dark:border-gray-700/50 backdrop-blur-md">
          <CardHeader>
            <CardTitle className="text-xl text-gray-700 dark:text-gray-200">{t('trackProject.progressSummary') || "Progress Summary"}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-6 mb-4">
              {quantities.map(q => {
                const totalPlanned = project.materials?.[`quantity_${q.key}`] || project[`quantity_${q.key}`] || 0;
                const totalInstalled = calculateTotalInstalled(q.key);
                const percentage = totalPlanned > 0 ? Math.min(Math.round((totalInstalled / totalPlanned) * 100), 100) : 0;
                const remaining = Math.max(totalPlanned - totalInstalled, 0);

                // Determine color based on percentage
                let color = '#3b82f6'; // Default blue
                if (percentage >= 100) color = '#22c55e'; // Green for completed
                else if (percentage >= 75) color = '#3b82f6'; // Blue for good progress
                else if (percentage >= 50) color = '#f59e0b'; // Amber for medium progress
                else if (percentage >= 25) color = '#f97316'; // Orange for low progress
                else color = '#ef4444'; // Red for very low progress

                return (
                  <div key={q.key} className="flex flex-col items-center">
                    <ProgressCircle
                      percentage={percentage}
                      size={100}
                      strokeWidth={10}
                      color={color}
                      label={q.label}
                      value={totalInstalled}
                      total={totalPlanned}
                      unit={t('dashboard.unit') || "وحدة"}
                    />
                    <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                      {t('trackProject.remaining') || "Remaining"}: {remaining}
                    </p>
                  </div>
                );
              })}
            </div>

            <h3 className="text-lg font-semibold text-gray-700 dark:text-gray-300 mt-6 mb-3">
              📊 {t('trackProject.detailedProgress') || "Detailed Progress"}
            </h3>

            <div className="space-y-3">
              {quantities.map(q => {
                const totalPlanned = project.materials?.[`quantity_${q.key}`] || project[`quantity_${q.key}`] || 0;
                const totalInstalled = calculateTotalInstalled(q.key);
                const percentage = totalPlanned > 0 ? ((totalInstalled / totalPlanned) * 100).toFixed(1) : 0;
                const remaining = Math.max(totalPlanned - totalInstalled, 0);
                return (
                  <div key={`bar-${q.key}`}>
                    <div className="flex justify-between text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      <span>{q.label}</span>
                      <span>{totalInstalled} / {totalPlanned} ({percentage}%)</span>
                    </div>
                    <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2.5">
                      <div className="bg-gradient-to-r from-sky-500 to-indigo-500 h-2.5 rounded-full" style={{ width: `${percentage}%` }}></div>
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* قسم الزيارات */}
      <motion.div variants={itemVariants} className="mb-8">
        <Card className="shadow-lg dark:bg-gray-800/80 border-gray-200 dark:border-gray-700/50 backdrop-blur-md">
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="text-xl text-gray-800 dark:text-white flex items-center">
                <Calendar className="h-5 w-5 mr-2" />
                {t('visits.visits') || 'Visits'}
              </CardTitle>
              <Button
                type="button"
                onClick={handleAddVisitNew}
                className="bg-blue-500 hover:bg-blue-600 text-white"
                size="sm"
              >
                <Plus className="h-4 w-4 mr-2" />
                {t('visits.addVisit') || 'Add Visit'}
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <VisitsList
              visits={visits}
              onEditVisit={handleEditVisit}
              loading={visitsLoading}
            />
          </CardContent>
        </Card>
      </motion.div>

      {/* نافذة إضافة/تعديل الزيارة */}
      <VisitModal
        isOpen={showVisitModal}
        onClose={() => {
          setShowVisitModal(false);
          setEditingVisit(null);
        }}
        onSave={handleSaveVisit}
        visit={editingVisit}
        project={project}
        isEditing={!!editingVisit}
      />
    </motion.div>
  );
};

export default TrackProjectPage;