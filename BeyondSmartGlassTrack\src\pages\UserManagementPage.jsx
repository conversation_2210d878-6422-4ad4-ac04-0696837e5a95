import React, { useState, useEffect, useCallback } from 'react';
import { motion } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog";
import { useToast } from '@/components/ui/use-toast';
import { Users, UserPlus, Edit3, Trash, AlertTriangle, Loader2, ListX } from 'lucide-react';
import { getAllUsers, addUser, updateUser, deleteUser } from '@/lib/supabaseDatabase';
import UserFormModal from '@/components/UserFormModal';

const UserManagementPage = () => {
  const { t, i18n } = useTranslation();
  const { toast } = useToast();
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showModal, setShowModal] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [currentUser, setCurrentUser] = useState(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [userToDelete, setUserToDelete] = useState(null);

  const userRoles = [
    { value: 'manager', label: 'مدير / Manager', color: 'bg-purple-100 text-purple-800' },
    { value: 'supervisor', label: 'مشرف / Supervisor', color: 'bg-blue-100 text-blue-800' },
    { value: 'data_entry', label: 'مدخل بيانات / Data Entry', color: 'bg-green-100 text-green-800' },
    { value: 'technician', label: 'فني / Technician', color: 'bg-orange-100 text-orange-800' }
  ];

  const fetchUsers = useCallback(async () => {
    setLoading(true);
    try {
      const data = await getAllUsers();
      const sortedUsers = [...data].sort((a, b) => {
        return a.email.localeCompare(b.email);
      });
      setUsers(sortedUsers);
    } catch (error) {
      console.error('Error fetching users:', error);
      toast({
        title: t('userManagement.fetchErrorTitle') || "Error",
        description: t('userManagement.fetchErrorMsg') || "Could not fetch users.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  }, [toast, t]);

  useEffect(() => {
    fetchUsers();
  }, [fetchUsers]);

  const handleAddUser = () => {
    setCurrentUser(null);
    setIsEditing(false);
    setShowModal(true);
  };

  const handleEditUser = (user) => {
    setCurrentUser(user);
    setIsEditing(true);
    setShowModal(true);
  };

  const handleSubmitUserForm = async (formData) => {
    try {
      if (isEditing && currentUser) {
        await updateUser(currentUser.id, {
          name: formData.full_name,
          role: formData.role,
          phone: formData.phone || null,
          department: formData.department || null,
          is_active: formData.is_active
        });

        toast({
          title: t('userManagement.updateSuccessTitle') || "User Updated",
          description: t('userManagement.updateSuccessMsg', {email: currentUser.email}) || `User ${currentUser.email} updated.`,
          className: "bg-green-500 text-white dark:bg-green-600"
        });

        fetchUsers();
        setShowModal(false);
      } else {
        const userData = {
          name: formData.full_name,
          email: formData.email,
          password: formData.password,
          phone: formData.phone || null,
          role: formData.role,
          department: formData.department || null,
          is_active: formData.is_active
        };

        const newUser = await addUser(userData);

        toast({
          title: t('userManagement.addSuccessTitle') || "User Added",
          description: newUser.temporaryPassword
            ? `User ${formData.email} added successfully! Temporary password: ${newUser.temporaryPassword}`
            : `User ${formData.email} added successfully! You can now login with password: ${formData.password}`,
          className: "bg-green-500 text-white dark:bg-green-600",
          duration: 15000
        });

        fetchUsers();
        setShowModal(false);
      }
    } catch (error) {
      console.error('Error submitting user form:', error);
      toast({
        title: isEditing
          ? (t('userManagement.updateErrorTitle') || "Update Error")
          : (t('userManagement.addErrorTitle') || "Add Error"),
        description: error.message,
        variant: "destructive"
      });
    }
  };

  const confirmDeleteUser = (user) => {
    setUserToDelete(user);
    setShowDeleteConfirm(true);
  };

  const handleDeleteUser = async () => {
    if (!userToDelete) return;

    try {
      await deleteUser(userToDelete.id);

      toast({
        title: t('userManagement.deleteSuccessTitle') || "User Deleted",
        description: t('userManagement.deleteSuccessMsg', {email: userToDelete.email}) || `User ${userToDelete.email} deleted.`,
        className: "bg-green-500 text-white dark:bg-green-600"
      });

      fetchUsers();
    } catch (error) {
      console.error('Error deleting user:', error);
      toast({
        title: t('userManagement.deleteErrorTitle') || "Delete Error",
        description: error.message || "Could not delete user.",
        variant: "destructive"
      });
    } finally {
      setShowDeleteConfirm(false);
      setUserToDelete(null);
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: { y: 0, opacity: 1, transition: { type: 'spring', stiffness: 100 } },
  };

  return (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={{ visible: { transition: { staggerChildren: 0.1 } } }}
      className="p-4 md:p-6"
      dir={i18n.dir()}
    >
      <motion.div variants={itemVariants} className="flex flex-col sm:flex-row justify-between items-center mb-8 gap-4">
        <h1 className="text-3xl font-bold text-gray-800 dark:text-white gradient-text flex items-center">
          <Users className={`h-8 w-8 ${i18n.language === 'ar' ? 'ml-3' : 'mr-3'}`} />
          {t('sidebar.userManagement')}
        </h1>
        <Button onClick={handleAddUser} className="w-full sm:w-auto bg-gradient-to-r from-green-500 to-teal-500 hover:from-green-600 hover:to-teal-600 text-white shadow-md hover:shadow-lg transition-shadow">
          <UserPlus className={`h-5 w-5 ${i18n.language === 'ar' ? 'ml-2' : 'mr-2'}`} />
          {t('userManagement.addUserButton') || "Add User"}
        </Button>
      </motion.div>

      <motion.div variants={itemVariants}>
        <Card className="shadow-xl dark:bg-gray-800/80 border-gray-200 dark:border-gray-700/50 backdrop-blur-md">
          <CardHeader>
            <CardTitle className="text-xl text-gray-700 dark:text-gray-200">{t('userManagement.usersList') || "Users List"}</CardTitle>
          </CardHeader>
          <CardContent className="p-0">
            {loading ? (
              <div className="p-6 text-center flex flex-col items-center">
                <Loader2 className="h-12 w-12 text-blue-500 animate-spin mb-2" />
                <p className="text-lg text-gray-600 dark:text-gray-300">{t('userManagement.loadingUsers') || "Loading users..."}</p>
              </div>
            ) :
            users.length === 0 ? (
              <div className="p-6 text-center text-gray-500 dark:text-gray-400">
                <ListX size={48} className="mx-auto mb-2" />
                <p className="text-lg">{t('userManagement.noUsers') || "No users found."}</p>
                <p className="text-sm">{t('userManagement.tryAddingUsers') || "Add users to manage them here."}</p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader className="bg-gray-50 dark:bg-gray-700/50">
                    <TableRow>
                      <TableHead className="px-4 py-3 text-gray-600 dark:text-gray-300">Name</TableHead>
                      <TableHead className="px-4 py-3 text-gray-600 dark:text-gray-300">Email</TableHead>
                      <TableHead className="px-4 py-3 text-gray-600 dark:text-gray-300">Phone</TableHead>
                      <TableHead className="px-4 py-3 text-gray-600 dark:text-gray-300">Role</TableHead>
                      <TableHead className="px-4 py-3 text-gray-600 dark:text-gray-300">Department</TableHead>
                      <TableHead className="px-4 py-3 text-gray-600 dark:text-gray-300">Status</TableHead>
                      <TableHead className="px-4 py-3 text-gray-600 dark:text-gray-300">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {users.map(user => {
                      const roleInfo = userRoles.find(r => r.value === user.role);
                      return (
                        <TableRow key={user.id} className="hover:bg-gray-50 dark:hover:bg-gray-700/30 border-b dark:border-gray-700/50">
                          <TableCell className="font-medium text-gray-800 dark:text-gray-100 px-4 py-3">
                            <div className="flex items-center">
                              <div className="h-8 w-8 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center text-white text-sm font-medium mr-3">
                                {user.name ? user.name.charAt(0).toUpperCase() : 'U'}
                              </div>
                              <div>
                                <div className="font-medium">{user.name || '-'}</div>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell className="text-gray-600 dark:text-gray-300 px-4 py-3">
                            <div className="text-sm">{user.email}</div>
                          </TableCell>
                          <TableCell className="text-gray-600 dark:text-gray-300 px-4 py-3">
                            <div className="text-sm">{user.phone || '-'}</div>
                          </TableCell>
                          <TableCell className="px-4 py-3">
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${roleInfo?.color || 'bg-gray-100 text-gray-800'}`}>
                              {roleInfo?.label.split(' / ')[1] || user.role}
                            </span>
                          </TableCell>
                          <TableCell className="text-gray-600 dark:text-gray-300 px-4 py-3">
                            <div className="text-sm">{user.department ? user.department.split(' / ')[1] || user.department : '-'}</div>
                          </TableCell>
                          <TableCell className="px-4 py-3">
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                              user.is_active !== false
                                ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                                : 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
                            }`}>
                              {user.is_active !== false ? 'Active' : 'Inactive'}
                            </span>
                          </TableCell>
                          <TableCell className="space-x-1 rtl:space-x-reverse px-4 py-3">
                            <Button variant="ghost" size="icon" onClick={() => handleEditUser(user)} className="text-yellow-500 hover:text-yellow-700 dark:text-yellow-400 dark:hover:text-yellow-300 h-8 w-8">
                              <Edit3 className="h-4 w-4"/>
                            </Button>
                            <Button variant="ghost" size="icon" onClick={() => confirmDeleteUser(user)} className="text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 h-8 w-8">
                              <Trash className="h-4 w-4"/>
                            </Button>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              </div>
            )}
          </CardContent>
        </Card>
      </motion.div>

      <UserFormModal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        onSubmit={handleSubmitUserForm}
        user={currentUser}
        isEditing={isEditing}
        t={t}
        i18n={i18n}
      />

      <AlertDialog open={showDeleteConfirm} onOpenChange={setShowDeleteConfirm}>
        <AlertDialogContent className="dark:bg-gray-800 dark:text-white">
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center">
              <AlertTriangle className="text-red-500 mr-2" />
              {t('userManagement.confirmDeleteTitle') || "Confirm Deletion"}
            </AlertDialogTitle>
            <AlertDialogDescription className="dark:text-gray-300">
              {t('userManagement.confirmDeleteMsg', {email: userToDelete?.email}) || `Are you sure you want to delete user ${userToDelete?.email}? This action might not be fully reversible for the authenticated user.`}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel className="dark:bg-gray-700 dark:hover:bg-gray-600 dark:border-gray-600">{t('userManagement.cancelButton') || "Cancel"}</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteUser} className="bg-red-600 hover:bg-red-700 dark:bg-red-500 dark:hover:bg-red-600 text-white">
              {t('userManagement.deleteButton') || "Delete"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </motion.div>
  );
};

export default UserManagementPage;
