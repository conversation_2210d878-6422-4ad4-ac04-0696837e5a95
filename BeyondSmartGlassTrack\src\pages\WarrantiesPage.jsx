import React from 'react';
import { motion } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Shield, Clock, AlertCircle } from 'lucide-react';

const WarrantiesPage = () => {
  const { t } = useTranslation();

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="space-y-6"
    >
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            {t('warranties.title')}
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Warranty activation and claims management
          </p>
        </div>
      </div>

      <Card>
        <CardContent className="p-8 text-center">
          <Shield size={48} className="mx-auto text-blue-500 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            Warranties Module
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            This module will manage warranty activation, claims, and coverage tracking.
          </p>
          <div className="flex items-center justify-center gap-2 text-yellow-600">
            <Clock size={16} />
            <span className="text-sm">Coming Soon</span>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default WarrantiesPage;
