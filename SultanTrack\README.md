# SultanTrack - نظام إدارة مشاريع تركيب وصيانة UPVC

<div align="center">
  <img src="public/sultan-logo.svg" alt="SultanTrack Logo" width="120" />
  <h3>نظام إدارة مشاريع تركيب وصيانة UPVC</h3>
  <p>تطبيق ويب داخلي ثنائي اللغة (عربي/إنجليزي) لإدارة مشاريع تركيب وصيانة النوافذ والأبواب من نوع UPVC</p>
</div>

## 🌟 الميزات الرئيسية

- **ثنائي اللغة**: دعم كامل للغتين العربية والإنجليزية
- **لوحة تحكم تفاعلية**: إحصائيات ورسوم بيانية للمشاريع والكميات
- **إدارة المشاريع**: إضافة، عرض، تعديل، حذف المشاريع
- **تتبع التقدم**: تسجيل الزيارات الميدانية وتتبع التقدم لكل مشروع
- **إدارة الصيانة**: متابعة المشاريع في فترة الضمان وطلبات الصيانة
- **إدارة المستخدمين**: إدارة المستخدمين والصلاحيات
- **تصميم متجاوب**: يعمل على جميع أحجام الشاشات (الكمبيوتر، الجوال، الأجهزة اللوحية)
- **الوضع المظلم**: دعم الوضع المظلم للاستخدام الليلي

## 🚀 التقنيات المستخدمة

- **الواجهة**: React + Tailwind CSS
- **إدارة الحالة**: React Hooks
- **التوجيه**: React Router DOM
- **الرسوم البيانية**: Recharts
- **الترجمة**: i18next
- **قاعدة البيانات**: Supabase (PostgreSQL)
- **المصادقة**: Supabase Auth
- **تخزين الملفات**: Supabase Storage

## 🛠️ متطلبات التشغيل

- Node.js (الإصدار 18 أو أحدث)
- حساب Supabase (مجاني)

## ⚙️ التثبيت والإعداد

1. **استنساخ المشروع**

```bash
git clone https://github.com/yourusername/sultan-track.git
cd sultan-track
```

2. **تثبيت التبعيات**

```bash
npm install
```

3. **إعداد متغيرات البيئة**

قم بإنشاء ملف `.env.local` في المجلد الرئيسي وأضف بيانات Supabase الخاصة بك:

```
VITE_SUPABASE_URL=your-supabase-url
VITE_SUPABASE_ANON_KEY=your-supabase-anon-key
```

4. **إعداد قاعدة البيانات**

قم بإنشاء الجداول التالية في Supabase:

- `projects`: لتخزين بيانات المشاريع
- `project_visits`: لتخزين بيانات الزيارات الميدانية
- `maintenance_requests`: لتخزين طلبات الصيانة

يمكنك استخدام ملف `sample-data.sql` لإضافة بيانات تجريبية.

5. **تشغيل التطبيق في وضع التطوير**

```bash
npm run dev
```

6. **بناء التطبيق للإنتاج**

```bash
npm run build
```

## 👥 أدوار المستخدمين

- **مدير (Manager)**: وصول كامل لجميع الميزات
- **مشرف (Supervisor)**: إدارة المشاريع والزيارات الميدانية وطلبات الصيانة
- **مدخل بيانات (Data Entry)**: إضافة وتعديل المشاريع
- **فني (Technician)**: تسجيل الزيارات الميدانية وعرض تفاصيل المشاريع

## 📱 دعم الأجهزة المحمولة

التطبيق متجاوب بالكامل ويعمل على جميع أحجام الشاشات:

- أجهزة الكمبيوتر المكتبية والمحمولة
- الأجهزة اللوحية
- الهواتف الذكية

## 🌙 الوضع المظلم

يدعم التطبيق الوضع المظلم الذي يمكن تفعيله من خلال زر التبديل في شريط التنقل العلوي.

## 📄 الترخيص

هذا المشروع مرخص بموجب [MIT License](LICENSE).

---

<div align="center">
  <p>تم تطويره بواسطة [اسمك/شركتك] © 2023</p>
</div>
