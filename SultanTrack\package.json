{"name": "web-app", "type": "module", "version": "0.0.0", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.0.3", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.0.5", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.1.5", "@supabase/supabase-js": "^2.49.8", "class-variance-authority": "^0.7.1", "clsx": "^2.0.0", "file-saver": "^2.0.5", "framer-motion": "^10.16.4", "i18next": "^23.7.6", "i18next-browser-languagedetector": "^7.2.0", "jspdf": "^2.5.1", "jspdf-autotable": "^3.8.2", "lucide-react": "^0.292.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-i18next": "^13.5.0", "react-router-dom": "^6.16.0", "recharts": "^2.10.1", "tailwind-merge": "^1.14.0", "tailwindcss-animate": "^1.0.7", "xlsx": "^0.18.5"}, "devDependencies": {"@types/node": "^20.8.3", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.0.3", "autoprefixer": "^10.4.16", "eslint": "^8.57.1", "eslint-config-react-app": "^7.0.1", "postcss": "^8.4.31", "tailwindcss": "^3.3.3", "terser": "^5.39.0", "vite": "^4.4.5"}}