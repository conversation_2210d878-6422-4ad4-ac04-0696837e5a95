import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Calendar, MapPin, FileText, Package } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/components/ui/use-toast';
import { getAllProjects } from '@/lib/supabaseDatabase';
import { supabase } from '@/lib/supabase';

const AddVisitModal = ({ isOpen, onClose, onSuccess }) => {
  const { t, i18n } = useTranslation();
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [projects, setProjects] = useState([]);
  const [loadingProjects, setLoadingProjects] = useState(true);

  // بيانات النموذج
  const [formData, setFormData] = useState({
    project_id: '',
    visit_date: new Date().toISOString().split('T')[0],
    project_status: '',
    material_shortages: {
      quantity_pvc: 0,
      quantity_shutter: 0,
      quantity_sgs: 0,
      quantity_doors: 0,
      quantity_glass: 0
    },
    materials_installed: {
      quantity_pvc: 0,
      quantity_shutter: 0,
      quantity_sgs: 0,
      quantity_doors: 0,
      quantity_glass: 0
    },
    notes: ''
  });

  // جلب المشاريع عند فتح النافذة
  useEffect(() => {
    if (isOpen) {
      fetchProjects();
    }
  }, [isOpen]);

  const fetchProjects = async () => {
    try {
      setLoadingProjects(true);
      const projectsData = await getAllProjects();
      setProjects(projectsData || []);
    } catch (error) {
      console.error('Error fetching projects:', error);
      toast({
        title: t('dashboard.addVisitModal.errorTitle'),
        description: 'فشل في جلب المشاريع',
        variant: "destructive"
      });
    } finally {
      setLoadingProjects(false);
    }
  };

  // معالجة تغيير القيم
  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleMaterialChange = (material, value, type = 'installed') => {
    const fieldName = type === 'installed' ? 'materials_installed' : 'material_shortages';
    setFormData(prev => ({
      ...prev,
      [fieldName]: {
        ...prev[fieldName],
        [material]: parseInt(value) || 0
      }
    }));
  };

  // حفظ الزيارة
  const handleSave = async () => {
    // التحقق من صحة البيانات
    if (!formData.project_id || !formData.visit_date) {
      toast({
        title: "خطأ في البيانات",
        description: "يرجى اختيار مشروع وتاريخ الزيارة",
        variant: "destructive"
      });
      return;
    }

    try {
      setLoading(true);

      // إعداد بيانات الزيارة
      const visitData = {
        project_id: formData.project_id,
        visit_date: formData.visit_date,
        project_status: formData.project_status,
        material_shortages: formData.material_shortages,
        materials_installed: formData.materials_installed,
        notes: formData.notes,
        created_at: new Date().toISOString()
      };

      // حفظ في قاعدة البيانات
      const { data, error } = await supabase
        .from('project_visits')
        .insert([visitData])
        .select();

      if (error) throw error;

      // إشعار النجاح
      toast({
        title: "تمت إضافة الزيارة",
        description: "تم تسجيل الزيارة الميدانية بنجاح",
        variant: "default"
      });

      // إعادة تعيين النموذج وإغلاق النافذة
      resetForm();
      onClose();
      if (onSuccess) onSuccess();

    } catch (error) {
      console.error('Error saving visit:', error);
      toast({
        title: "خطأ في إضافة الزيارة",
        description: error.message,
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  // إعادة تعيين النموذج
  const resetForm = () => {
    setFormData({
      project_id: '',
      visit_date: new Date().toISOString().split('T')[0],
      project_status: '',
      material_shortages: {
        quantity_pvc: 0,
        quantity_shutter: 0,
        quantity_sgs: 0,
        quantity_doors: 0,
        quantity_glass: 0
      },
      materials_installed: {
        quantity_pvc: 0,
        quantity_shutter: 0,
        quantity_sgs: 0,
        quantity_doors: 0,
        quantity_glass: 0
      },
      notes: ''
    });
  };

  // إغلاق النافذة
  const handleClose = () => {
    resetForm();
    onClose();
  };

  // خيارات حالة المشروع
  const statusOptions = [
    { value: 'مستمر', label: t('visits.ongoing') },
    { value: 'نواقص جزئي', label: t('visits.partialShortage') },
    { value: 'متوقف على نواقص', label: t('visits.stoppedShortage') },
    { value: 'منجز', label: t('visits.completed') }
  ];

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
          onClick={handleClose}
        >
          <motion.div
            initial={{ scale: 0.95, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.95, opacity: 0 }}
            className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto"
            onClick={(e) => e.stopPropagation()}
            dir={i18n.dir()}
          >
            <Card className="border-0 shadow-none">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
                <CardTitle className="text-xl font-bold text-gray-800 dark:text-white">
                  {t('visits.addNewVisit')}
                </CardTitle>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleClose}
                  className="h-8 w-8 p-0"
                >
                  <X className="h-4 w-4" />
                </Button>
              </CardHeader>

              <CardContent className="space-y-6">
                {/* اختيار المشروع */}
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center gap-2">
                    <MapPin className="h-4 w-4" />
                    {t('visits.selectProject')}
                  </label>
                  {loadingProjects ? (
                    <div className="flex items-center justify-center py-4">
                      <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-blue-500"></div>
                    </div>
                  ) : (
                    <select
                      value={formData.project_id}
                      onChange={(e) => handleInputChange('project_id', e.target.value)}
                      className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                    >
                      <option value="">-- {t('visits.selectProject')} --</option>
                      {projects.map((project) => (
                        <option key={project.id} value={project.id}>
                          {project.project_number} - {project.client_name}
                        </option>
                      ))}
                    </select>
                  )}
                  {projects.length === 0 && !loadingProjects && (
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      لا توجد مشاريع متاحة
                    </p>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* تاريخ الزيارة */}
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center gap-2">
                      <Calendar className="h-4 w-4" />
                      {t('visits.visitDate')}
                    </label>
                    <input
                      type="date"
                      value={formData.visit_date}
                      onChange={(e) => handleInputChange('visit_date', e.target.value)}
                      className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                    />
                  </div>

                  {/* حالة المشروع */}
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      {t('visits.projectStatus')}
                    </label>
                    <select
                      value={formData.project_status}
                      onChange={(e) => handleInputChange('project_status', e.target.value)}
                      className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                    >
                      <option value="">{t('visits.selectStatus')}</option>
                      {statusOptions.map((option) => (
                        <option key={option.value} value={option.value}>
                          {option.label}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>

                {/* النواقص من الخامات */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center">
                      <Package className="h-5 w-5 mr-2" />
                      {t('visits.materialShortages')}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
                      {[
                        { key: 'quantity_pvc', label: t('visits.pvcUnits') },
                        { key: 'quantity_shutter', label: t('visits.shutterUnits') },
                        { key: 'quantity_sgs', label: t('visits.sgsUnits') },
                        { key: 'quantity_doors', label: t('visits.doorsUnits') },
                        { key: 'quantity_glass', label: t('visits.glassUnits') }
                      ].map(material => (
                        <div key={material.key} className="space-y-2">
                          <Label htmlFor={`shortage_${material.key}`}>{material.label}</Label>
                          <Input
                            id={`shortage_${material.key}`}
                            type="number"
                            min="0"
                            step="1"
                            value={formData.material_shortages[material.key]}
                            onChange={(e) => handleMaterialChange(material.key, e.target.value, 'shortages')}
                          />
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                {/* الخامات المركبة في هذا اليوم */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center">
                      <Package className="h-5 w-5 mr-2" />
                      {t('visits.materialsInstalled')}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
                      {[
                        { key: 'quantity_pvc', label: t('visits.pvcUnits') },
                        { key: 'quantity_shutter', label: t('visits.shutterUnits') },
                        { key: 'quantity_sgs', label: t('visits.sgsUnits') },
                        { key: 'quantity_doors', label: t('visits.doorsUnits') },
                        { key: 'quantity_glass', label: t('visits.glassUnits') }
                      ].map(material => (
                        <div key={material.key} className="space-y-2">
                          <Label htmlFor={`installed_${material.key}`}>{material.label}</Label>
                          <Input
                            id={`installed_${material.key}`}
                            type="number"
                            min="0"
                            step="1"
                            value={formData.materials_installed[material.key]}
                            onChange={(e) => handleMaterialChange(material.key, e.target.value, 'installed')}
                          />
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                {/* ملاحظات الزيارة */}
                <div className="space-y-2">
                  <Label htmlFor="notes" className="flex items-center gap-2">
                    <FileText className="h-4 w-4" />
                    {t('visits.visitNotes')}
                  </Label>
                  <Textarea
                    id="notes"
                    value={formData.notes}
                    onChange={(e) => handleInputChange('notes', e.target.value)}
                    placeholder={t('visits.notesPlaceholder')}
                    rows={4}
                  />
                </div>

                {/* أزرار الإجراءات */}
                <div className="flex justify-end gap-3 pt-4 border-t border-gray-200 dark:border-gray-700">
                  <Button
                    variant="outline"
                    onClick={handleClose}
                    disabled={loading}
                  >
                    {t('common.cancel')}
                  </Button>
                  <Button
                    onClick={handleSave}
                    disabled={loading || !formData.project_id || !formData.visit_date}
                    className="bg-blue-600 hover:bg-blue-700"
                  >
                    {loading ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                        {t('common.loading')}
                      </>
                    ) : (
                      t('visits.saveVisit')
                    )}
                  </Button>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default AddVisitModal;
