import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Calendar, MapPin, Filter, RotateCcw, TrendingUp } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { getAllProjects } from '@/lib/supabaseDatabase';

const DashboardFilters = ({ onFiltersChange, currentFilters }) => {
  const { t, i18n } = useTranslation();
  const [filters, setFilters] = useState({
    timeRange: 'month',
    startDate: '',
    endDate: '',
    city: 'all',
    projectStatus: 'all',
    materialType: 'all',
    ...currentFilters
  });
  const [cities, setCities] = useState([]);
  const [loading, setLoading] = useState(false);

  // جلب المدن المتاحة من المشاريع
  useEffect(() => {
    const fetchCities = async () => {
      try {
        const projects = await getAllProjects();
        const uniqueCities = [...new Set(projects
          .map(p => p.project_location)
          .filter(location => location && location.trim() !== '')
        )];
        setCities(uniqueCities);
      } catch (error) {
        console.error('Error fetching cities:', error);
      }
    };
    fetchCities();
  }, []);

  // تطبيق الفلاتر
  const applyFilters = () => {
    setLoading(true);
    onFiltersChange(filters);
    setTimeout(() => setLoading(false), 500);
  };

  // إعادة تعيين الفلاتر
  const resetFilters = () => {
    const defaultFilters = {
      timeRange: 'month',
      startDate: '',
      endDate: '',
      city: 'all',
      projectStatus: 'all',
      materialType: 'all'
    };
    setFilters(defaultFilters);
    onFiltersChange(defaultFilters);
  };

  // تحديث فلتر واحد
  const updateFilter = (key, value) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const timeRangeOptions = [
    { value: 'week', label: t('dashboard.filters.lastWeek'), icon: '📅' },
    { value: 'month', label: t('dashboard.filters.lastMonth'), icon: '📆' },
    { value: 'quarter', label: t('dashboard.filters.lastQuarter'), icon: '🗓️' },
    { value: 'year', label: t('dashboard.filters.lastYear'), icon: '📊' },
    { value: 'custom', label: t('dashboard.filters.customRange'), icon: '⚙️' }
  ];

  const statusOptions = [
    { value: 'all', label: t('dashboard.filters.allStatuses'), icon: '📋' },
    { value: 'pending', label: t('dashboard.filters.pending'), icon: '⏳' },
    { value: 'in_progress', label: t('dashboard.filters.inProgress'), icon: '🔄' },
    { value: 'completed', label: t('dashboard.filters.completed'), icon: '✅' },
    { value: 'on_hold', label: t('dashboard.filters.onHold'), icon: '⏸️' }
  ];

  const materialOptions = [
    { value: 'all', label: t('dashboard.filters.allMaterials'), icon: '📦' },
    { value: 'pvc', label: t('dashboard.pvc'), icon: '🔲' },
    { value: 'shutter', label: t('dashboard.shutter'), icon: '🚪' },
    { value: 'sgs', label: t('dashboard.sgs'), icon: '🔧' },
    { value: 'doors', label: t('dashboard.doors'), icon: '🚪' },
    { value: 'glass', label: t('dashboard.glass'), icon: '🪟' }
  ];

  return (
    <motion.div
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <Card className="shadow-lg dark:bg-gray-800 mb-6 border-l-4 border-blue-500 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-800 dark:to-gray-700">
        <CardHeader>
          <CardTitle className="text-xl font-semibold text-gray-700 dark:text-white flex items-center gap-2">
            <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
              <Filter className="h-5 w-5 text-blue-600 dark:text-blue-400" />
            </div>
            {t('dashboard.filters.title')}
            <span className="text-sm bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded-full ml-auto">
              متقدم
            </span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4 mb-4">
            {/* فلتر الفترة الزمنية */}
            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center gap-1">
                <Calendar className="h-4 w-4" />
                {t('dashboard.filters.timeRange')}
              </Label>
              <Select value={filters.timeRange} onValueChange={(value) => updateFilter('timeRange', value)}>
                <SelectTrigger className="w-full">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {timeRangeOptions.map(option => (
                    <SelectItem key={option.value} value={option.value}>
                      <span className="flex items-center gap-2">
                        <span>{option.icon}</span>
                        {option.label}
                      </span>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* التواريخ المخصصة */}
            {filters.timeRange === 'custom' && (
              <>
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    {t('dashboard.filters.startDate')}
                  </Label>
                  <Input
                    type="date"
                    value={filters.startDate}
                    onChange={(e) => updateFilter('startDate', e.target.value)}
                    className="w-full"
                  />
                </div>
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    {t('dashboard.filters.endDate')}
                  </Label>
                  <Input
                    type="date"
                    value={filters.endDate}
                    onChange={(e) => updateFilter('endDate', e.target.value)}
                    className="w-full"
                  />
                </div>
              </>
            )}

            {/* فلتر المدينة */}
            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center gap-1">
                <MapPin className="h-4 w-4" />
                {t('dashboard.filters.city')}
              </Label>
              <Select value={filters.city} onValueChange={(value) => updateFilter('city', value)}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder={t('dashboard.filters.selectCity')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">
                    <span className="flex items-center gap-2">
                      <span>🌍</span>
                      {t('dashboard.filters.allCities')}
                    </span>
                  </SelectItem>
                  {cities.map(city => (
                    <SelectItem key={city} value={city}>
                      <span className="flex items-center gap-2">
                        <span>🏙️</span>
                        {city}
                      </span>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* فلتر حالة المشروع */}
            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center gap-1">
                <TrendingUp className="h-4 w-4" />
                {t('dashboard.filters.projectStatus')}
              </Label>
              <Select value={filters.projectStatus} onValueChange={(value) => updateFilter('projectStatus', value)}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder={t('dashboard.filters.selectStatus')} />
                </SelectTrigger>
                <SelectContent>
                  {statusOptions.map(option => (
                    <SelectItem key={option.value} value={option.value}>
                      <span className="flex items-center gap-2">
                        <span>{option.icon}</span>
                        {option.label}
                      </span>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* فلتر نوع المادة */}
            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                {t('dashboard.filters.materialType')}
              </Label>
              <Select value={filters.materialType} onValueChange={(value) => updateFilter('materialType', value)}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder={t('dashboard.filters.selectMaterial')} />
                </SelectTrigger>
                <SelectContent>
                  {materialOptions.map(option => (
                    <SelectItem key={option.value} value={option.value}>
                      <span className="flex items-center gap-2">
                        <span>{option.icon}</span>
                        {option.label}
                      </span>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* أزرار التحكم */}
          <div className="flex flex-wrap gap-3 pt-4 border-t border-gray-200 dark:border-gray-600">
            <motion.div
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <Button
                onClick={applyFilters}
                disabled={loading}
                className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white shadow-md hover:shadow-lg transition-all duration-200 w-full sm:w-auto"
              >
                {loading ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                ) : (
                  <Filter className="h-4 w-4 mr-2" />
                )}
                {t('dashboard.filters.apply')}
              </Button>
            </motion.div>

            <motion.div
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <Button
                onClick={resetFilters}
                variant="outline"
                className="border-gray-300 hover:bg-gray-50 dark:border-gray-600 dark:hover:bg-gray-700 w-full sm:w-auto"
              >
                <RotateCcw className="h-4 w-4 mr-2" />
                {t('dashboard.filters.reset')}
              </Button>
            </motion.div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default DashboardFilters;
