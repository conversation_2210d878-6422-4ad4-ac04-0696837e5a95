import React, { useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { useToast } from '@/components/ui/use-toast';
import {
  UploadCloud,
  FileText,
  Image as ImageIcon,
  X,
  Download,
  Eye,
  Trash2,
  AlertCircle,
  CheckCircle
} from 'lucide-react';
import { supabase } from '@/lib/supabase';
import { saveFileRecord, deleteFile } from '@/lib/fileService';

const FileUploadManager = ({
  projectId = null,
  files = [],
  onFilesChange,
  maxFiles = 10,
  maxFileSize = 10 * 1024 * 1024, // 10MB
  allowedTypes = ['.pdf', '.jpg', '.jpeg', '.png', '.doc', '.docx'],
  showPreview = true,
  disabled = false
}) => {
  const { t } = useTranslation();
  const { toast } = useToast();
  const [uploading, setUploading] = useState(false);
  const [dragActive, setDragActive] = useState(false);

  // رفع ملف إلى Supabase Storage
  const uploadFileToSupabase = async (file, projectId) => {
    try {
      const fileExt = file.name.split('.').pop().toLowerCase();
      const timestamp = Date.now();
      const fileName = `${projectId}/${timestamp}_${file.name.replace(/[^a-zA-Z0-9.-]/g, '_')}`;

      // تحديد البucket حسب نوع الملف
      const bucket = file.type.startsWith('image/') ? 'project-images' : 'project-documents';

      const { data, error } = await supabase.storage
        .from(bucket)
        .upload(fileName, file, {
          cacheControl: '3600',
          upsert: false
        });

      if (error) throw error;

      // الحصول على URL عام للملف
      const { data: urlData } = supabase.storage
        .from(bucket)
        .getPublicUrl(fileName);

      // حفظ معلومات الملف في قاعدة البيانات
      const fileRecord = await saveFileRecord({
        projectId: projectId,
        name: file.name,
        path: data.path,
        size: file.size,
        type: file.type,
        bucket: bucket,
        url: urlData.publicUrl,
        uploadedBy: null
      });

      if (!fileRecord.success) {
        // إذا فشل حفظ السجل، احذف الملف من التخزين
        await supabase.storage.from(bucket).remove([fileName]);
        throw new Error(fileRecord.error);
      }

      return {
        id: fileRecord.data.id, // استخدام ID من قاعدة البيانات
        path: data.path,
        bucket: bucket,
        name: file.name,
        size: file.size,
        type: file.type,
        url: urlData.publicUrl,
        uploaded: true,
        uploadedAt: fileRecord.data.created_at
      };
    } catch (error) {
      console.error('Error uploading file:', error);
      throw error;
    }
  };

  // حذف ملف من Supabase Storage وقاعدة البيانات
  const deleteFileFromSupabase = async (fileData) => {
    try {
      if (fileData.uploaded && fileData.id) {
        // استخدام دالة deleteFile من fileService
        const result = await deleteFile(fileData.id);
        if (!result.success) {
          throw new Error(result.error);
        }
      } else if (fileData.bucket && fileData.path) {
        // حذف من التخزين فقط إذا لم يكن محفوظ في قاعدة البيانات
        const { error } = await supabase.storage
          .from(fileData.bucket)
          .remove([fileData.path]);

        if (error) throw error;
      }
    } catch (error) {
      console.error('Error deleting file:', error);
      throw error;
    }
  };

  // معالجة اختيار الملفات
  const handleFileSelect = useCallback(async (selectedFiles) => {
    if (disabled) return;

    const fileArray = Array.from(selectedFiles);
    const validFiles = [];
    const errors = [];

    // التحقق من صحة الملفات
    for (const file of fileArray) {
      // التحقق من حجم الملف
      if (file.size > maxFileSize) {
        errors.push(`${file.name}: File too large (max ${Math.round(maxFileSize / 1024 / 1024)}MB)`);
        continue;
      }

      // التحقق من نوع الملف
      const fileExt = '.' + file.name.split('.').pop().toLowerCase();
      if (!allowedTypes.includes(fileExt)) {
        errors.push(`${file.name}: File type not allowed`);
        continue;
      }

      // التحقق من عدد الملفات
      if (files.length + validFiles.length >= maxFiles) {
        errors.push(`Maximum ${maxFiles} files allowed`);
        break;
      }

      validFiles.push({
        id: `temp_${Date.now()}_${Math.random()}`,
        file: file,
        name: file.name,
        size: file.size,
        type: file.type,
        uploaded: false,
        preview: file.type.startsWith('image/') ? URL.createObjectURL(file) : null
      });
    }

    // عرض الأخطاء
    if (errors.length > 0) {
      toast({
        title: "File Upload Errors",
        description: errors.join('\n'),
        variant: "destructive"
      });
    }

    // إضافة الملفات الصحيحة
    if (validFiles.length > 0) {
      const newFiles = [...files, ...validFiles];
      onFilesChange(newFiles);

      // رفع الملفات إلى Supabase إذا كان projectId متوفر
      if (projectId) {
        setUploading(true);
        for (const fileData of validFiles) {
          try {
            const uploadResult = await uploadFileToSupabase(fileData.file, projectId);

            // تحديث بيانات الملف بعد الرفع
            const updatedFiles = newFiles.map(f =>
              f.id === fileData.id ? { ...f, ...uploadResult } : f
            );
            onFilesChange(updatedFiles);

          } catch (error) {
            console.error(`Error uploading ${fileData.name}:`, error);
            toast({
              title: "Upload Error",
              description: `Failed to upload ${fileData.name}`,
              variant: "destructive"
            });
          }
        }
        setUploading(false);
      }
    }
  }, [files, onFilesChange, maxFiles, maxFileSize, allowedTypes, projectId, disabled, toast]);

  // معالجة حذف ملف
  const handleRemoveFile = async (fileId) => {
    const fileToRemove = files.find(f => f.id === fileId);
    if (!fileToRemove) return;

    try {
      // حذف من Supabase إذا كان مرفوع
      if (fileToRemove.uploaded) {
        await deleteFileFromSupabase(fileToRemove);
      }

      // إزالة من القائمة
      const updatedFiles = files.filter(f => f.id !== fileId);
      onFilesChange(updatedFiles);

      // تنظيف preview URL
      if (fileToRemove.preview && fileToRemove.preview.startsWith('blob:')) {
        URL.revokeObjectURL(fileToRemove.preview);
      }

    } catch (error) {
      console.error('Error removing file:', error);
      toast({
        title: "Error",
        description: "Failed to remove file",
        variant: "destructive"
      });
    }
  };

  // معالجة السحب والإفلات
  const handleDrag = useCallback((e) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback((e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (disabled) return;

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileSelect(e.dataTransfer.files);
    }
  }, [handleFileSelect, disabled]);

  // معالجة اختيار الملفات من input
  const handleInputChange = (e) => {
    if (e.target.files) {
      handleFileSelect(e.target.files);
    }
  };

  // تنسيق حجم الملف
  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="space-y-4">
      {/* منطقة رفع الملفات */}
      <div
        className={`relative border-2 border-dashed rounded-lg p-6 transition-colors ${
          dragActive
            ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
            : 'border-gray-300 dark:border-gray-600 hover:border-blue-400'
        } ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
      >
        <div className="text-center">
          <UploadCloud className={`mx-auto h-12 w-12 ${dragActive ? 'text-blue-500' : 'text-gray-400'}`} />
          <div className="mt-4">
            <label htmlFor="file-upload" className={`cursor-pointer ${disabled ? 'cursor-not-allowed' : ''}`}>
              <span className="mt-2 block text-sm font-medium text-gray-900 dark:text-gray-100">
                {uploading ? 'Uploading...' : 'Drop files here or click to browse'}
              </span>
              <input
                id="file-upload"
                name="file-upload"
                type="file"
                className="sr-only"
                multiple
                disabled={disabled || uploading}
                onChange={handleInputChange}
                accept={allowedTypes.join(',')}
              />
            </label>
            <p className="mt-1 text-xs text-gray-500">
              {allowedTypes.join(', ')} up to {Math.round(maxFileSize / 1024 / 1024)}MB each. Max {maxFiles} files.
            </p>
          </div>
        </div>

        {/* مؤشر التحميل */}
        {uploading && (
          <div className="absolute inset-0 bg-white/80 dark:bg-gray-900/80 flex items-center justify-center rounded-lg">
            <div className="flex items-center space-x-2">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
              <span className="text-sm text-gray-600 dark:text-gray-400">Uploading...</span>
            </div>
          </div>
        )}
      </div>

      {/* عرض الملفات المرفوعة */}
      {files.length > 0 && (
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100">
              Uploaded Files ({files.length}/{maxFiles})
            </h4>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
            <AnimatePresence>
              {files.map((file) => (
                <motion.div
                  key={file.id}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.8 }}
                  className="relative bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-3 shadow-sm"
                >
                  {/* معاينة الملف */}
                  {showPreview && file.type.startsWith('image/') && (
                    <div className="aspect-video bg-gray-100 dark:bg-gray-700 rounded-md mb-2 overflow-hidden">
                      <img
                        src={file.preview || file.url}
                        alt={file.name}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          e.target.style.display = 'none';
                        }}
                      />
                    </div>
                  )}

                  {/* معلومات الملف */}
                  <div className="space-y-1">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center space-x-2 min-w-0 flex-1">
                        {file.type.startsWith('image/') ? (
                          <ImageIcon className="h-4 w-4 text-blue-500 flex-shrink-0" />
                        ) : (
                          <FileText className="h-4 w-4 text-green-500 flex-shrink-0" />
                        )}
                        <span className="text-xs font-medium text-gray-900 dark:text-gray-100 truncate">
                          {file.name}
                        </span>
                      </div>

                      {/* حالة الرفع */}
                      <div className="flex items-center space-x-1 ml-2">
                        {file.uploaded ? (
                          <CheckCircle className="h-4 w-4 text-green-500" />
                        ) : (
                          <AlertCircle className="h-4 w-4 text-yellow-500" />
                        )}
                      </div>
                    </div>

                    <p className="text-xs text-gray-500">
                      {formatFileSize(file.size)}
                    </p>

                    {/* أزرار الإجراءات */}
                    <div className="flex items-center justify-between pt-2">
                      <div className="flex space-x-1">
                        {file.uploaded && file.url && (
                          <>
                            <Button
                              size="sm"
                              variant="ghost"
                              className="h-6 w-6 p-0"
                              onClick={() => window.open(file.url, '_blank')}
                            >
                              <Eye className="h-3 w-3" />
                            </Button>
                            <Button
                              size="sm"
                              variant="ghost"
                              className="h-6 w-6 p-0"
                              onClick={() => {
                                const link = document.createElement('a');
                                link.href = file.url;
                                link.download = file.name;
                                link.click();
                              }}
                            >
                              <Download className="h-3 w-3" />
                            </Button>
                          </>
                        )}
                      </div>

                      <Button
                        size="sm"
                        variant="ghost"
                        className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
                        onClick={() => handleRemoveFile(file.id)}
                        disabled={uploading}
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                </motion.div>
              ))}
            </AnimatePresence>
          </div>
        </div>
      )}
    </div>
  );
};

export default FileUploadManager;
