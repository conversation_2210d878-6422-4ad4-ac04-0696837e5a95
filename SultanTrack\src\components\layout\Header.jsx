
import React, { useState, useEffect } from 'react';
import { Sun, Moon, Globe, UserCircle, User } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { useTranslation } from 'react-i18next';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";


const Header = ({ userRole, onRoleChange }) => {
  const [darkMode, setDarkMode] = useState(localStorage.getItem('theme') === 'dark');
  const { i18n, t } = useTranslation();
  const navigate = useNavigate();

  // دالة مؤقتة لتغيير الدور للاختبار
  const handleRoleChange = (newRole) => {
    localStorage.setItem('userRole', newRole);
    if (onRoleChange) {
      onRoleChange(newRole);
    }
    window.location.reload(); // إعادة تحميل الصفحة لتطبيق التغييرات
  };

  useEffect(() => {
    if (darkMode) {
      document.documentElement.classList.add('dark');
      localStorage.setItem('theme', 'dark');
    } else {
      document.documentElement.classList.remove('dark');
      localStorage.setItem('theme', 'light');
    }
  }, [darkMode]);

  const toggleDarkMode = () => {
    setDarkMode(!darkMode);
  };

  const changeLanguage = (lng) => {
    i18n.changeLanguage(lng);
    localStorage.setItem('i18nextLng', lng);
    document.documentElement.lang = lng;
    document.documentElement.dir = lng === 'ar' ? 'rtl' : 'ltr';
  };

  return (
    <header className="bg-white dark:bg-gray-800 shadow-md px-2 sm:px-4 py-3 sm:py-4 flex justify-between items-center sticky top-0 z-10">
      <div className="flex items-center min-w-0 space-x-4">
        {/* Placeholder for breadcrumbs or page title */}
        <h1 className="text-lg sm:text-xl font-semibold text-gray-700 dark:text-gray-200 truncate">SultanTrack</h1>
        {userRole && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="text-sm bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded-full hover:bg-blue-200 dark:hover:bg-blue-800">
                {t(`userManagement.roles.${userRole}`) || userRole} ⚡
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align={i18n.dir() === 'rtl' ? 'start' : 'end'}>
              <DropdownMenuItem onClick={() => handleRoleChange('manager')} className={userRole === 'manager' ? 'bg-accent' : ''}>
                👑 {t('userManagement.roles.manager') || 'Manager'}
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleRoleChange('supervisor')} className={userRole === 'supervisor' ? 'bg-accent' : ''}>
                👨‍💼 {t('userManagement.roles.supervisor') || 'Supervisor'}
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleRoleChange('data_entry')} className={userRole === 'data_entry' ? 'bg-accent' : ''}>
                📝 {t('userManagement.roles.data_entry') || 'Data Entry'}
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleRoleChange('technician')} className={userRole === 'technician' ? 'bg-accent' : ''}>
                🔧 {t('userManagement.roles.technician') || 'Technician'}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        )}
      </div>
      <div className={`flex items-center ${i18n.language === 'ar' ? 'space-x-reverse space-x-2' : 'space-x-2'} sm:space-x-3`}>
        <Button variant="ghost" size="icon" onClick={toggleDarkMode} aria-label="Toggle dark mode" className="h-8 w-8 sm:h-10 sm:w-10">
          {darkMode ? <Sun className="h-4 w-4 sm:h-5 sm:w-5 text-yellow-400" /> : <Moon className="h-4 w-4 sm:h-5 sm:w-5 text-gray-600" />}
        </Button>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon" aria-label="Change language" className="h-8 w-8 sm:h-10 sm:w-10">
              <Globe className="h-4 w-4 sm:h-5 sm:w-5 text-gray-600 dark:text-gray-300" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align={i18n.dir() === 'rtl' ? 'start' : 'end'}>
            <DropdownMenuItem onClick={() => changeLanguage('en')} className={i18n.language === 'en' ? 'bg-accent' : ''}>
              English
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => changeLanguage('ar')} className={i18n.language === 'ar' ? 'bg-accent' : ''}>
              العربية
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon" aria-label="User menu" className="h-8 w-8 sm:h-10 sm:w-10">
              <UserCircle className="h-5 w-5 sm:h-6 sm:w-6 text-gray-600 dark:text-gray-300" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align={i18n.dir() === 'rtl' ? 'start' : 'end'}>
            <DropdownMenuItem onClick={() => navigate('/profile')} className="cursor-pointer">
              <User className={`h-4 w-4 ${i18n.language === 'ar' ? 'ml-2' : 'mr-2'}`} />
              {t('sidebar.profile') || 'الملف الشخصي'}
            </DropdownMenuItem>
            <DropdownMenuItem disabled>Settings (Soon)</DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </header>
  );
};

export default Header;
