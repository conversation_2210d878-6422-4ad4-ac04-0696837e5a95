import React from 'react';
import { motion } from 'framer-motion';

const ProgressCircle = ({ percentage, size = 100, strokeWidth = 8, color = '#3b82f6', label, value, total, unit = '' }) => {
  // Calculate the circumference of the circle
  const radius = (size - strokeWidth) / 2;
  const circumference = radius * 2 * Math.PI;
  
  // Calculate the stroke-dashoffset based on the percentage
  const offset = circumference - (percentage / 100) * circumference;
  
  return (
    <div className="flex flex-col items-center justify-center">
      <div className="relative" style={{ width: size, height: size }}>
        {/* Background circle */}
        <svg width={size} height={size} className="transform -rotate-90">
          <circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            fill="none"
            stroke="currentColor"
            strokeWidth={strokeWidth}
            className="text-gray-200 dark:text-gray-700"
          />
          
          {/* Progress circle */}
          <motion.circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            fill="none"
            stroke={color}
            strokeWidth={strokeWidth}
            strokeDasharray={circumference}
            initial={{ strokeDashoffset: circumference }}
            animate={{ strokeDashoffset: offset }}
            transition={{ duration: 1, ease: "easeOut" }}
            strokeLinecap="round"
          />
        </svg>
        
        {/* Percentage text in the middle */}
        <div className="absolute inset-0 flex flex-col items-center justify-center text-center">
          <span className="text-xl font-bold text-gray-800 dark:text-white">{percentage}%</span>
          <span className="text-xs text-gray-500 dark:text-gray-400">{value}/{total} {unit}</span>
        </div>
      </div>
      
      {/* Label below the circle */}
      {label && (
        <span className="mt-2 text-sm font-medium text-gray-700 dark:text-gray-300">{label}</span>
      )}
    </div>
  );
};

export default ProgressCircle;
