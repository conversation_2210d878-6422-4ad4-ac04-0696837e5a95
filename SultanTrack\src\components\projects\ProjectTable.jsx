import React from 'react';
import { motion } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ChevronsUpDown, ArrowUp, ArrowDown, Eye, Edit2, Trash2 } from 'lucide-react';

const ProjectTable = ({ projects, sortConfig, requestSort, confirmDeleteProject, itemVariants }) => {
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();

  const getStatusBadgeVariant = (status) => {
    switch (status?.toLowerCase()) {
      case 'not started':
      case 'لم يبدأ':
      case 'notstarted':
      case 'pending':
      case 'معلق':
        return 'secondary';
      case 'in progress':
      case 'قيد التنفيذ':
      case 'inprogress':
      case 'in_progress':
        return 'default';
      case 'delayed':
      case 'متأخر':
        return 'destructive';
      case 'completed':
      case 'مكتمل':
        return 'success';
      case 'on hold':
      case 'متوقف مؤقتاً':
      case 'onhold':
        return 'warning';
      default:
        return 'secondary';
    }
  };

  // دالة لترجمة حالة المشروع
  const getStatusTranslation = (status) => {
    const statusMap = {
      'completed': t('dashboard.completed') || 'مكتمل',
      'in progress': t('dashboard.inProgress') || 'قيد التنفيذ',
      'inprogress': t('dashboard.inProgress') || 'قيد التنفيذ',
      'in_progress': t('dashboard.inProgress') || 'قيد التنفيذ',
      'delayed': t('dashboard.delayed') || 'متأخر',
      'not started': t('dashboard.notStarted') || 'لم يبدأ',
      'notstarted': t('dashboard.notStarted') || 'لم يبدأ',
      'pending': t('dashboard.pending') || 'معلق',
      'on hold': t('dashboard.onHold') || 'متوقف مؤقتاً',
      'onhold': t('dashboard.onHold') || 'متوقف مؤقتاً',
      'مكتمل': t('dashboard.completed') || 'مكتمل',
      'قيد التنفيذ': t('dashboard.inProgress') || 'قيد التنفيذ',
      'متأخر': t('dashboard.delayed') || 'متأخر',
      'لم يبدأ': t('dashboard.notStarted') || 'لم يبدأ',
      'معلق': t('dashboard.pending') || 'معلق',
      'متوقف مؤقتاً': t('dashboard.onHold') || 'متوقف مؤقتاً'
    };
    return statusMap[status?.toLowerCase()] || status || t('common.notSpecified') || 'غير محدد';
  };

  const tableHeaders = [
    { key: 'project_number', label: t('allProjects.projectNumber') || "Project No." },
    { key: 'client_name', label: t('allProjects.clientName') || "Client" },
    { key: 'city', label: t('allProjects.city') || "City" },
    { key: 'status', label: t('allProjects.status') || "Status" },
    { key: 'progress_percentage', label: t('allProjects.progress') || "Progress %" },
    { key: 'notes', label: t('allProjects.notes') || "Notes" }
  ];

  return (
    <motion.div
      variants={itemVariants}
      className="bg-white dark:bg-gray-800/80 shadow-xl rounded-lg overflow-hidden backdrop-blur-md border border-gray-200 dark:border-gray-700/50"
    >
      <div className="overflow-x-auto">
        <Table>
          <TableHeader className="bg-gray-50 dark:bg-gray-700/50">
            <TableRow>
              {tableHeaders.map(col => (
                <TableHead
                  key={col.key}
                  onClick={() => requestSort(col.key)}
                  className="cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600/50 text-gray-600 dark:text-gray-300 font-semibold px-4 py-3"
                >
                  <div className="flex items-center">
                    {col.label}
                    {sortConfig.key === col.key ?
                      (sortConfig.direction === 'ascending' ? <ArrowUp className="ml-2 h-4 w-4" /> : <ArrowDown className="ml-2 h-4 w-4" />)
                      : <ChevronsUpDown className="ml-2 h-4 w-4 opacity-30" />}
                  </div>
                </TableHead>
              ))}
              <TableHead className="text-gray-600 dark:text-gray-300 font-semibold px-4 py-3">{t('allProjects.actions') || "Actions"}</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {projects.map((project) => (
              <motion.tr
                key={project.id}
                className="hover:bg-gray-50 dark:hover:bg-gray-700/30 transition-colors border-b dark:border-gray-700/50"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.3 }}
              >
                <TableCell className="font-medium text-gray-800 dark:text-gray-100 px-4 py-3">
                  <span
                    className="cursor-pointer hover:text-blue-600 hover:underline"
                    onClick={() => navigate(`/track-project/${project.id}`)}
                  >
                    {project.project_number}
                  </span>
                </TableCell>
                <TableCell className="text-gray-600 dark:text-gray-300 px-4 py-3">{project.client_name}</TableCell>
                <TableCell className="text-gray-600 dark:text-gray-300 px-4 py-3">{project.city || project.project_location || project.address || 'غير محدد'}</TableCell>
                <TableCell className="px-4 py-3">
                  <Badge variant={getStatusBadgeVariant(project.status)} className="capitalize text-xs">
                    {getStatusTranslation(project.status)}
                  </Badge>
                </TableCell>
                <TableCell className="text-gray-600 dark:text-gray-300 px-4 py-3">
                  <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2.5">
                    <div
                      className="bg-gradient-to-r from-blue-500 to-green-500 h-2.5 rounded-full transition-all duration-500 ease-out"
                      style={{ width: `${project.progress_percentage || 0}%` }}
                    ></div>
                  </div>
                  <span className="text-xs mt-1 block text-center">{project.progress_percentage || 0}%</span>
                </TableCell>
                <TableCell className="text-gray-500 dark:text-gray-400 truncate max-w-xs px-4 py-3">{project.notes}</TableCell>
                <TableCell className="space-x-1 rtl:space-x-reverse px-4 py-3">
                  <Button variant="ghost" size="icon" onClick={() => navigate(`/track-project/${project.id}`)} className="text-blue-500 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 h-8 w-8">
                    <Eye className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => navigate(`/edit-project/${project.id}`)}
                    className="text-yellow-500 hover:text-yellow-700 dark:text-yellow-400 dark:hover:text-yellow-300 h-8 w-8"
                  >
                    <Edit2 className="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="icon" onClick={() => confirmDeleteProject(project)} className="text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 h-8 w-8">
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </TableCell>
              </motion.tr>
            ))}
          </TableBody>
        </Table>
      </div>
    </motion.div>
  );
};

export default ProjectTable;