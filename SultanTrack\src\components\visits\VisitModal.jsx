import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { X, Save, Calendar, Package } from 'lucide-react';

const VisitModal = ({
  isOpen,
  onClose,
  onSave,
  visit = null,
  project = null,
  isEditing = false
}) => {
  const { t } = useTranslation();
  const [formData, setFormData] = useState({
    visit_date: '',
    project_status: '',
    material_shortages: {
      quantity_pvc: 0,
      quantity_shutter: 0,
      quantity_sgs: 0,
      quantity_doors: 0,
      quantity_glass: 0
    },
    materials_installed: {
      quantity_pvc: 0,
      quantity_shutter: 0,
      quantity_sgs: 0,
      quantity_doors: 0,
      quantity_glass: 0
    },
    notes: ''
  });
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (visit && isEditing) {
      setFormData({
        visit_date: visit.visit_date || '',
        project_status: visit.project_status || '',
        material_shortages: visit.material_shortages || {
          quantity_pvc: 0,
          quantity_shutter: 0,
          quantity_sgs: 0,
          quantity_doors: 0,
          quantity_glass: 0
        },
        materials_installed: visit.materials_installed || {
          quantity_pvc: 0,
          quantity_shutter: 0,
          quantity_sgs: 0,
          quantity_doors: 0,
          quantity_glass: 0
        },
        notes: visit.notes || ''
      });
    } else {
      // إعداد البيانات الافتراضية للزيارة الجديدة
      setFormData({
        visit_date: new Date().toISOString().split('T')[0],
        project_status: project?.status || '',
        material_shortages: {
          quantity_pvc: 0,
          quantity_shutter: 0,
          quantity_sgs: 0,
          quantity_doors: 0,
          quantity_glass: 0
        },
        materials_installed: {
          quantity_pvc: 0,
          quantity_shutter: 0,
          quantity_sgs: 0,
          quantity_doors: 0,
          quantity_glass: 0
        },
        notes: ''
      });
    }
  }, [visit, isEditing, project]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (name, value) => {
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleMaterialChange = (material, value, type = 'shortages') => {
    const fieldName = type === 'installed' ? 'materials_installed' : 'material_shortages';
    setFormData(prev => ({
      ...prev,
      [fieldName]: {
        ...prev[fieldName],
        [material]: parseInt(value) || 0
      }
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      const visitData = {
        ...formData,
        id: visit?.id || null,
        project_id: project?.id
      };

      await onSave(visitData);
      onClose();
    } catch (error) {
      console.error('Error saving visit:', error);
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto"
      >
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-800 dark:text-white flex items-center">
            <Calendar className="h-5 w-5 mr-2" />
            {isEditing ?
              (t('visits.editVisit') || 'Edit Visit') :
              (t('visits.addNewVisit') || 'Add New Visit')
            }
          </h2>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <X className="h-5 w-5" />
          </Button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* معلومات الزيارة الأساسية */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">{t('visits.visitInfo') || 'Visit Information'}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="visit_date">{t('visits.visitDate') || 'Visit Date'}</Label>
                  <Input
                    id="visit_date"
                    name="visit_date"
                    type="date"
                    value={formData.visit_date}
                    onChange={handleChange}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="project_status">{t('visits.projectStatus') || 'Project Status'}</Label>
                  <Select
                    value={formData.project_status}
                    onValueChange={(value) => handleSelectChange('project_status', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder={t('visits.selectStatus') || 'Select Project Status'} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Ongoing">{t('visits.ongoing') || 'Ongoing'}</SelectItem>
                      <SelectItem value="Partial Shortage">{t('visits.partialShortage') || 'Partial Shortage'}</SelectItem>
                      <SelectItem value="Stopped Due to Shortage">{t('visits.stoppedShortage') || 'Stopped Due to Shortage'}</SelectItem>
                      <SelectItem value="Completed">{t('visits.completed') || 'Completed'}</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* النواقص من الخامات */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center">
                <Package className="h-5 w-5 mr-2" />
                {t('visits.materialShortages') || 'Material Shortages'}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
                {[
                  { key: 'quantity_pvc', label: t('visits.pvcUnits') || 'PVC (units)' },
                  { key: 'quantity_shutter', label: t('visits.shutterUnits') || 'Shutter (units)' },
                  { key: 'quantity_sgs', label: t('visits.sgsUnits') || 'SGS (units)' },
                  { key: 'quantity_doors', label: t('visits.doorsUnits') || 'Doors (units)' },
                  { key: 'quantity_glass', label: t('visits.glassUnits') || 'Glass (units)' }
                ].map(material => (
                  <div key={material.key} className="space-y-2">
                    <Label htmlFor={`shortage_${material.key}`}>{material.label}</Label>
                    <Input
                      id={`shortage_${material.key}`}
                      type="number"
                      min="0"
                      step="1"
                      value={formData.material_shortages[material.key]}
                      onChange={(e) => handleMaterialChange(material.key, e.target.value, 'shortages')}
                    />
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* الخامات المركبة في هذا اليوم */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center">
                <Package className="h-5 w-5 mr-2" />
                {t('visits.materialsInstalled') || 'Materials Installed Today'}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
                {[
                  { key: 'quantity_pvc', label: t('visits.pvcUnits') || 'PVC (units)' },
                  { key: 'quantity_shutter', label: t('visits.shutterUnits') || 'Shutter (units)' },
                  { key: 'quantity_sgs', label: t('visits.sgsUnits') || 'SGS (units)' },
                  { key: 'quantity_doors', label: t('visits.doorsUnits') || 'Doors (units)' },
                  { key: 'quantity_glass', label: t('visits.glassUnits') || 'Glass (units)' }
                ].map(material => (
                  <div key={material.key} className="space-y-2">
                    <Label htmlFor={`installed_${material.key}`}>{material.label}</Label>
                    <Input
                      id={`installed_${material.key}`}
                      type="number"
                      min="0"
                      step="1"
                      value={formData.materials_installed[material.key]}
                      onChange={(e) => handleMaterialChange(material.key, e.target.value, 'installed')}
                    />
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* ملاحظات */}
          <div className="space-y-2">
            <Label htmlFor="notes">{t('visits.visitNotes') || 'Visit Notes'}</Label>
            <Textarea
              id="notes"
              name="notes"
              value={formData.notes}
              onChange={handleChange}
              rows={4}
              placeholder={t('visits.notesPlaceholder') || 'Enter notes about the visit...'}
            />
          </div>

          {/* أزرار الحفظ والإلغاء */}
          <div className="flex justify-end space-x-2 pt-4 border-t border-gray-200 dark:border-gray-700">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={loading}
            >
              {t('common.cancel') || 'Cancel'}
            </Button>
            <Button
              type="submit"
              disabled={loading}
              className="bg-blue-500 hover:bg-blue-600 text-white"
            >
              {loading ? (
                <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white"></div>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  {isEditing ?
                    (t('visits.updateVisit') || 'Update Visit') :
                    (t('visits.saveVisit') || 'Save Visit')
                  }
                </>
              )}
            </Button>
          </div>
        </form>
      </motion.div>
    </div>
  );
};

export default VisitModal;
