import React from 'react';
import { motion } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Edit, Calendar, AlertTriangle, CheckCircle, Clock, Activity } from 'lucide-react';

const VisitsList = ({ visits = [], onEditVisit, loading = false }) => {
  const { t } = useTranslation();

  // دالة لترجمة حالة المشروع
  const getStatusTranslation = (status) => {
    switch (status) {
      case 'Ongoing':
        return t('visits.ongoing') || 'Ongoing';
      case 'Partial Shortage':
        return t('visits.partialShortage') || 'Partial Shortage';
      case 'Stopped Due to Shortage':
        return t('visits.stoppedShortage') || 'Stopped Due to Shortage';
      case 'Completed':
        return t('visits.completed') || 'Completed';
      // الحالات القديمة للتوافق
      case 'Not Started':
      case 'pending':
        return t('visits.ongoing') || 'Ongoing';
      case 'In Progress':
      case 'in_progress':
        return t('visits.ongoing') || 'Ongoing';
      case 'Delayed':
      case 'delayed':
        return t('visits.partialShortage') || 'Partial Shortage';
      default:
        return status;
    }
  };

  // دالة لتحديد لون الحالة
  const getStatusColor = (status) => {
    switch (status) {
      case 'Ongoing':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'Partial Shortage':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 'Stopped Due to Shortage':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      case 'Completed':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      // الحالات القديمة للتوافق
      case 'Not Started':
      case 'pending':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
      case 'In Progress':
      case 'in_progress':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'Delayed':
      case 'delayed':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
    }
  };

  // دالة لتحديد أيقونة الحالة
  const getStatusIcon = (status) => {
    switch (status) {
      case 'Ongoing':
        return <Activity className="h-4 w-4" />;
      case 'Partial Shortage':
        return <AlertTriangle className="h-4 w-4" />;
      case 'Stopped Due to Shortage':
        return <AlertTriangle className="h-4 w-4" />;
      case 'Completed':
        return <CheckCircle className="h-4 w-4" />;
      // الحالات القديمة للتوافق
      case 'Not Started':
      case 'pending':
        return <Clock className="h-4 w-4" />;
      case 'In Progress':
      case 'in_progress':
        return <Activity className="h-4 w-4" />;
      case 'Delayed':
      case 'delayed':
        return <AlertTriangle className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  // دالة لتنسيق التاريخ
  const formatDate = (dateString) => {
    if (!dateString) return t('common.notSpecified') || 'Not Specified';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // دالة لحساب إجمالي النواقص
  const getTotalShortages = (shortages) => {
    if (!shortages) return 0;
    return Object.values(shortages).reduce((total, value) => total + (parseFloat(value) || 0), 0);
  };

  // دالة لحساب إجمالي الخامات المركبة
  const getTotalInstalled = (installed) => {
    if (!installed) return 0;
    return Object.values(installed).reduce((total, value) => total + (parseFloat(value) || 0), 0);
  };

  // دالة لعرض ملخص الخامات المركبة
  const getMaterialsSummary = (materials) => {
    if (!materials) return '';
    const items = [];
    if (materials.quantity_pvc > 0) items.push(`PVC: ${materials.quantity_pvc}`);
    if (materials.quantity_shutter > 0) items.push(`Shutter: ${materials.quantity_shutter}`);
    if (materials.quantity_sgs > 0) items.push(`SGS: ${materials.quantity_sgs}`);
    if (materials.quantity_doors > 0) items.push(`Doors: ${materials.quantity_doors}`);
    if (materials.quantity_glass > 0) items.push(`Glass: ${materials.quantity_glass}`);
    return items.join(' | ');
  };

  // ترتيب الزيارات حسب التاريخ (الأحدث أولاً)
  const sortedVisits = [...visits].sort((a, b) => new Date(b.visit_date) - new Date(a.visit_date));

  if (loading) {
    return (
      <div className="space-y-4">
        {[1, 2, 3].map(i => (
          <div key={i} className="animate-pulse">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-2 flex-1">
                    <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4"></div>
                    <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
                  </div>
                  <div className="h-8 w-8 bg-gray-200 dark:bg-gray-700 rounded"></div>
                </div>
              </CardContent>
            </Card>
          </div>
        ))}
      </div>
    );
  }

  if (visits.length === 0) {
    return (
      <div className="text-center py-8">
        <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <p className="text-gray-500 dark:text-gray-400">
          {t('visits.noVisitsYet') || 'No visits recorded yet'}
        </p>
        <p className="text-sm text-gray-400 dark:text-gray-500 mt-2">
          {t('visits.clickAddVisit') || 'Click "Add Visit" to record the first visit'}
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      {sortedVisits.map((visit, index) => (
        <motion.div
          key={visit.id || index}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: index * 0.1 }}
        >
          <Card className="hover:shadow-md transition-shadow duration-200">
            <CardContent className="p-4">
              <div className="flex items-center justify-between space-x-4 rtl:space-x-reverse">
                {/* تاريخ الزيارة */}
                <div className="flex items-center space-x-2 rtl:space-x-reverse min-w-0 flex-shrink-0">
                  <Calendar className="h-4 w-4 text-blue-500" />
                  <span className="font-medium text-gray-800 dark:text-white text-sm">
                    {formatDate(visit.visit_date)}
                  </span>
                </div>

                {/* حالة المشروع */}
                <div className="flex-shrink-0">
                  <Badge className={`${getStatusColor(visit.project_status)} flex items-center space-x-1 rtl:space-x-reverse text-xs`}>
                    {getStatusIcon(visit.project_status)}
                    <span>{getStatusTranslation(visit.project_status)}</span>
                  </Badge>
                </div>

                {/* ملخص الخامات المركبة */}
                <div className="flex-1 min-w-0">
                  {getTotalInstalled(visit.materials_installed) > 0 ? (
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                      <span className="font-medium text-green-600 dark:text-green-400">
                        {t('visits.installed') || 'Installed'}:
                      </span>
                      <span className="ml-2 truncate">
                        {getMaterialsSummary(visit.materials_installed)}
                      </span>
                    </div>
                  ) : (
                    <div className="text-sm text-gray-400 dark:text-gray-500">
                      {t('visits.noMaterialsInstalled') || 'No materials installed'}
                    </div>
                  )}
                </div>

                {/* النواقص (إذا وجدت) */}
                {getTotalShortages(visit.material_shortages) > 0 && (
                  <div className="flex items-center space-x-1 rtl:space-x-reverse text-xs text-orange-600 dark:text-orange-400 flex-shrink-0">
                    <AlertTriangle className="h-3 w-3" />
                    <span>
                      {getTotalShortages(visit.material_shortages)} {t('visits.shortages') || 'shortages'}
                    </span>
                  </div>
                )}

                {/* زر التعديل */}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onEditVisit(visit)}
                  className="text-blue-500 hover:text-blue-700 hover:bg-blue-50 dark:hover:bg-blue-900/20 flex-shrink-0"
                >
                  <Edit className="h-4 w-4" />
                </Button>
              </div>

              {/* ملاحظات (في سطر منفصل إذا وجدت) */}
              {visit.notes && (
                <div className="mt-2 pt-2 border-t border-gray-100 dark:border-gray-700">
                  <p className="text-xs text-gray-500 dark:text-gray-400 line-clamp-1">
                    <span className="font-medium">{t('visits.notes') || 'Notes'}:</span> {visit.notes}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </motion.div>
      ))}

      {/* إجمالي عدد الزيارات */}
      <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-center space-x-2 rtl:space-x-reverse text-sm text-gray-600 dark:text-gray-400">
          <Calendar className="h-4 w-4" />
          <span>
            {t('visits.totalVisits') || 'Total Visits'}: {visits.length} {t('visits.visit') || 'visit'}
            {visits.length !== 1 && 's'}
          </span>
        </div>
      </div>
    </div>
  );
};

export default VisitsList;
