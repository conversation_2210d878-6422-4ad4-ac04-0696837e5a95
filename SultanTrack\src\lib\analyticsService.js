import { supabase } from './supabase';

// ==================== إحصائيات طلبات الصيانة ====================

// الحصول على إحصائيات طلبات الصيانة
export const getMaintenanceAnalytics = async (timeFilter = 'month') => {
  try {
    // تحديد نطاق التاريخ بناءً على الفلتر
    const now = new Date();
    let startDate;

    switch (timeFilter) {
      case 'week':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case 'month':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      case 'year':
        startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
        break;
      default:
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    }

    // جلب جميع طلبات الصيانة
    const { data: allRequests, error: allError } = await supabase
      .from('maintenance_requests')
      .select('*');

    if (allError) throw allError;

    // جلب طلبات الصيانة في الفترة المحددة
    const { data: periodRequests, error: periodError } = await supabase
      .from('maintenance_requests')
      .select('*')
      .gte('created_at', startDate.toISOString());

    if (periodError) throw periodError;

    // حساب الإحصائيات العامة
    const totalRequests = allRequests?.length || 0;
    const completedRequests = allRequests?.filter(req =>
      req.status === 'completed' || req.status === 'Completed'
    ).length || 0;
    const pendingRequests = allRequests?.filter(req =>
      req.status === 'pending' || req.status === 'New' || req.status === 'in_progress'
    ).length || 0;

    // حساب الإحصائيات للفترة المحددة
    const periodTotal = periodRequests?.length || 0;
    const periodCompleted = periodRequests?.filter(req =>
      req.status === 'completed' || req.status === 'Completed'
    ).length || 0;
    const periodPending = periodRequests?.filter(req =>
      req.status === 'pending' || req.status === 'New' || req.status === 'in_progress'
    ).length || 0;

    return {
      total: totalRequests,
      completed: completedRequests,
      pending: pendingRequests,
      period: {
        total: periodTotal,
        completed: periodCompleted,
        pending: periodPending,
        timeFilter
      }
    };
  } catch (error) {
    console.error('Error getting maintenance analytics:', error);
    throw error;
  }
};

// ==================== إحصائيات المشاريع ====================

// الحصول على إحصائيات المشاريع
export const getProjectAnalytics = async (timeFilter = 'month') => {
  try {
    // تحديد نطاق التاريخ بناءً على الفلتر
    const now = new Date();
    let startDate;

    switch (timeFilter) {
      case 'week':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case 'month':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      case 'year':
        startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
        break;
      default:
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    }

    // جلب جميع المشاريع
    const { data: allProjects, error: allError } = await supabase
      .from('projects')
      .select('*');

    if (allError) throw allError;

    // جلب المشاريع في الفترة المحددة
    const { data: periodProjects, error: periodError } = await supabase
      .from('projects')
      .select('*')
      .gte('created_at', startDate.toISOString());

    if (periodError) throw periodError;

    // حساب الإحصائيات العامة
    const totalProjects = allProjects?.length || 0;
    const completedProjects = allProjects?.filter(project =>
      project.status === 'completed' || project.status === 'Completed'
    ).length || 0;
    const inProgressProjects = allProjects?.filter(project =>
      project.status === 'in_progress' || project.status === 'In Progress'
    ).length || 0;
    const pendingProjects = allProjects?.filter(project =>
      project.status === 'pending' || project.status === 'Not Started'
    ).length || 0;

    // حساب الإحصائيات للفترة المحددة
    const periodTotal = periodProjects?.length || 0;
    const periodCompleted = periodProjects?.filter(project =>
      project.status === 'completed' || project.status === 'Completed'
    ).length || 0;
    const periodInProgress = periodProjects?.filter(project =>
      project.status === 'in_progress' || project.status === 'In Progress'
    ).length || 0;

    return {
      total: totalProjects,
      completed: completedProjects,
      inProgress: inProgressProjects,
      pending: pendingProjects,
      period: {
        total: periodTotal,
        completed: periodCompleted,
        inProgress: periodInProgress,
        timeFilter
      }
    };
  } catch (error) {
    console.error('Error getting project analytics:', error);
    throw error;
  }
};

// ==================== إحصائيات نقص المواد ====================

// الحصول على إحصائيات نقص المواد من الزيارات
export const getMaterialShortageAnalytics = async () => {
  try {
    // جلب جميع الزيارات
    const { data: visits, error: visitsError } = await supabase
      .from('project_visits')
      .select(`
        *,
        projects (
          id,
          project_number,
          client_name,
          status
        )
      `)
      .order('visit_date', { ascending: false });

    if (visitsError) throw visitsError;

    // تجميع المشاريع حسب آخر زيارة لكل مشروع
    const projectsLatestVisit = {};
    visits?.forEach(visit => {
      if (!projectsLatestVisit[visit.project_id] ||
          new Date(visit.visit_date) > new Date(projectsLatestVisit[visit.project_id].visit_date)) {
        projectsLatestVisit[visit.project_id] = visit;
      }
    });

    // حساب المشاريع المتوقفة على المواد
    const stoppedDueToShortage = Object.values(projectsLatestVisit).filter(visit =>
      visit.project_status === 'متوقف على نواقص' ||
      visit.project_status === 'Stopped Due to Shortage'
    ).length;

    // حساب المشاريع بنقص جزئي
    const partialShortage = Object.values(projectsLatestVisit).filter(visit =>
      visit.project_status === 'نواقص جزئي' ||
      visit.project_status === 'Partial Shortage'
    ).length;

    // حساب المشاريع المستمرة
    const ongoingProjects = Object.values(projectsLatestVisit).filter(visit =>
      visit.project_status === 'مستمر' ||
      visit.project_status === 'Ongoing'
    ).length;

    return {
      stoppedDueToShortage,
      partialShortage,
      ongoingProjects,
      totalProjectsWithVisits: Object.keys(projectsLatestVisit).length
    };
  } catch (error) {
    console.error('Error getting material shortage analytics:', error);
    throw error;
  }
};

// ==================== دالة شاملة للحصول على جميع الإحصائيات ====================

export const getDashboardAnalytics = async (timeFilter = 'month', language = 'ar') => {
  try {
    const [maintenanceStats, projectStats, materialStats] = await Promise.all([
      getMaintenanceAnalytics(timeFilter),
      getProjectAnalytics(timeFilter),
      getMaterialShortageAnalytics()
    ]);

    return {
      maintenance: maintenanceStats,
      projects: projectStats,
      materials: materialStats,
      timeFilter,
      language
    };
  } catch (error) {
    console.error('Error getting dashboard analytics:', error);
    throw error;
  }
};

// ==================== إحصائيات تفصيلية للفترات الزمنية ====================

// الحصول على إحصائيات شهرية للرسوم البيانية
export const getMonthlyTrends = async (language = 'ar') => {
  try {
    const now = new Date();
    const sixMonthsAgo = new Date(now.getTime() - 6 * 30 * 24 * 60 * 60 * 1000);

    // جلب المشاريع المكتملة في آخر 6 شهور
    const { data: completedProjects, error: projectsError } = await supabase
      .from('projects')
      .select('created_at, status')
      .gte('created_at', sixMonthsAgo.toISOString())
      .in('status', ['completed', 'Completed']);

    if (projectsError) throw projectsError;

    // جلب طلبات الصيانة المكتملة في آخر 6 شهور
    const { data: completedMaintenance, error: maintenanceError } = await supabase
      .from('maintenance_requests')
      .select('created_at, status')
      .gte('created_at', sixMonthsAgo.toISOString())
      .in('status', ['completed', 'Completed']);

    if (maintenanceError) throw maintenanceError;

    // تجميع البيانات حسب الشهر
    const monthlyData = [];
    for (let i = 5; i >= 0; i--) {
      const monthStart = new Date(now.getFullYear(), now.getMonth() - i, 1);
      const monthEnd = new Date(now.getFullYear(), now.getMonth() - i + 1, 0);

      const projectsCount = completedProjects?.filter(project => {
        const date = new Date(project.created_at);
        return date >= monthStart && date <= monthEnd;
      }).length || 0;

      const maintenanceCount = completedMaintenance?.filter(request => {
        const date = new Date(request.created_at);
        return date >= monthStart && date <= monthEnd;
      }).length || 0;

      // تنسيق أسماء الشهور بشكل صحيح حسب اللغة
      const monthNamesAr = [
        'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
        'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
      ];

      const monthNamesEn = [
        'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
        'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
      ];

      const monthNames = language === 'ar' ? monthNamesAr : monthNamesEn;
      const monthName = monthNames[monthStart.getMonth()];
      const year = monthStart.getFullYear();

      monthlyData.push({
        month: `${monthName} ${year}`,
        projects: projectsCount,
        maintenance: maintenanceCount
      });
    }

    return monthlyData;
  } catch (error) {
    console.error('Error getting monthly trends:', error);
    throw error;
  }
};
