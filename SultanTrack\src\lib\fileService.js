import { supabase } from './supabase';

// إنشاء جدول الملفات إذا لم يكن موجوداً
export const createFilesTable = async () => {
  try {
    const { error } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS project_files (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
          file_name VARCHAR(255) NOT NULL,
          file_path VARCHAR(500) NOT NULL,
          file_size BIGINT NOT NULL,
          file_type VARCHAR(100) NOT NULL,
          bucket_name VARCHAR(100) NOT NULL,
          file_url TEXT,
          uploaded_by UUID REFERENCES auth.users(id),
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        -- إنشاء فهرس للبحث السريع
        CREATE INDEX IF NOT EXISTS idx_project_files_project_id ON project_files(project_id);
        CREATE INDEX IF NOT EXISTS idx_project_files_created_at ON project_files(created_at);

        -- إعداد RLS
        ALTER TABLE project_files ENABLE ROW LEVEL SECURITY;

        -- سياسة للقراءة
        DROP POLICY IF EXISTS "Enable read access for all users" ON project_files;
        CREATE POLICY "Enable read access for all users" ON project_files
          FOR SELECT USING (true);

        -- سياسة للإدراج
        DROP POLICY IF EXISTS "Enable insert for authenticated users only" ON project_files;
        CREATE POLICY "Enable insert for authenticated users only" ON project_files
          FOR INSERT WITH CHECK (auth.role() = 'authenticated');

        -- سياسة للتحديث
        DROP POLICY IF EXISTS "Enable update for authenticated users only" ON project_files;
        CREATE POLICY "Enable update for authenticated users only" ON project_files
          FOR UPDATE USING (auth.role() = 'authenticated');

        -- سياسة للحذف
        DROP POLICY IF EXISTS "Enable delete for authenticated users only" ON project_files;
        CREATE POLICY "Enable delete for authenticated users only" ON project_files
          FOR DELETE USING (auth.role() = 'authenticated');
      `
    });

    if (error) throw error;
    return { success: true };
  } catch (error) {
    console.error('Error creating files table:', error);
    return { success: false, error: error.message };
  }
};

// حفظ معلومات الملف في قاعدة البيانات
export const saveFileRecord = async (fileData) => {
  try {
    const { data, error } = await supabase
      .from('project_files')
      .insert({
        project_id: fileData.projectId,
        file_name: fileData.name,
        file_path: fileData.path,
        file_size: fileData.size,
        file_type: fileData.type,
        bucket_name: fileData.bucket,
        file_url: fileData.url,
        uploaded_by: fileData.uploadedBy || null
      })
      .select()
      .single();

    if (error) throw error;
    return { success: true, data };
  } catch (error) {
    console.error('Error saving file record:', error);
    return { success: false, error: error.message };
  }
};

// جلب ملفات مشروع معين
export const getProjectFiles = async (projectId) => {
  try {
    const { data, error } = await supabase
      .from('project_files')
      .select('*')
      .eq('project_id', projectId)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return { success: true, data: data || [] };
  } catch (error) {
    console.error('Error fetching project files:', error);
    return { success: false, error: error.message, data: [] };
  }
};

// حذف ملف من قاعدة البيانات والتخزين
export const deleteFile = async (fileId) => {
  try {
    // جلب معلومات الملف أولاً
    const { data: fileData, error: fetchError } = await supabase
      .from('project_files')
      .select('*')
      .eq('id', fileId)
      .single();

    if (fetchError) throw fetchError;

    // حذف الملف من التخزين
    if (fileData.bucket_name && fileData.file_path) {
      const { error: storageError } = await supabase.storage
        .from(fileData.bucket_name)
        .remove([fileData.file_path]);

      if (storageError) {
        console.warn('Warning: Could not delete file from storage:', storageError);
        // لا نوقف العملية إذا فشل حذف الملف من التخزين
      }
    }

    // حذف السجل من قاعدة البيانات
    const { error: deleteError } = await supabase
      .from('project_files')
      .delete()
      .eq('id', fileId);

    if (deleteError) throw deleteError;

    return { success: true };
  } catch (error) {
    console.error('Error deleting file:', error);
    return { success: false, error: error.message };
  }
};

// تحديث معلومات الملف
export const updateFileRecord = async (fileId, updates) => {
  try {
    const { data, error } = await supabase
      .from('project_files')
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('id', fileId)
      .select()
      .single();

    if (error) throw error;
    return { success: true, data };
  } catch (error) {
    console.error('Error updating file record:', error);
    return { success: false, error: error.message };
  }
};

// رفع ملف إلى Supabase Storage
export const uploadFileToStorage = async (file, projectId, userId = null) => {
  try {
    const fileExt = file.name.split('.').pop().toLowerCase();
    const timestamp = Date.now();
    const sanitizedName = file.name.replace(/[^a-zA-Z0-9.-]/g, '_');
    const fileName = `${projectId}/${timestamp}_${sanitizedName}`;
    
    // تحديد البucket حسب نوع الملف
    const bucket = file.type.startsWith('image/') ? 'project-images' : 'project-documents';

    // رفع الملف
    const { data: uploadData, error: uploadError } = await supabase.storage
      .from(bucket)
      .upload(fileName, file, {
        cacheControl: '3600',
        upsert: false
      });

    if (uploadError) throw uploadError;

    // الحصول على URL عام للملف
    const { data: urlData } = supabase.storage
      .from(bucket)
      .getPublicUrl(fileName);

    // حفظ معلومات الملف في قاعدة البيانات
    const fileRecord = await saveFileRecord({
      projectId: projectId,
      name: file.name,
      path: uploadData.path,
      size: file.size,
      type: file.type,
      bucket: bucket,
      url: urlData.publicUrl,
      uploadedBy: userId
    });

    if (!fileRecord.success) {
      // إذا فشل حفظ السجل، احذف الملف من التخزين
      await supabase.storage.from(bucket).remove([fileName]);
      throw new Error(fileRecord.error);
    }

    return {
      success: true,
      data: {
        id: fileRecord.data.id,
        path: uploadData.path,
        bucket: bucket,
        name: file.name,
        size: file.size,
        type: file.type,
        url: urlData.publicUrl,
        uploaded: true,
        uploadedAt: fileRecord.data.created_at
      }
    };
  } catch (error) {
    console.error('Error uploading file:', error);
    return { success: false, error: error.message };
  }
};

// جلب URL للتحميل المباشر
export const getDownloadUrl = async (bucket, path) => {
  try {
    const { data, error } = await supabase.storage
      .from(bucket)
      .createSignedUrl(path, 3600); // صالح لمدة ساعة

    if (error) throw error;
    return { success: true, url: data.signedUrl };
  } catch (error) {
    console.error('Error getting download URL:', error);
    return { success: false, error: error.message };
  }
};

// تنظيف الملفات المؤقتة (الملفات التي لم يتم ربطها بمشروع)
export const cleanupOrphanedFiles = async () => {
  try {
    // جلب الملفات التي لا تنتمي لمشاريع موجودة
    const { data: orphanedFiles, error } = await supabase
      .from('project_files')
      .select('*')
      .not('project_id', 'in', 
        supabase.from('projects').select('id')
      );

    if (error) throw error;

    // حذف الملفات المؤقتة
    for (const file of orphanedFiles || []) {
      await deleteFile(file.id);
    }

    return { 
      success: true, 
      message: `Cleaned up ${orphanedFiles?.length || 0} orphaned files` 
    };
  } catch (error) {
    console.error('Error cleaning up orphaned files:', error);
    return { success: false, error: error.message };
  }
};

// إحصائيات الملفات
export const getFileStats = async () => {
  try {
    const { data, error } = await supabase
      .from('project_files')
      .select('file_size, file_type, bucket_name');

    if (error) throw error;

    const stats = {
      totalFiles: data.length,
      totalSize: data.reduce((sum, file) => sum + (file.file_size || 0), 0),
      byType: {},
      byBucket: {}
    };

    data.forEach(file => {
      // إحصائيات حسب النوع
      const type = file.file_type.split('/')[0]; // image, application, etc.
      stats.byType[type] = (stats.byType[type] || 0) + 1;

      // إحصائيات حسب البucket
      stats.byBucket[file.bucket_name] = (stats.byBucket[file.bucket_name] || 0) + 1;
    });

    return { success: true, data: stats };
  } catch (error) {
    console.error('Error getting file stats:', error);
    return { success: false, error: error.message };
  }
};

export default {
  createFilesTable,
  saveFileRecord,
  getProjectFiles,
  deleteFile,
  updateFileRecord,
  uploadFileToStorage,
  getDownloadUrl,
  cleanupOrphanedFiles,
  getFileStats
};
