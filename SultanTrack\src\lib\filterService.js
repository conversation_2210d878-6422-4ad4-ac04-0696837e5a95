import { getAllProjects, getAllMaintenanceRequests } from './supabaseDatabase';

// دالة لفلترة البيانات حسب الفلاتر المحددة
export const applyFilters = async (filters) => {
  try {
    console.log('Applying filters:', filters);

    // جلب جميع البيانات
    const [projects, maintenanceRequests] = await Promise.all([
      getAllProjects(),
      getAllMaintenanceRequests()
    ]);

    // فلترة المشاريع
    let filteredProjects = filterProjects(projects, filters);

    // فلترة طلبات الصيانة
    let filteredMaintenance = filterMaintenance(maintenanceRequests, filters);

    // حساب الإحصائيات المفلترة
    const analytics = calculateFilteredAnalytics(filteredProjects, filteredMaintenance, filters);

    return {
      projects: filteredProjects,
      maintenance: filteredMaintenance,
      analytics: analytics
    };
  } catch (error) {
    console.error('Error applying filters:', error);
    throw error;
  }
};

// فلترة المشاريع
const filterProjects = (projects, filters) => {
  let filtered = [...projects];

  // فلتر الفترة الزمنية
  if (filters.timeRange && filters.timeRange !== 'all') {
    filtered = filterByTimeRange(filtered, filters.timeRange, filters.startDate, filters.endDate);
  }

  // فلتر المدينة
  if (filters.city && filters.city.trim() !== '' && filters.city !== 'all') {
    filtered = filtered.filter(project =>
      project.project_location &&
      project.project_location.toLowerCase().includes(filters.city.toLowerCase())
    );
  }

  // فلتر حالة المشروع
  if (filters.projectStatus && filters.projectStatus !== '' && filters.projectStatus !== 'all') {
    filtered = filtered.filter(project => {
      const status = getProjectStatus(project);
      return status === filters.projectStatus;
    });
  }

  // فلتر نوع المادة (إظهار المشاريع التي تحتوي على هذه المادة)
  if (filters.materialType && filters.materialType !== '' && filters.materialType !== 'all') {
    filtered = filtered.filter(project => {
      const materials = project.materials || {};
      const materialKey = `quantity_${filters.materialType}`;
      return materials[materialKey] > 0 || project[materialKey] > 0;
    });
  }

  return filtered;
};

// فلترة طلبات الصيانة
const filterMaintenance = (maintenanceRequests, filters) => {
  let filtered = [...maintenanceRequests];

  // فلتر الفترة الزمنية
  if (filters.timeRange && filters.timeRange !== 'all') {
    filtered = filterMaintenanceByTimeRange(filtered, filters.timeRange, filters.startDate, filters.endDate);
  }

  // فلتر المدينة
  if (filters.city && filters.city.trim() !== '' && filters.city !== 'all') {
    filtered = filtered.filter(request =>
      request.city &&
      request.city.toLowerCase().includes(filters.city.toLowerCase())
    );
  }

  return filtered;
};

// فلترة حسب الفترة الزمنية للمشاريع
const filterByTimeRange = (projects, timeRange, startDate, endDate) => {
  const now = new Date();
  let filterDate;

  switch (timeRange) {
    case 'week':
      filterDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      break;
    case 'month':
      filterDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      break;
    case 'quarter':
      filterDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
      break;
    case 'year':
      filterDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
      break;
    case 'custom':
      if (startDate && endDate) {
        return projects.filter(project => {
          const projectDate = new Date(project.start_date || project.created_at);
          return projectDate >= new Date(startDate) && projectDate <= new Date(endDate);
        });
      }
      return projects;
    default:
      return projects;
  }

  return projects.filter(project => {
    const projectDate = new Date(project.start_date || project.created_at);
    return projectDate >= filterDate;
  });
};

// فلترة حسب الفترة الزمنية لطلبات الصيانة
const filterMaintenanceByTimeRange = (requests, timeRange, startDate, endDate) => {
  const now = new Date();
  let filterDate;

  switch (timeRange) {
    case 'week':
      filterDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      break;
    case 'month':
      filterDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      break;
    case 'quarter':
      filterDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
      break;
    case 'year':
      filterDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
      break;
    case 'custom':
      if (startDate && endDate) {
        return requests.filter(request => {
          const requestDate = new Date(request.request_date || request.created_at);
          return requestDate >= new Date(startDate) && requestDate <= new Date(endDate);
        });
      }
      return requests;
    default:
      return requests;
  }

  return requests.filter(request => {
    const requestDate = new Date(request.request_date || request.created_at);
    return requestDate >= filterDate;
  });
};

// تحديد حالة المشروع
const getProjectStatus = (project) => {
  if (project.status) {
    return project.status;
  }

  // منطق تحديد الحالة بناءً على البيانات المتاحة
  if (project.completion_date) {
    return 'completed';
  } else if (project.start_date) {
    const startDate = new Date(project.start_date);
    const now = new Date();
    if (startDate <= now) {
      return 'in_progress';
    }
  }

  return 'pending';
};

// حساب الإحصائيات المفلترة
const calculateFilteredAnalytics = (projects, maintenanceRequests, filters) => {
  // إحصائيات المشاريع
  const projectStats = {
    total: projects.length,
    pending: projects.filter(p => getProjectStatus(p) === 'pending').length,
    inProgress: projects.filter(p => getProjectStatus(p) === 'in_progress').length,
    completed: projects.filter(p => getProjectStatus(p) === 'completed').length,
    onHold: projects.filter(p => getProjectStatus(p) === 'on_hold').length
  };

  // إحصائيات الصيانة
  const maintenanceStats = {
    total: maintenanceRequests.length,
    pending: maintenanceRequests.filter(r => r.status === 'new' || r.status === 'pending').length,
    inProgress: maintenanceRequests.filter(r => r.status === 'inprogress').length,
    completed: maintenanceRequests.filter(r => r.status === 'closed').length
  };

  // إحصائيات المواد
  const materialStats = calculateMaterialStats(projects);

  // بيانات الكميات للرسم البياني
  const quantitiesData = calculateQuantitiesData(projects, filters.materialType);

  return {
    projects: projectStats,
    maintenance: maintenanceStats,
    materials: materialStats,
    quantities: quantitiesData
  };
};

// حساب إحصائيات المواد
const calculateMaterialStats = (projects) => {
  let stoppedDueToShortage = 0;
  let partialShortage = 0;
  let ongoingProjects = 0;

  projects.forEach(project => {
    const status = getProjectStatus(project);
    if (status === 'on_hold') {
      stoppedDueToShortage++;
    } else if (status === 'in_progress') {
      // يمكن إضافة منطق للتحقق من النقص الجزئي
      ongoingProjects++;
    }
  });

  return {
    stoppedDueToShortage,
    partialShortage,
    ongoingProjects
  };
};

// حساب بيانات الكميات
const calculateQuantitiesData = (projects, materialFilter) => {
  const materials = ['pvc', 'shutter', 'sgs', 'doors', 'glass'];

  // إذا كان هناك فلتر مادة محدد، اعرض هذه المادة فقط
  const materialsToShow = materialFilter && materialFilter !== 'all' ? [materialFilter] : materials;

  return materialsToShow.map(material => {
    const total = projects.reduce((sum, project) => {
      const materialValue = project.materials?.[`quantity_${material}`] || project[`quantity_${material}`] || 0;
      return sum + (typeof materialValue === 'number' ? materialValue : 0);
    }, 0);

    return {
      name: material,
      value: total,
      unit: 'units'
    };
  });
};

// ألوان محسنة للرسوم البيانية
export const getChartColors = () => {
  return {
    primary: '#3b82f6',      // أزرق
    secondary: '#10b981',    // أخضر
    accent: '#f59e0b',       // برتقالي
    danger: '#ef4444',       // أحمر
    warning: '#eab308',      // أصفر
    info: '#06b6d4',         // سماوي
    purple: '#8b5cf6',       // بنفسجي
    pink: '#ec4899',         // وردي
    gradient: [
      '#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#eab308',
      '#06b6d4', '#8b5cf6', '#ec4899'
    ]
  };
};

// تحسين ألوان الرسم البياني الدائري
export const getPieChartColors = (dataLength) => {
  const colors = getChartColors();
  return colors.gradient.slice(0, dataLength);
};
