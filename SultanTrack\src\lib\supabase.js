import { createClient } from '@supabase/supabase-js';

// إعدادات Supabase - يجب استبدالها بالقيم الفعلية من مشروع Supabase الخاص بك
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 'https://qpyaydajfoktjljtjltx.supabase.co';
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFweWF5ZGFqZm9rdGpsanRqbHR4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwODY1NzAsImV4cCI6MjA2MzY2MjU3MH0.ZtBZNAeJ1LM6cUnqsJg2AolfkDMIFk2GatVWoIQuWOQ';

// إنشاء عميل Supabase
export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// دالة للتحقق من الاتصال
export const testConnection = async () => {
  try {
    const { data, error } = await supabase.auth.getSession();
    if (error) {
      console.error('Supabase connection error:', error);
      return false;
    }
    console.log('Supabase connected successfully');
    return true;
  } catch (error) {
    console.error('Supabase connection failed:', error);
    return false;
  }
};

// دالة لإنشاء مستخدمين تجريبيين
export const createTestUsers = async () => {
  const testUsers = [
    {
      email: '<EMAIL>',
      password: 'password',
      role: 'manager',
      name: 'Manager User'
    },
    {
      email: '<EMAIL>',
      password: 'password',
      role: 'supervisor',
      name: 'Supervisor User'
    },
    {
      email: '<EMAIL>',
      password: 'password',
      role: 'data_entry',
      name: 'Data Entry User'
    },
    {
      email: '<EMAIL>',
      password: 'password',
      role: 'technician',
      name: 'Technician User'
    }
  ];

  for (const user of testUsers) {
    try {
      const { data, error } = await supabase.auth.signUp({
        email: user.email,
        password: user.password,
        options: {
          data: {
            role: user.role,
            name: user.name
          }
        }
      });

      if (error && !error.message.includes('already registered')) {
        console.error(`Error creating user ${user.email}:`, error);
      } else {
        console.log(`User ${user.email} created or already exists`);
      }
    } catch (error) {
      console.error(`Exception creating user ${user.email}:`, error);
    }
  }
};

// دالة لإنشاء قاعدة البيانات كاملة
export const createDatabase = async () => {
  try {
    console.log('Starting database setup...');

    // إنشاء جدول المشاريع
    const projectsTable = `
      CREATE TABLE IF NOT EXISTS projects (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        project_number VARCHAR(50) UNIQUE NOT NULL,
        client_name VARCHAR(255) NOT NULL,
        client_phone VARCHAR(20),
        client_email VARCHAR(255),
        project_type VARCHAR(100),
        project_location TEXT,
        project_description TEXT,
        start_date DATE,
        end_date DATE,
        status VARCHAR(50) DEFAULT 'pending',
        total_cost DECIMAL(10,2),
        paid_amount DECIMAL(10,2) DEFAULT 0,
        remaining_amount DECIMAL(10,2) DEFAULT 0,
        materials JSONB,
        notes TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `;

    // إنشاء جدول المستخدمين
    const usersTable = `
      CREATE TABLE IF NOT EXISTS users (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        email VARCHAR(255) UNIQUE NOT NULL,
        phone VARCHAR(20),
        role VARCHAR(50) DEFAULT 'employee',
        department VARCHAR(100),
        city VARCHAR(100),
        bio TEXT,
        avatar_url VARCHAR(500),
        is_active BOOLEAN DEFAULT true,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `;

    // إنشاء جدول طلبات الصيانة
    const maintenanceTable = `
      CREATE TABLE IF NOT EXISTS maintenance_requests (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
        client_name VARCHAR(255) NOT NULL,
        client_phone VARCHAR(20),
        client_email VARCHAR(255),
        issue_description TEXT NOT NULL,
        priority VARCHAR(20) DEFAULT 'medium',
        status VARCHAR(50) DEFAULT 'pending',
        assigned_to VARCHAR(255),
        scheduled_date DATE,
        completed_date DATE,
        cost DECIMAL(10,2),
        notes TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `;

    // إنشاء جدول زيارات المشاريع
    const visitsTable = `
      CREATE TABLE IF NOT EXISTS project_visits (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
        visit_date DATE NOT NULL,
        visit_time TIME,
        visitor_name VARCHAR(255) NOT NULL,
        visit_purpose VARCHAR(255),
        notes TEXT,
        photos JSONB,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `;

    // إنشاء جدول النسخ الاحتياطي
    const backupsTable = `
      CREATE TABLE IF NOT EXISTS backups (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        projects JSONB,
        visits JSONB,
        maintenance_requests JSONB,
        users JSONB,
        timestamp TIMESTAMP WITH TIME ZONE,
        version VARCHAR(10),
        size DECIMAL(10,2),
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `;

    // تجميع جميع الجداول
    const allTables = [
      { name: 'projects', sql: projectsTable },
      { name: 'users', sql: usersTable },
      { name: 'maintenance_requests', sql: maintenanceTable },
      { name: 'project_visits', sql: visitsTable },
      { name: 'backups', sql: backupsTable }
    ];

    const results = [];

    // فحص الجداول الموجودة بدلاً من محاولة إنشائها
    for (const table of allTables) {
      try {
        const { data, error } = await supabase.from(table.name).select('*').limit(1);
        if (error) {
          console.error(`Table ${table.name} does not exist:`, error);
          results.push({ table: table.name, success: false, error: `Table ${table.name} not found. Please create it manually in SQL Editor.` });
        } else {
          console.log(`${table.name} table exists`);
          results.push({ table: table.name, success: true });
        }
      } catch (err) {
        console.error(`Exception checking ${table.name} table:`, err);
        results.push({ table: table.name, success: false, error: err.message });
      }
    }

    // إنشاء الفهارس
    await createIndexes();

    // إنشاء الدوال والمشغلات
    await createFunctionsAndTriggers();

    // إعداد سياسات الأمان
    await setupRLS();

    const successCount = results.filter(r => r.success).length;
    const totalCount = results.length;

    if (successCount === totalCount) {
      return {
        success: true,
        message: `تم إنشاء جميع الجداول بنجاح (${successCount}/${totalCount})`,
        results
      };
    } else {
      return {
        success: false,
        error: `تم إنشاء ${successCount} من ${totalCount} جداول فقط. يرجى إنشاء الجداول يدوياً في SQL Editor.`,
        results
      };
    }
  } catch (error) {
    console.error('Error creating database:', error);
    return {
      success: false,
      error: 'فشل في إنشاء قاعدة البيانات. يرجى إنشاء الجداول يدوياً في Supabase SQL Editor.'
    };
  }
};

// دالة لإنشاء الجداول إذا لم تكن موجودة
export const initializeTables = async () => {
  try {
    // محاولة الوصول للجداول للتحقق من وجودها
    const tables = ['projects', 'users', 'maintenance_requests', 'project_visits', 'backups'];
    const results = [];

    for (const table of tables) {
      try {
        const { error } = await supabase.from(table).select('*').limit(1);
        if (error) {
          results.push({ table, exists: false, error: error.message });
        } else {
          results.push({ table, exists: true });
        }
      } catch (err) {
        results.push({ table, exists: false, error: err.message });
      }
    }

    console.log('Tables check results:', results);
    return { success: true, results };
  } catch (error) {
    console.error('Error checking tables:', error);
    return { success: false, error: error.message };
  }
};

// دالة لإنشاء الفهارس
export const createIndexes = async () => {
  const indexes = [
    'CREATE INDEX IF NOT EXISTS idx_projects_status ON projects(status);',
    'CREATE INDEX IF NOT EXISTS idx_projects_client_name ON projects(client_name);',
    'CREATE INDEX IF NOT EXISTS idx_maintenance_status ON maintenance_requests(status);',
    'CREATE INDEX IF NOT EXISTS idx_maintenance_project_id ON maintenance_requests(project_id);',
    'CREATE INDEX IF NOT EXISTS idx_visits_project_id ON project_visits(project_id);',
    'CREATE INDEX IF NOT EXISTS idx_visits_date ON project_visits(visit_date);',
    'CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);',
    'CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);'
  ];

  for (const index of indexes) {
    try {
      await supabase.rpc('exec_sql', { sql: index });
    } catch (error) {
      console.error('Error creating index:', error);
    }
  }
};

// دالة لإنشاء الدوال والمشغلات
export const createFunctionsAndTriggers = async () => {
  const updateFunction = `
    CREATE OR REPLACE FUNCTION update_updated_at_column()
    RETURNS TRIGGER AS $$
    BEGIN
        NEW.updated_at = NOW();
        RETURN NEW;
    END;
    $$ language 'plpgsql';
  `;

  const triggers = [
    'CREATE TRIGGER update_projects_updated_at BEFORE UPDATE ON projects FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();',
    'CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();',
    'CREATE TRIGGER update_maintenance_updated_at BEFORE UPDATE ON maintenance_requests FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();'
  ];

  try {
    await supabase.rpc('exec_sql', { sql: updateFunction });
    for (const trigger of triggers) {
      await supabase.rpc('exec_sql', { sql: trigger });
    }
  } catch (error) {
    console.error('Error creating functions and triggers:', error);
  }
};

// دالة لإعداد سياسات الأمان
export const setupRLS = async () => {
  const rlsCommands = [
    'ALTER TABLE projects ENABLE ROW LEVEL SECURITY;',
    'ALTER TABLE users ENABLE ROW LEVEL SECURITY;',
    'ALTER TABLE maintenance_requests ENABLE ROW LEVEL SECURITY;',
    'ALTER TABLE project_visits ENABLE ROW LEVEL SECURITY;',
    'ALTER TABLE backups ENABLE ROW LEVEL SECURITY;'
  ];

  const policies = [
    'CREATE POLICY "Enable read access for all users" ON projects FOR SELECT USING (true);',
    'CREATE POLICY "Enable insert access for all users" ON projects FOR INSERT WITH CHECK (true);',
    'CREATE POLICY "Enable update access for all users" ON projects FOR UPDATE USING (true);',
    'CREATE POLICY "Enable delete access for all users" ON projects FOR DELETE USING (true);',

    'CREATE POLICY "Enable read access for all users" ON users FOR SELECT USING (true);',
    'CREATE POLICY "Enable insert access for all users" ON users FOR INSERT WITH CHECK (true);',
    'CREATE POLICY "Enable update access for all users" ON users FOR UPDATE USING (true);',
    'CREATE POLICY "Enable delete access for all users" ON users FOR DELETE USING (true);',

    'CREATE POLICY "Enable read access for all users" ON maintenance_requests FOR SELECT USING (true);',
    'CREATE POLICY "Enable insert access for all users" ON maintenance_requests FOR INSERT WITH CHECK (true);',
    'CREATE POLICY "Enable update access for all users" ON maintenance_requests FOR UPDATE USING (true);',
    'CREATE POLICY "Enable delete access for all users" ON maintenance_requests FOR DELETE USING (true);',

    'CREATE POLICY "Enable read access for all users" ON project_visits FOR SELECT USING (true);',
    'CREATE POLICY "Enable insert access for all users" ON project_visits FOR INSERT WITH CHECK (true);',
    'CREATE POLICY "Enable update access for all users" ON project_visits FOR UPDATE USING (true);',
    'CREATE POLICY "Enable delete access for all users" ON project_visits FOR DELETE USING (true);',

    'CREATE POLICY "Enable read access for all users" ON backups FOR SELECT USING (true);',
    'CREATE POLICY "Enable insert access for all users" ON backups FOR INSERT WITH CHECK (true);',
    'CREATE POLICY "Enable update access for all users" ON backups FOR UPDATE USING (true);',
    'CREATE POLICY "Enable delete access for all users" ON backups FOR DELETE USING (true);'
  ];

  try {
    for (const command of rlsCommands) {
      await supabase.rpc('exec_sql', { sql: command });
    }
    for (const policy of policies) {
      await supabase.rpc('exec_sql', { sql: policy });
    }
  } catch (error) {
    console.error('Error setting up RLS:', error);
  }
};

// دالة لإعداد التخزين (Storage)
export const setupStorage = async () => {
  try {
    // إنشاء bucket للصور
    const { error: bucketError } = await supabase.storage.createBucket('project-images', {
      public: true,
      allowedMimeTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
      fileSizeLimit: 10485760 // 10MB
    });

    if (bucketError && !bucketError.message.includes('already exists')) {
      console.error('Error creating storage bucket:', bucketError);
      return { success: false, error: bucketError.message };
    }

    // إنشاء bucket للمستندات
    const { error: docsBucketError } = await supabase.storage.createBucket('project-documents', {
      public: true, // تغيير إلى public للوصول المباشر
      allowedMimeTypes: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
      fileSizeLimit: 10485760 // 10MB
    });

    if (docsBucketError && !docsBucketError.message.includes('already exists')) {
      console.error('Error creating documents bucket:', docsBucketError);
    }

    // إنشاء جدول الملفات
    const { error: tableError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS project_files (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
          file_name VARCHAR(255) NOT NULL,
          file_path VARCHAR(500) NOT NULL,
          file_size BIGINT NOT NULL,
          file_type VARCHAR(100) NOT NULL,
          bucket_name VARCHAR(100) NOT NULL,
          file_url TEXT,
          uploaded_by UUID REFERENCES auth.users(id),
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        -- إنشاء فهارس للبحث السريع
        CREATE INDEX IF NOT EXISTS idx_project_files_project_id ON project_files(project_id);
        CREATE INDEX IF NOT EXISTS idx_project_files_created_at ON project_files(created_at);

        -- إعداد RLS
        ALTER TABLE project_files ENABLE ROW LEVEL SECURITY;

        -- سياسات RLS
        DROP POLICY IF EXISTS "Enable read access for all users" ON project_files;
        CREATE POLICY "Enable read access for all users" ON project_files
          FOR SELECT USING (true);

        DROP POLICY IF EXISTS "Enable insert for authenticated users only" ON project_files;
        CREATE POLICY "Enable insert for authenticated users only" ON project_files
          FOR INSERT WITH CHECK (true);

        DROP POLICY IF EXISTS "Enable update for authenticated users only" ON project_files;
        CREATE POLICY "Enable update for authenticated users only" ON project_files
          FOR UPDATE USING (true);

        DROP POLICY IF EXISTS "Enable delete for authenticated users only" ON project_files;
        CREATE POLICY "Enable delete for authenticated users only" ON project_files
          FOR DELETE USING (true);
      `
    });

    if (tableError) {
      console.error('Error creating files table:', tableError);
    }

    return { success: true, message: 'تم إعداد التخزين وجدول الملفات بنجاح' };
  } catch (error) {
    console.error('Error setting up storage:', error);
    return { success: false, error: error.message };
  }
};
