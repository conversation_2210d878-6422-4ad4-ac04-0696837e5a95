// خدمة تصدير طلبات الصيانة بصيغة PDF وExcel

import jsPDF from 'jspdf';
import 'jspdf-autotable';
import * as XLSX from 'xlsx';
import { saveAs } from 'file-saver';

// تصدير طلبات الصيانة بصيغة PDF
export const exportMaintenanceRequestsToPDF = (requests, title, t, isRTL = false) => {
  // إنشاء مستند PDF جديد
  const doc = new jsPDF({
    orientation: 'landscape',
    unit: 'mm',
    format: 'a4'
  });

  // إضافة دعم اللغة العربية
  if (isRTL) {
    doc.setR2L(true);
  }

  // إضافة عنوان التقرير
  doc.setFontSize(18);
  const pageWidth = doc.internal.pageSize.getWidth();
  doc.text(title, pageWidth / 2, 15, { align: 'center' });

  // إضافة التاريخ
  doc.setFontSize(10);
  const today = new Date().toLocaleDateString(isRTL ? 'ar-SA' : 'en-US');
  doc.text(`${t('exportService.date') || 'Date'}: ${today}`, pageWidth - 20, 10, { align: isRTL ? 'left' : 'right' });

  // تحضير البيانات للجدول
  const tableData = requests.map(request => [
    request.projects?.project_number || t('warranty.projectN_A') || 'N/A',
    request.projects?.client_name || '',
    request.issue_type || '',
    request.description || '',
    request.request_date ? new Date(request.request_date).toLocaleDateString(isRTL ? 'ar-SA' : 'en-US') : '',
    t(`warranty.status_${request.status?.toLowerCase().replace(' ', '')}`) || request.status || ''
  ]);

  // تحديد رؤوس الأعمدة
  const headers = [
    [
      t('allProjects.projectNumber') || 'Project Number',
      t('addProject.clientName') || 'Client Name',
      t('warranty.issueType') || 'Issue Type',
      t('warranty.description') || 'Description',
      t('warranty.requestDate') || 'Request Date',
      t('allProjects.status') || 'Status'
    ]
  ];

  // إنشاء الجدول
  doc.autoTable({
    head: headers,
    body: tableData,
    startY: 25,
    theme: 'grid',
    styles: {
      fontSize: 8,
      cellPadding: 2,
      overflow: 'linebreak',
      halign: isRTL ? 'right' : 'left'
    },
    headStyles: {
      fillColor: [41, 128, 185],
      textColor: 255,
      fontStyle: 'bold'
    },
    alternateRowStyles: {
      fillColor: [240, 240, 240]
    },
    columnStyles: {
      3: { cellWidth: 60 } // عرض أكبر لعمود الوصف
    }
  });

  // إضافة ترويسة وتذييل
  const pageCount = doc.internal.getNumberOfPages();
  for (let i = 1; i <= pageCount; i++) {
    doc.setPage(i);
    // تذييل الصفحة
    doc.setFontSize(8);
    doc.text(
      `${t('exportService.page') || 'Page'} ${i} ${t('exportService.of') || 'of'} ${pageCount}`,
      pageWidth / 2,
      doc.internal.pageSize.getHeight() - 10,
      { align: 'center' }
    );
    // ترويسة الصفحة - اسم التطبيق
    doc.text('SultanTrack', 20, 10);
  }

  // حفظ الملف
  doc.save(`${title.replace(/\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.pdf`);
};

// تصدير طلبات الصيانة بصيغة Excel
export const exportMaintenanceRequestsToExcel = (requests, title, t) => {
  // تحضير البيانات
  const data = requests.map(request => ({
    [t('allProjects.projectNumber') || 'Project Number']: request.projects?.project_number || t('warranty.projectN_A') || 'N/A',
    [t('addProject.clientName') || 'Client Name']: request.projects?.client_name || '',
    [t('warranty.issueType') || 'Issue Type']: request.issue_type || '',
    [t('warranty.description') || 'Description']: request.description || '',
    [t('warranty.requestDate') || 'Request Date']: request.request_date ? new Date(request.request_date).toLocaleDateString() : '',
    [t('allProjects.status') || 'Status']: t(`warranty.status_${request.status?.toLowerCase().replace(' ', '')}`) || request.status || ''
  }));

  // إنشاء ورقة عمل
  const worksheet = XLSX.utils.json_to_sheet(data);

  // إنشاء مصنف عمل
  const workbook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(workbook, worksheet, 'Maintenance Requests');

  // تصدير المصنف
  const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
  const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
  saveAs(blob, `${title.replace(/\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.xlsx`);
};

// تصدير المشاريع تحت الضمان بصيغة PDF
export const exportWarrantyProjectsToPDF = (projects, title, t, isRTL = false) => {
  // إنشاء مستند PDF جديد
  const doc = new jsPDF({
    orientation: 'landscape',
    unit: 'mm',
    format: 'a4'
  });

  // إضافة دعم اللغة العربية
  if (isRTL) {
    doc.setR2L(true);
  }

  // إضافة عنوان التقرير
  doc.setFontSize(18);
  const pageWidth = doc.internal.pageSize.getWidth();
  doc.text(title, pageWidth / 2, 15, { align: 'center' });

  // إضافة التاريخ
  doc.setFontSize(10);
  const today = new Date().toLocaleDateString(isRTL ? 'ar-SA' : 'en-US');
  doc.text(`${t('exportService.date') || 'Date'}: ${today}`, pageWidth - 20, 10, { align: isRTL ? 'left' : 'right' });

  // تحضير البيانات للجدول
  const tableData = projects.map(project => {
    const completionDate = project.completion_date ? new Date(project.completion_date) : null;
    const warrantyEnd = completionDate ? new Date(completionDate.setFullYear(completionDate.getFullYear() + 5)) : null;

    return [
      project.project_number || '',
      project.client_name || '',
      project.city || '',
      project.completion_date ? new Date(project.completion_date).toLocaleDateString(isRTL ? 'ar-SA' : 'en-US') : '',
      warrantyEnd ? warrantyEnd.toLocaleDateString(isRTL ? 'ar-SA' : 'en-US') : ''
    ];
  });

  // تحديد رؤوس الأعمدة
  const headers = [
    [
      t('allProjects.projectNumber') || 'Project Number',
      t('addProject.clientName') || 'Client Name',
      t('addProject.city') || 'City',
      t('warranty.completionDate') || 'Completion Date',
      t('warranty.warrantyEndDate') || 'Warranty End Date'
    ]
  ];

  // إنشاء الجدول
  doc.autoTable({
    head: headers,
    body: tableData,
    startY: 25,
    theme: 'grid',
    styles: {
      fontSize: 8,
      cellPadding: 2,
      overflow: 'linebreak',
      halign: isRTL ? 'right' : 'left'
    },
    headStyles: {
      fillColor: [41, 128, 185],
      textColor: 255,
      fontStyle: 'bold'
    },
    alternateRowStyles: {
      fillColor: [240, 240, 240]
    }
  });

  // إضافة ترويسة وتذييل
  const pageCount = doc.internal.getNumberOfPages();
  for (let i = 1; i <= pageCount; i++) {
    doc.setPage(i);
    // تذييل الصفحة
    doc.setFontSize(8);
    doc.text(
      `${t('exportService.page') || 'Page'} ${i} ${t('exportService.of') || 'of'} ${pageCount}`,
      pageWidth / 2,
      doc.internal.pageSize.getHeight() - 10,
      { align: 'center' }
    );
    // ترويسة الصفحة - اسم التطبيق
    doc.text('SultanTrack', 20, 10);
  }

  // حفظ الملف
  doc.save(`${title.replace(/\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.pdf`);
};

// تصدير المشاريع تحت الضمان بصيغة Excel
export const exportWarrantyProjectsToExcel = (projects, title, t) => {
  // تحضير البيانات
  const data = projects.map(project => {
    const completionDate = project.completion_date ? new Date(project.completion_date) : null;
    const warrantyEnd = completionDate ? new Date(completionDate.setFullYear(completionDate.getFullYear() + 5)) : null;

    return {
      [t('allProjects.projectNumber') || 'Project Number']: project.project_number || '',
      [t('addProject.clientName') || 'Client Name']: project.client_name || '',
      [t('addProject.city') || 'City']: project.city || '',
      [t('warranty.completionDate') || 'Completion Date']: project.completion_date ? new Date(project.completion_date).toLocaleDateString() : '',
      [t('warranty.warrantyEndDate') || 'Warranty End Date']: warrantyEnd ? warrantyEnd.toLocaleDateString() : ''
    };
  });

  // إنشاء ورقة عمل
  const worksheet = XLSX.utils.json_to_sheet(data);

  // إنشاء مصنف عمل
  const workbook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(workbook, worksheet, 'Warranty Projects');

  // تصدير المصنف
  const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
  const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
  saveAs(blob, `${title.replace(/\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.xlsx`);
};

export default {
  exportMaintenanceRequestsToPDF,
  exportMaintenanceRequestsToExcel,
  exportWarrantyProjectsToPDF,
  exportWarrantyProjectsToExcel
};
