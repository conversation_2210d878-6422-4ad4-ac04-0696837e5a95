{"appName": "SultanTrack", "sidebar": {"dashboard": "Dashboard", "allProjects": "All Projects", "addProject": "Add Project", "trackProject": "Track Project", "allVisits": "All Visits", "warrantyMaintenance": "Warranty & Maintenance", "userManagement": "User Management", "backup": "Backup & Restore", "profile": "Profile", "supabaseSetup": "Database Setup", "logout": "Logout", "closeSidebar": "Close sidebar", "openSidebar": "Open sidebar"}, "dashboard": {"title": "Dashboard", "totalProjects": "Total Projects", "projectStatus": "Project Status", "notStarted": "Not Started", "inProgress": "In Progress", "delayed": "Delayed", "completed": "Completed", "pending": "Pending", "onHold": "On Hold", "totalQuantitiesExecuted": "Total Quantities Executed (per type):", "pvc": "PVC", "shutter": "Shutter", "sgs": "SGS", "doors": "Doors", "glass": "Glass", "unit": "unit", "chartsTitle": "Project Status & Progress", "installedMaterialsChart": "Installed Materials from Visits", "installedMaterialsDescription": "Total materials installed from all field visits", "timeFilter": "Time Filter:", "week": "Week", "month": "Month", "year": "Year", "refresh": "Refresh", "quantity": "Quantity", "installedQuantity": "Installed Quantity", "all": "All", "projectStatusDescription": "Distribution of projects by current status", "analytics": {"projectsStats": "Projects Statistics", "maintenanceStats": "Maintenance Statistics", "materialStats": "Material Shortage Statistics", "monthlyTrends": "Monthly Trends", "timeTrends": "Time Trends", "totalMaintenanceRequests": "Total Maintenance Requests", "completedMaintenance": "Completed Maintenance", "pendingMaintenance": "Pending Maintenance", "stoppedDueToShortage": "Stopped Due to Shortage", "partialShortage": "Partial Shortage", "ongoingProjects": "Ongoing Projects", "completedInPeriod": "Completed in Period", "projectsCompleted": "Projects Completed", "maintenanceCompleted": "Maintenance Completed", "completedDuringWeek": "Completed During Week", "completedDuringMonth": "Completed During Month", "completedDuringYear": "Completed During Year"}, "quickActions": {"title": "Quick Actions", "addProject": "Add New Project", "addMaintenanceRequest": "Add Maintenance Request", "addVisit": "Add New Visit", "addProjectDesc": "Create a new project quickly", "addMaintenanceDesc": "Register a new maintenance request", "addVisitDesc": "Record a new field visit"}, "filters": {"title": "Data Filters", "timeRange": "Time Range", "lastWeek": "Last Week", "lastMonth": "Last Month", "lastQuarter": "Last Quarter", "lastYear": "Last Year", "customRange": "Custom Range", "startDate": "Start Date", "endDate": "End Date", "city": "City", "selectCity": "Select City", "allCities": "All Cities", "projectStatus": "Project Status", "selectStatus": "Select Status", "allStatuses": "All Statuses", "pending": "Pending", "inProgress": "In Progress", "onHold": "On Hold", "materialType": "Material Type", "selectMaterial": "Select Material", "allMaterials": "All Materials", "apply": "Apply Filters", "reset": "Reset"}}, "login": {"title": "Login to SultanTrack", "username": "Email Address", "password": "Password", "emailPlaceholder": "Enter your email address", "passwordPlaceholder": "Enter your password", "loginButton": "<PERSON><PERSON>", "authFailedTitle": "Authentication Failed", "invalidCredentials": "Invalid email or password. Please try again.", "loginSuccessTitle": "Login Successful", "welcomeMessage": "Welcome back, {{email}}!", "loggingIn": "Logging in..."}, "addProject": {"formTitle": "New Project Details", "contractNumber": "Contract Number", "contractNumberPlaceholder": "Enter contract number", "jobOrder": "Job Order", "jobOrderPlaceholder": "Enter job order number", "clientName": "Client Name", "city": "City", "startDate": "Start Date", "durationDays": "Project Duration (Weeks)", "quantities": "Quantities", "quantity_pvc": "PVC (units)", "quantity_shutter": "Shutter (units)", "quantity_sgs": "SGS (units)", "quantity_doors": "Doors (units)", "quantity_glass": "Glass (units)", "projectType": "Project Type", "newInstallation": "New Installation", "maintenance": "Maintenance", "notes": "Notes", "uploadFiles": "Upload Files (PDF, Images)", "filesSelected": "files selected", "selectedFiles": "Selected files ({{count}})", "selectFilesButton": "Select files", "dragAndDrop": "or drag and drop", "fileTypes": "PDF, JPG, PNG up to 10MB each. Max 5 files.", "submitButton": "Add Project", "submitting": "Submitting...", "errorTitle": "Error Adding Project", "fileUploadErrorTitle": "File Upload Error", "fileUploadErrorMessage": "Could not upload {{fileName}}", "successTitle": "Project Added", "successMessage": "Project {{projectName}} has been added successfully."}, "allProjects": {"searchPlaceholder": "Search by No., Client, City...", "projectNumber": "Project No.", "clientName": "Client", "city": "City", "status": "Status", "progress": "Progress %", "notes": "Notes", "actions": "Actions", "loading": "Loading projects...", "noProjectsFound": "No projects found matching your criteria.", "fetchErrorTitle": "<PERSON><PERSON>r", "fetchErrorMsg": "Could not fetch projects list.", "confirmDeleteTitle": "Confirm Deletion", "confirmDeleteMsg": "Are you sure you want to delete project {{projectName}}? This action cannot be undone.", "deleteButton": "Delete", "deleteErrorTitle": "Error Deleting Project", "deleteSuccessTitle": "Project Deleted", "deleteSuccessMsg": "Project {{projectName}} has been deleted.", "noProjectsMatchSearch": "Try adjusting your search term.", "tryAddingProjects": "Try adding some projects to see them here."}, "trackProject": {"backToProjects": "Back to Projects", "projectInfo": "Project Information", "progressSummary": "Progress Summary", "remaining": "Remaining", "addVisitTitle": "Record New Field Visit", "visitDate": "Visit Date", "completed": "{{item}} Completed", "visitNotes": "Visit Notes", "submitVisitButton": "Record Visit", "submittingVisit": "Submitting Visit...", "visitHistory": "Visit History", "visitOn": "Visit on", "noVisits": "None", "loading": "Loading project data...", "notFound": "Project not found.", "fetchErrorTitle": "Error", "fetchProjectErrorMsg": "Could not fetch project details.", "fetchVisitsErrorMsg": "Could not fetch project visits.", "addVisitErrorTitle": "<PERSON><PERSON><PERSON>", "addVisitSuccessTitle": "Visit Added", "addVisitSuccessMsg": "Field visit recorded successfully.", "completedThisVisit": "Completed", "selectProjectDescription": "Select a project to track its progress and manage visits", "trackThisProject": "Track This Project", "detailedProgress": "Detailed Progress", "totalVisits": "Total Visits", "completionPercentage": "Completion Percentage", "projectDays": "Project Days", "lastVisit": "Last Visit"}, "searchProject": {"searchTitle": "Find Project", "enterSearchTerm": "Enter Search Term", "searchBy": "Search By", "searchButton": "Search", "searching": "Searching...", "validationTitle": "Search Term Required", "validationMsg": "Please enter a search term to begin.", "errorTitle": "Search Error", "noResultsTitle": "No Results", "noResultsMsg": "No projects found matching your criteria.", "downloadPdf": "Download Project PDF", "downloadTitle": "Download PDF", "downloadMsg": "PDF download for project", "comingSoon": "(Feature Coming Soon)", "loadingResults": "Loading results...", "noResultsFoundFor": "No results found for:", "startSearching": "Start searching for projects by number or client name."}, "warranty": {"warrantyProjectsTitle": "Projects Under Warranty", "completionDate": "Completion Date", "warrantyEndDate": "Warranty End Date", "loadingWarranty": "Loading warranty projects...", "noWarrantyProjects": "No projects currently under warranty.", "maintenanceRequestsTitle": "Maintenance Requests", "addRequestButton": "Add New Request", "loadingMaintenance": "Loading maintenance requests...", "noMaintenanceRequests": "No maintenance requests.", "newMaintenanceRequest": "New Maintenance Request", "editMaintenanceRequest": "Edit Maintenance Request", "issueType": "Issue Type", "description": "Description", "requestDate": "Request Date", "selectProject": "-- Select Project --", "newRequest": "New", "clientName": "Client Name", "jobOrderNumber": "Job Order / Contract Number", "contractDate": "Contract Date", "clientPhone": "Client Phone Number", "city": "City", "attachFiles": "Attach Files", "fileTypes": "PDF, Images, Documents - up to 10MB each", "projectN_A": "N/A", "status_new": "New", "status_inprogress": "In Progress", "status_closed": "Closed", "cancelButton": "Cancel", "submitButton": "Submit Request", "fetchErrorTitle": "<PERSON><PERSON>r", "fetchWarrantyErrorMsg": "Could not fetch warranty projects.", "fetchMaintenanceErrorMsg": "Could not fetch maintenance requests.", "validationErrorTitle": "Validation Error", "validationErrorMsg": "Required fields must be filled.", "addRequestErrorTitle": "Error Adding Request", "addRequestSuccessTitle": "Request Added", "addRequestSuccessMsg": "Maintenance request added successfully.", "updateRequestSuccessTitle": "Request Updated", "updateRequestSuccessMsg": "Maintenance request updated successfully.", "confirmDeleteRequestTitle": "Confirm Deletion", "confirmDeleteRequestMsg": "Are you sure you want to delete this maintenance request?"}, "exportService": {"exportPDF": "Export PDF", "exportExcel": "Export Excel", "projectsReport": "Projects Report", "date": "Date", "page": "Page", "of": "of", "downloadSuccess": "PDF file has been generated successfully.", "downloadError": "Could not generate file."}, "editProject": {"title": "Edit Project", "basicInfo": "Basic Information", "projectDetails": "Project Details", "quantities": "Quantities", "cancel": "Cancel", "saveChanges": "Save Changes", "notFoundTitle": "Project Not Found", "notFoundMsg": "The requested project could not be found.", "fetchErrorTitle": "Error", "fetchErrorMsg": "Could not fetch project details.", "updateSuccessTitle": "Project Updated", "updateSuccessMsg": "Project has been updated successfully.", "updateErrorTitle": "Error", "updateErrorMsg": "Could not update project. Please try again.", "address": "Address", "phone": "Phone", "email": "Email", "completionDate": "Completion Date", "status": "Status", "selectStatus": "Select Status", "exportPDF": "Export PDF", "exportExcel": "Export Excel", "uploadFiles": "Upload Files (PDF, Images)", "addNewFiles": "Add New Files"}, "userManagement": {"addUserButton": "Add User", "usersList": "Users List", "fullName": "Full Name", "email": "Email", "role": "Role", "loadingUsers": "Loading users...", "noUsers": "No users found.", "confirmDeleteMsg": "Are you sure you want to delete user {{email}}?", "confirmDeleteTitle": "Confirm Deletion", "deleteButton": "Delete", "editUserTitle": "Edit User", "addUserTitle": "Add New User", "password": "Password", "tryAddingUsers": "Add users to manage them here.", "permissionsTitle": "Permissions", "permissions": {"view": "View Data", "edit": "Edit Data", "add": "Add Data", "delete": "Delete Data", "dashboardAccess": "Dashboard Access"}, "roles": {"manager": "Manager", "supervisor": "Supervisor", "data_entry": "Data Entry", "technician": "Technician"}, "cancelButton": "Cancel", "saveChangesButton": "Save Changes", "fetchErrorTitle": "<PERSON><PERSON>r", "fetchErrorMsg": "Could not fetch users. Ensure 'profiles' table exists and RLS is configured.", "updateErrorTitle": "Update Error", "updateSuccessTitle": "User Updated", "updateSuccessMsg": "User {{email}} updated successfully.", "addErrorTitle": "Add Error", "profileErrorTitle": "Profile Error", "addSuccessTitle": "User Added", "addSuccessMsg": "User {{email}} added successfully.", "deleteErrorTitle": "Delete Error", "deleteSuccessTitle": "User Deleted", "deleteSuccessMsg": "User {{email}} deleted successfully."}, "backup": {"title": "Backup & Restore", "description": "Create backups of your project data and restore them when needed. You can also schedule automatic backups to Google Drive.", "manualBackup": "Manual Backup", "createBackup": "Create Backup", "downloadBackup": "Download Backup", "restoreBackup": "Restore Backup", "selectFile": "Select Backup File", "uploadFile": "Upload & Restore", "googleDriveBackup": "Google Drive Backup", "connectGoogleDrive": "Connect to Google Drive", "disconnectGoogleDrive": "Disconnect from Google Drive", "scheduleBackup": "Schedule Automatic Backup", "frequency": "Backup Frequency", "daily": "Daily", "weekly": "Weekly", "monthly": "Monthly", "backupTime": "Backup Time", "saveSchedule": "Save Schedule", "selectDataTitle": "Select Data to Backup", "selectDataDescription": "Choose which data you want to include in your backup", "projectsData": "Projects Data", "visitsData": "Visits Data", "maintenanceData": "Maintenance Requests Data", "usersData": "Users Data", "emailNotifications": "Email Notifications", "emailNotificationsDescription": "Receive backup notifications and files via email", "emailAddress": "Email Address", "addEmail": "Add <PERSON>", "emailSettings": "<PERSON><PERSON>s", "sendBackupToEmail": "Send Backup to Email", "notifyOnBackupComplete": "Notify on Backup Complete", "notifyOnBackupFail": "Notify on Backup Failure", "sendTestEmail": "Send Test Email", "existingBackups": "Existing Backups", "existingBackupsDescription": "View and manage your existing backups", "noBackupsFound": "No backups found", "backupName": "Name", "backupDate": "Date", "backupSize": "Size", "actions": "Actions", "download": "Download", "restore": "Rest<PERSON>", "delete": "Delete", "restoreFromFile": "Restore from File", "restoreFromFileDescription": "Upload a backup file to restore your data", "restoreInstructions": "Restore Instructions", "step1": "Select a backup file (.json) from your computer", "step2": "Click 'Upload & Restore' to begin the restoration process", "step3": "Wait for the restoration to complete", "warning": "Warning", "restoreWarning": "Restoring will replace all current data. Make sure to create a backup first.", "confirmDeleteTitle": "Confirm Deletion", "confirmDeleteMsg": "Are you sure you want to delete this backup? This action cannot be undone.", "confirmRestoreTitle": "Confirm <PERSON>ore", "confirmRestoreMsg": "Are you sure you want to restore this backup? Current data will be replaced.", "cancel": "Cancel", "backupSuccessTitle": "Backup Created", "backupSuccessMsg": "Backup has been created successfully.", "restoreSuccessTitle": "Restore Completed", "restoreSuccessMsg": "Data has been restored successfully.", "deleteSuccessTitle": "Backup Deleted", "deleteSuccessMsg": "Backup has been deleted successfully.", "errorTitle": "Error", "backupErrorMsg": "Could not create backup. Please try again.", "restoreErrorMsg": "Could not restore backup. Please try again.", "deleteErrorMsg": "Could not delete backup. Please try again.", "invalidEmail": "Invalid email address", "emailAddedSuccess": "Email added successfully", "emailRemovedSuccess": "Email removed successfully", "testEmailSent": "Test email sent successfully", "settingsSavedTitle": "Settings Saved", "settingsSavedMsg": "Email settings have been saved.", "scheduleSuccessTitle": "Schedule Saved", "scheduleSuccessMsg": "Automatic backup schedule has been saved.", "googleConnectSuccessTitle": "Connected to Google Drive", "googleConnectSuccessMsg": "Your account has been connected to Google Drive.", "connectedToGoogleDrive": "Connected to Google Drive", "backupHistory": "Backup History", "noBackups": "No backups found", "date": "Date", "size": "Size", "googleConnectErrorMsg": "Could not connect to Google Drive. Please try again.", "scheduleErrorMsg": "Could not save backup schedule. Please try again.", "allData": "All Data", "removeEmail": "Remove"}, "fileDisplay": {"noFiles": "No files uploaded yet", "uploadedFiles": "Uploaded Files", "uploadedOn": "Uploaded on", "viewFile": "View file", "downloadFile": "Download file", "deleteFile": "Delete file", "addNewFiles": "Add New Files", "uploadNewFiles": "Upload New Files"}, "visits": {"visits": "Visits", "addVisit": "Add Visit", "addNewVisit": "Add New Visit", "editVisit": "<PERSON>", "visitInfo": "Visit Information", "selectProject": "Select Project", "visitDate": "Visit Date", "projectStatus": "Project Status", "selectStatus": "Select Project Status", "ongoing": "Ongoing", "partialShortage": "Partial Shortage", "stoppedShortage": "Stopped Due to Shortage", "completed": "Completed", "materialShortages": "Material Shortages", "materialsInstalled": "Materials Installed Today", "pvcUnits": "PVC (units)", "shutterUnits": "Shutter (units)", "sgsUnits": "SGS (units)", "doorsUnits": "Doors (units)", "glassUnits": "Glass (units)", "visitNotes": "Visit Notes", "notesPlaceholder": "Enter notes about the visit...", "saveVisit": "Save Visit", "updateVisit": "Update Visit", "noVisitsYet": "No visits recorded yet", "clickAddVisit": "<PERSON>lick \"Add Visit\" to record the first visit", "totalShortages": "Total Shortages", "units": "units", "totalVisits": "Total Visits", "visit": "visit", "allVisits": "All Visits", "allVisitsDescription": "View and manage all project visits", "searchPlaceholder": "Search by project number, client, or city...", "allStatuses": "All Statuses", "activeProjects": "Active Projects", "filteredResults": "Filtered Results", "thisMonth": "This Month", "visitsList": "Visits List", "noFilteredVisits": "No visits match your filters", "loadError": "Failed to load visits", "notes": "Notes", "updateSuccessTitle": "Visit Updated", "updateSuccessMsg": "Visit has been updated successfully", "updateErrorTitle": "Update Error", "updateErrorMsg": "Failed to update visit. Please try again.", "installed": "Installed", "noMaterialsInstalled": "No materials installed", "shortages": "Shortages"}, "profile": {"title": "Profile", "personalInfo": "Personal Information", "accountInfo": "Account Information", "editButton": "Edit", "saveButton": "Save", "cancelButton": "Cancel", "fullName": "Full Name", "fullNamePlaceholder": "Enter full name", "email": "Email", "phone": "Phone Number", "phonePlaceholder": "Enter phone number", "city": "City", "cityPlaceholder": "Enter city", "department": "Department", "departmentPlaceholder": "Enter department", "role": "Role", "bio": "Bio", "bioPlaceholder": "Enter bio...", "noBio": "No bio added yet.", "memberSince": "Member Since", "lastLogin": "Last Login", "accountStatus": "Account Status", "active": "Active", "avatarUploadTitle": "Upload Avatar", "avatarUploadMsg": "Avatar upload feature will be available soon", "fetchErrorTitle": "<PERSON><PERSON>r", "updateSuccessTitle": "Update Successful", "updateSuccessMsg": "Profile has been updated successfully.", "updateErrorTitle": "Update Error"}, "common": {"cancel": "Cancel", "save": "Save", "edit": "Edit", "delete": "Delete", "loading": "Loading...", "notSpecified": "Not Specified", "error": "Error"}}