import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import { Calendar, Edit, Search, Filter, ChevronDown, Building2, User, MapPin } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import { getAllVisits, updateVisit } from '@/lib/visitsService';
import { getAllProjects } from '@/lib/supabaseDatabase';
import VisitModal from '@/components/visits/VisitModal';

const AllVisitsPage = () => {
  const { t, i18n } = useTranslation();
  const { toast } = useToast();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [visits, setVisits] = useState([]);
  const [projects, setProjects] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [filteredVisits, setFilteredVisits] = useState([]);

  // حالات نافذة الزيارة
  const [showVisitModal, setShowVisitModal] = useState(false);
  const [editingVisit, setEditingVisit] = useState(null);
  const [selectedProject, setSelectedProject] = useState(null);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5 }
    }
  };

  // Load data
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        const [visitsData, projectsData] = await Promise.all([
          getAllVisits(),
          getAllProjects()
        ]);

        // Create projects map for quick lookup
        const projectsMap = {};
        projectsData.forEach(project => {
          projectsMap[project.id] = project;
        });

        // Enrich visits with project data
        const enrichedVisits = visitsData.map(visit => ({
          ...visit,
          project: projectsMap[visit.project_id] || {}
        }));

        // Sort by date (newest first)
        enrichedVisits.sort((a, b) => new Date(b.visit_date) - new Date(a.visit_date));

        setVisits(enrichedVisits);
        setProjects(projectsData);
        setFilteredVisits(enrichedVisits);
      } catch (error) {
        console.error('Error loading visits:', error);
        toast({
          title: t('common.error') || 'Error',
          description: t('visits.loadError') || 'Failed to load visits',
          variant: 'destructive'
        });
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [t, toast]);

  // Filter visits
  useEffect(() => {
    let filtered = visits;

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(visit =>
        visit.project?.project_number?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        visit.project?.client_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        visit.project?.city?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(visit => {
        const status = visit.project_status?.toLowerCase() || '';

        // تطابق القيم مع الفلتر
        if (statusFilter === 'ongoing') {
          return status === 'ongoing';
        } else if (statusFilter === 'partialShortage') {
          return status === 'partial shortage';
        } else if (statusFilter === 'stoppedShortage') {
          return status === 'stopped due to shortage';
        } else if (statusFilter === 'completed') {
          return status === 'completed';
        }
        return false;
      });
    }

    setFilteredVisits(filtered);
  }, [visits, searchTerm, statusFilter]);

  // Get status badge
  const getStatusBadge = (status) => {

    // تطبيع الحالة - التعامل مع القيم المختلفة
    let normalizedStatus = 'ongoing'; // القيمة الافتراضية

    if (typeof status === 'string') {
      const lowerStatus = status.toLowerCase();

      // التحقق من القيم الإنجليزية المحفوظة في قاعدة البيانات
      if (lowerStatus === 'ongoing' || lowerStatus.includes('مستمر')) {
        normalizedStatus = 'ongoing';
      } else if (lowerStatus === 'partial shortage' || lowerStatus.includes('نواقص جزئي')) {
        normalizedStatus = 'partialShortage';
      } else if (lowerStatus === 'stopped due to shortage' || lowerStatus.includes('متوقف')) {
        normalizedStatus = 'stoppedShortage';
      } else if (lowerStatus === 'completed' || lowerStatus.includes('منجز')) {
        normalizedStatus = 'completed';
      }
    }

    const statusConfig = {
      'ongoing': {
        label: t('visits.ongoing') || 'مستمر',
        className: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
      },
      'partialShortage': {
        label: t('visits.partialShortage') || 'نواقص جزئي',
        className: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
      },
      'stoppedShortage': {
        label: t('visits.stoppedShortage') || 'متوقف على نواقص',
        className: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
      },
      'completed': {
        label: t('visits.completed') || 'منجز',
        className: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
      }
    };

    const config = statusConfig[normalizedStatus];
    return (
      <Badge className={config.className}>
        {config.label}
      </Badge>
    );
  };

  // Handle edit visit
  const handleEditVisit = (visit) => {
    setEditingVisit(visit);
    setSelectedProject(visit.project);
    setShowVisitModal(true);
  };

  // Handle save visit
  const handleSaveVisit = async (visitData) => {
    try {
      const result = await updateVisit(visitData.id, visitData);

      if (result.success) {
        // إعادة تحميل البيانات
        const [visitsData, projectsData] = await Promise.all([
          getAllVisits(),
          getAllProjects()
        ]);

        // Create projects map for quick lookup
        const projectsMap = {};
        projectsData.forEach(project => {
          projectsMap[project.id] = project;
        });

        // Enrich visits with project data
        const enrichedVisits = visitsData.map(visit => ({
          ...visit,
          project: projectsMap[visit.project_id] || {}
        }));

        // Sort by date (newest first)
        enrichedVisits.sort((a, b) => new Date(b.visit_date) - new Date(a.visit_date));

        setVisits(enrichedVisits);
        setShowVisitModal(false);
        setEditingVisit(null);
        setSelectedProject(null);

        toast({
          title: t('visits.updateSuccessTitle') || 'Visit Updated',
          description: t('visits.updateSuccessMsg') || 'Visit has been updated successfully',
          className: "bg-green-500 text-white"
        });
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      console.error('Error saving visit:', error);
      toast({
        title: t('visits.updateErrorTitle') || 'Update Error',
        description: t('visits.updateErrorMsg') || 'Failed to update visit. Please try again.',
        variant: 'destructive'
      });
    }
  };

  // Format date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString(i18n.language === 'ar' ? 'ar-SA' : 'en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={containerVariants}
      className="p-4 md:p-6"
      dir={i18n.dir()}
    >
      {/* Header */}
      <motion.div variants={itemVariants} className="mb-6">
        <h1 className="text-3xl font-bold text-gray-800 dark:text-white mb-2">
          {t('visits.allVisits') || 'All Visits'}
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          {t('visits.allVisitsDescription') || 'View and manage all project visits'}
        </p>
      </motion.div>

      {/* Filters */}
      <motion.div variants={itemVariants} className="mb-6">
        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col md:flex-row gap-4">
              {/* Search */}
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder={t('visits.searchPlaceholder') || 'Search by project number, client, or city...'}
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              {/* Status Filter */}
              <div className="md:w-48">
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                >
                  <option value="all">{t('visits.allStatuses') || 'All Statuses'}</option>
                  <option value="ongoing">{t('visits.ongoing') || 'Ongoing'}</option>
                  <option value="partialShortage">{t('visits.partialShortage') || 'Partial Shortage'}</option>
                  <option value="stoppedShortage">{t('visits.stoppedShortage') || 'Stopped Due to Shortage'}</option>
                  <option value="completed">{t('visits.completed') || 'Completed'}</option>
                </select>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Stats */}
      <motion.div variants={itemVariants} className="mb-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center">
                <Calendar className="h-8 w-8 text-blue-500" />
                <div className="ml-3">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    {t('visits.totalVisits') || 'Total Visits'}
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {visits.length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center">
                <Building2 className="h-8 w-8 text-green-500" />
                <div className="ml-3">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    {t('visits.activeProjects') || 'Active Projects'}
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {new Set(visits.map(v => v.project_id)).size}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center">
                <Filter className="h-8 w-8 text-orange-500" />
                <div className="ml-3">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    {t('visits.filteredResults') || 'Filtered Results'}
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {filteredVisits.length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center">
                <Calendar className="h-8 w-8 text-purple-500" />
                <div className="ml-3">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    {t('visits.thisMonth') || 'This Month'}
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {visits.filter(v => {
                      const visitDate = new Date(v.visit_date);
                      const now = new Date();
                      return visitDate.getMonth() === now.getMonth() &&
                             visitDate.getFullYear() === now.getFullYear();
                    }).length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </motion.div>

      {/* Visits List */}
      <motion.div variants={itemVariants}>
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Calendar className="h-5 w-5 mr-2" />
              {t('visits.visitsList') || 'Visits List'}
            </CardTitle>
          </CardHeader>
          <CardContent>
            {filteredVisits.length === 0 ? (
              <div className="text-center py-8">
                <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500 dark:text-gray-400">
                  {searchTerm || statusFilter !== 'all'
                    ? (t('visits.noFilteredVisits') || 'No visits match your filters')
                    : (t('visits.noVisitsYet') || 'No visits recorded yet')
                  }
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {filteredVisits.map((visit) => (
                  <motion.div
                    key={visit.id}
                    variants={itemVariants}
                    className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1 grid grid-cols-1 md:grid-cols-5 gap-4 items-center">
                        {/* Date */}
                        <div className="flex items-center">
                          <Calendar className="h-4 w-4 text-gray-400 mr-2" />
                          <span className="text-sm font-medium text-gray-900 dark:text-white">
                            {formatDate(visit.visit_date)}
                          </span>
                        </div>

                        {/* Project Number */}
                        <div className="flex items-center">
                          <Building2 className="h-4 w-4 text-gray-400 mr-2" />
                          <span className="text-sm text-gray-600 dark:text-gray-400">
                            {visit.project?.project_number || 'N/A'}
                          </span>
                        </div>

                        {/* Client Name */}
                        <div className="flex items-center">
                          <User className="h-4 w-4 text-gray-400 mr-2" />
                          <span className="text-sm text-gray-600 dark:text-gray-400">
                            {visit.project?.client_name || 'N/A'}
                          </span>
                        </div>

                        {/* City */}
                        <div className="flex items-center">
                          <MapPin className="h-4 w-4 text-gray-400 mr-2" />
                          <span className="text-sm text-gray-600 dark:text-gray-400">
                            {visit.project?.city || 'N/A'}
                          </span>
                        </div>

                        {/* Status */}
                        <div>
                          {getStatusBadge(visit.project_status)}
                        </div>
                      </div>

                      {/* Edit Button */}
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleEditVisit(visit)}
                        className="ml-4 text-blue-600 hover:text-blue-800 hover:bg-blue-50 dark:text-blue-400 dark:hover:text-blue-300 dark:hover:bg-blue-900/20"
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                    </div>

                    {/* Notes (if any) */}
                    {visit.notes && (
                      <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          <span className="font-medium">{t('visits.notes') || 'Notes'}:</span> {visit.notes}
                        </p>
                      </div>
                    )}
                  </motion.div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </motion.div>

      {/* نافذة تعديل الزيارة */}
      <VisitModal
        isOpen={showVisitModal}
        onClose={() => {
          setShowVisitModal(false);
          setEditingVisit(null);
          setSelectedProject(null);
        }}
        onSave={handleSaveVisit}
        visit={editingVisit}
        project={selectedProject}
        isEditing={!!editingVisit}
      />
    </motion.div>
  );
};

export default AllVisitsPage;
