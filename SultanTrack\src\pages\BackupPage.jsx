import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import { useToast } from '@/components/ui/use-toast';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Database,
  Download,
  Upload,
  Mail,
  Send,
  Trash2,
  Plus,
  Calendar,
  Clock,
  Cloud,
  FileText,
  History,
  AlertTriangle,
  CheckCircle,
  Loader2
} from 'lucide-react';
import { saveAs } from 'file-saver';
import {
  getAllProjects,
  getAllVisits,
  getAllMaintenanceRequests,
  getAllUsers,
  createBackup as dbCreateBackup,
  getAllBackups as dbGetAllBackups,
  getBackupById as dbGetBackupById,
  deleteBackup as dbDeleteBackup,
  addProject,
  addProjectVisit,
  addMaintenanceRequest,
  addUser
} from '@/lib/supabaseDatabase';

const BackupPage = () => {
  const { t, i18n } = useTranslation();
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [email, setEmail] = useState('');
  const [emails, setEmails] = useState([]);
  const [dataToBackup, setDataToBackup] = useState({
    projects: true,
    visits: true,
    maintenance: true,
    users: false
  });
  const [emailSettings, setEmailSettings] = useState({
    sendBackup: false,
    notifyOnComplete: true,
    notifyOnFail: true
  });
  const [backupFrequency, setBackupFrequency] = useState('weekly');
  const [backupTime, setBackupTime] = useState('12:00');
  const [backups, setBackups] = useState([]);
  const [restoreFile, setRestoreFile] = useState(null);
  const [isConnectedToGoogle, setIsConnectedToGoogle] = useState(false);
  const [confirmDialog, setConfirmDialog] = useState({ open: false, type: '', backupId: null });

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.3
      }
    }
  };

  // Load saved settings and backups from localStorage
  useEffect(() => {
    const savedEmails = localStorage.getItem('backup_emails');
    if (savedEmails) {
      setEmails(JSON.parse(savedEmails));
    }

    const savedSettings = localStorage.getItem('backup_email_settings');
    if (savedSettings) {
      setEmailSettings(JSON.parse(savedSettings));
    }

    const savedDataSettings = localStorage.getItem('backup_data_settings');
    if (savedDataSettings) {
      setDataToBackup(JSON.parse(savedDataSettings));
    }

    const savedFrequency = localStorage.getItem('backup_frequency');
    if (savedFrequency) {
      setBackupFrequency(savedFrequency);
    }

    const savedTime = localStorage.getItem('backup_time');
    if (savedTime) {
      setBackupTime(savedTime);
    }

    // Load existing backups
    loadBackups();
  }, []);

  // Load backups from database
  const loadBackups = async () => {
    try {
      const allBackups = await dbGetAllBackups();
      setBackups(allBackups);
    } catch (error) {
      console.error('Error loading backups:', error);
    }
  };

  // Create a backup with selected data
  const createBackup = async () => {
    try {
      setLoading(true);

      // Save data selection settings
      localStorage.setItem('backup_data_settings', JSON.stringify(dataToBackup));

      // Fetch selected data from Supabase
      const backupData = {
        name: `Backup ${new Date().toLocaleString()}`,
        timestamp: new Date().toISOString(),
        version: '1.0'
      };

      // Fetch data based on user selection
      if (dataToBackup.projects) {
        backupData.projects = await getAllProjects();
      }

      if (dataToBackup.visits) {
        backupData.visits = await getAllVisits();
      }

      if (dataToBackup.maintenance) {
        backupData.maintenanceRequests = await getAllMaintenanceRequests();
      }

      if (dataToBackup.users) {
        // Note: You might want to exclude sensitive user data
        backupData.users = await getAllUsers();
      }

      // Calculate backup size
      const backupJson = JSON.stringify(backupData);
      backupData.size = Math.round((backupJson.length / 1024) * 100) / 100; // Size in KB

      // Save backup to Supabase
      await dbCreateBackup(backupData);

      // Reload backups list
      await loadBackups();

      // If email notifications are enabled, simulate sending emails
      if (emailSettings.notifyOnComplete && emails.length > 0) {
        console.log('Sending notification emails to:', emails);
      }

      // If sending backup to email is enabled, simulate sending backup file
      if (emailSettings.sendBackup && emails.length > 0) {
        console.log('Sending backup file to:', emails);
      }

      toast({
        title: t('backup.backupSuccessTitle') || "Backup Created",
        description: t('backup.backupSuccessMsg') || "Backup has been created successfully.",
        className: "bg-green-500 text-white dark:bg-green-600"
      });
    } catch (error) {
      console.error('Error creating backup:', error);

      // If notification on fail is enabled, simulate sending failure notification
      if (emailSettings.notifyOnFail && emails.length > 0) {
        console.log('Sending failure notification to:', emails);
      }

      toast({
        title: t('backup.errorTitle'),
        description: t('backup.backupErrorMsg'),
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  // Add email to the list
  const addEmail = () => {
    if (!email) return;

    // Simple email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      toast({
        title: t('backup.errorTitle'),
        description: t('backup.invalidEmail'),
        variant: "destructive"
      });
      return;
    }

    if (emails.includes(email)) {
      toast({
        title: t('backup.errorTitle'),
        description: t('backup.invalidEmail'),
        variant: "destructive"
      });
      return;
    }

    const updatedEmails = [...emails, email];
    setEmails(updatedEmails);
    setEmail('');

    // Save to localStorage
    localStorage.setItem('backup_emails', JSON.stringify(updatedEmails));

    toast({
      title: "Success",
      description: t('backup.emailAddedSuccess'),
      className: "bg-green-500 text-white dark:bg-green-600"
    });
  };

  // Remove email from the list
  const removeEmail = (emailToRemove) => {
    const updatedEmails = emails.filter(e => e !== emailToRemove);
    setEmails(updatedEmails);

    // Save to localStorage
    localStorage.setItem('backup_emails', JSON.stringify(updatedEmails));

    toast({
      title: "Success",
      description: t('backup.emailRemovedSuccess'),
      className: "bg-green-500 text-white dark:bg-green-600"
    });
  };

  // Send test email
  const sendTestEmail = () => {
    if (emails.length === 0) {
      toast({
        title: t('backup.errorTitle'),
        description: t('backup.invalidEmail'),
        variant: "destructive"
      });
      return;
    }

    // Simulate sending test email
    console.log('Sending test email to:', emails);

    toast({
      title: "Success",
      description: t('backup.testEmailSent'),
      className: "bg-green-500 text-white dark:bg-green-600"
    });
  };

  // Save email settings
  const saveEmailSettings = () => {
    localStorage.setItem('backup_email_settings', JSON.stringify(emailSettings));

    toast({
      title: "Success",
      description: "Email settings saved successfully",
      className: "bg-green-500 text-white dark:bg-green-600"
    });
  };

  // Download a backup file
  const downloadBackup = async (backupId) => {
    try {
      const backup = await dbGetBackupById(backupId);
      if (!backup) {
        throw new Error('Backup not found');
      }

      // Create a complete backup object for download
      const backupData = {
        projects: backup.projects || [],
        visits: backup.visits || [],
        maintenanceRequests: backup.maintenanceRequests || [],
        timestamp: backup.timestamp || new Date().toISOString(),
        version: backup.version || '1.0',
        name: backup.name || 'Backup',
        created_at: backup.created_at
      };

      const backupJson = JSON.stringify(backupData);
      const blob = new Blob([backupJson], { type: 'application/json' });
      const fileName = `sultantrack_backup_${new Date(backup.created_at).toISOString().split('T')[0]}.json`;

      saveAs(blob, fileName);

      toast({
        title: "Download Started",
        description: "Your backup file is being downloaded.",
        className: "bg-green-500 text-white dark:bg-green-600"
      });
    } catch (error) {
      console.error('Error downloading backup:', error);
      toast({
        title: t('backup.errorTitle'),
        description: error.message,
        variant: "destructive"
      });
    }
  };

  // Handle file selection for restore
  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setRestoreFile(file);
    }
  };

  // Restore from a backup file
  const restoreFromFile = async () => {
    if (!restoreFile) return;

    try {
      setLoading(true);

      const reader = new FileReader();
      reader.onload = async (e) => {
        try {
          const backupData = JSON.parse(e.target.result);

          // Validate backup data structure
          if (!backupData.projects && !backupData.visits && !backupData.maintenanceRequests) {
            throw new Error('Invalid backup file format - no valid data found');
          }

          // Restore projects
          if (backupData.projects && Array.isArray(backupData.projects)) {
            for (const project of backupData.projects) {
              try {
                // Remove the id to let Supabase generate a new one
                const { id, ...projectData } = project;
                await addProject(projectData);
              } catch (error) {
                console.warn('Failed to restore project:', project.project_number, error);
              }
            }
          }

          // Restore visits
          if (backupData.visits && Array.isArray(backupData.visits)) {
            for (const visit of backupData.visits) {
              try {
                const { id, ...visitData } = visit;
                await addProjectVisit(visitData);
              } catch (error) {
                console.warn('Failed to restore visit:', visit.id, error);
              }
            }
          }

          // Restore maintenance requests
          if (backupData.maintenanceRequests && Array.isArray(backupData.maintenanceRequests)) {
            for (const request of backupData.maintenanceRequests) {
              try {
                const { id, ...requestData } = request;
                await addMaintenanceRequest(requestData);
              } catch (error) {
                console.warn('Failed to restore maintenance request:', request.id, error);
              }
            }
          }

          // Restore users (optional)
          if (backupData.users && Array.isArray(backupData.users)) {
            for (const user of backupData.users) {
              try {
                const { id, ...userData } = user;
                await addUser(userData);
              } catch (error) {
                console.warn('Failed to restore user:', user.email, error);
              }
            }
          }

          toast({
            title: t('backup.restoreSuccessTitle'),
            description: t('backup.restoreSuccessMsg'),
            className: "bg-green-500 text-white dark:bg-green-600"
          });

          // Reset file input
          setRestoreFile(null);

          // Close dialog if open
          setConfirmDialog({ open: false, type: '', backupId: null });

        } catch (error) {
          console.error('Error parsing backup file:', error);
          toast({
            title: t('backup.errorTitle'),
            description: t('backup.restoreErrorMsg'),
            variant: "destructive"
          });
        } finally {
          setLoading(false);
        }
      };

      reader.readAsText(restoreFile);

    } catch (error) {
      console.error('Error restoring from file:', error);
      toast({
        title: t('backup.errorTitle'),
        description: t('backup.restoreErrorMsg'),
        variant: "destructive"
      });
      setLoading(false);
    }
  };

  // Restore from a stored backup
  const restoreBackup = async (backupId) => {
    try {
      setLoading(true);

      const backup = await dbGetBackupById(backupId);
      if (!backup) {
        throw new Error('Backup not found');
      }

      // Restore projects
      if (backup.projects && Array.isArray(backup.projects)) {
        for (const project of backup.projects) {
          try {
            const { id, ...projectData } = project;
            await addProject(projectData);
          } catch (error) {
            console.warn('Failed to restore project:', project.project_number, error);
          }
        }
      }

      // Restore visits
      if (backup.visits && Array.isArray(backup.visits)) {
        for (const visit of backup.visits) {
          try {
            const { id, ...visitData } = visit;
            await addProjectVisit(visitData);
          } catch (error) {
            console.warn('Failed to restore visit:', visit.id, error);
          }
        }
      }

      // Restore maintenance requests
      if (backup.maintenanceRequests && Array.isArray(backup.maintenanceRequests)) {
        for (const request of backup.maintenanceRequests) {
          try {
            const { id, ...requestData } = request;
            await addMaintenanceRequest(requestData);
          } catch (error) {
            console.warn('Failed to restore maintenance request:', request.id, error);
          }
        }
      }

      // Restore users (optional)
      if (backup.users && Array.isArray(backup.users)) {
        for (const user of backup.users) {
          try {
            const { id, ...userData } = user;
            await addUser(userData);
          } catch (error) {
            console.warn('Failed to restore user:', user.email, error);
          }
        }
      }

      toast({
        title: t('backup.restoreSuccessTitle'),
        description: t('backup.restoreSuccessMsg'),
        className: "bg-green-500 text-white dark:bg-green-600"
      });

      // Close dialog
      setConfirmDialog({ open: false, type: '', backupId: null });

    } catch (error) {
      console.error('Error restoring backup:', error);
      toast({
        title: t('backup.errorTitle'),
        description: t('backup.restoreErrorMsg'),
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  // Delete a backup
  const deleteBackup = async (backupId) => {
    try {
      setLoading(true);

      // Delete from database
      await dbDeleteBackup(backupId);

      // Update backups list
      const updatedBackups = await dbGetAllBackups();
      setBackups(updatedBackups);

      toast({
        title: t('backup.deleteSuccessTitle'),
        description: t('backup.deleteSuccessMsg'),
        className: "bg-green-500 text-white dark:bg-green-600"
      });

      // Close dialog
      setConfirmDialog({ open: false, type: '', backupId: null });

    } catch (error) {
      console.error('Error deleting backup:', error);
      toast({
        title: t('backup.errorTitle'),
        description: t('backup.deleteErrorMsg'),
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  // Connect to Google Drive
  const connectToGoogleDrive = () => {
    // This would be implemented with Google Drive API
    // For now, we'll just simulate the connection
    setIsConnectedToGoogle(true);

    toast({
      title: t('backup.googleConnectSuccessTitle'),
      description: t('backup.googleConnectSuccessMsg'),
      className: "bg-green-500 text-white dark:bg-green-600"
    });
  };

  // Disconnect from Google Drive
  const disconnectFromGoogleDrive = () => {
    setIsConnectedToGoogle(false);

    toast({
      title: "Disconnected",
      description: "Your account has been disconnected from Google Drive.",
      className: "bg-blue-500 text-white"
    });
  };

  // Save backup schedule
  const saveBackupSchedule = () => {
    // This would be implemented with a scheduling mechanism
    // For now, we'll just save the preferences
    localStorage.setItem('backup_frequency', backupFrequency);
    localStorage.setItem('backup_time', backupTime);

    toast({
      title: t('backup.scheduleSuccessTitle'),
      description: t('backup.scheduleSuccessMsg'),
      className: "bg-green-500 text-white dark:bg-green-600"
    });
  };

  return (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={containerVariants}
      className="p-4 md:p-6"
      dir={i18n.dir()}
    >
      <motion.div variants={itemVariants} className="flex items-center mb-6">
        <Database className="h-6 w-6 mr-3 text-blue-500" />
        <h1 className="text-2xl font-bold text-gray-800 dark:text-white">
          {t('backup.title')}
        </h1>
      </motion.div>

      <motion.p variants={itemVariants} className="text-gray-600 dark:text-gray-300 mb-8">
        {t('backup.description')}
      </motion.p>

      <Tabs defaultValue="manual" className="w-full">
        <TabsList className="grid w-full grid-cols-4 mb-8">
          <TabsTrigger value="manual">{t('backup.manualBackup')}</TabsTrigger>
          <TabsTrigger value="restore">{t('backup.restoreBackup')}</TabsTrigger>
          <TabsTrigger value="email">{t('backup.emailNotifications')}</TabsTrigger>
          <TabsTrigger value="google">{t('backup.googleDriveBackup')}</TabsTrigger>
        </TabsList>

        <TabsContent value="manual">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <motion.div variants={itemVariants}>
              <Card className="shadow-lg dark:bg-gray-800/80 border-gray-200 dark:border-gray-700/50 backdrop-blur-md">
                <CardHeader>
                  <CardTitle className="text-xl text-gray-800 dark:text-white">
                    {t('backup.selectDataTitle')}
                  </CardTitle>
                  <CardDescription>
                    {t('backup.selectDataDescription')}
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="projects"
                      checked={dataToBackup.projects}
                      onCheckedChange={(checked) => setDataToBackup({...dataToBackup, projects: checked})}
                    />
                    <Label htmlFor="projects">{t('backup.projectsData')}</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="visits"
                      checked={dataToBackup.visits}
                      onCheckedChange={(checked) => setDataToBackup({...dataToBackup, visits: checked})}
                    />
                    <Label htmlFor="visits">{t('backup.visitsData')}</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="maintenance"
                      checked={dataToBackup.maintenance}
                      onCheckedChange={(checked) => setDataToBackup({...dataToBackup, maintenance: checked})}
                    />
                    <Label htmlFor="maintenance">{t('backup.maintenanceData')}</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="users"
                      checked={dataToBackup.users}
                      onCheckedChange={(checked) => setDataToBackup({...dataToBackup, users: checked})}
                    />
                    <Label htmlFor="users">{t('backup.usersData')}</Label>
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div variants={itemVariants}>
              <Card className="shadow-lg dark:bg-gray-800/80 border-gray-200 dark:border-gray-700/50 backdrop-blur-md">
                <CardHeader>
                  <CardTitle className="text-xl text-gray-800 dark:text-white">
                    {t('backup.createBackup')}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <Button
                    onClick={createBackup}
                    disabled={loading || (!dataToBackup.projects && !dataToBackup.visits && !dataToBackup.maintenance && !dataToBackup.users)}
                    className="w-full bg-blue-500 hover:bg-blue-600 text-white"
                  >
                    {loading ? (
                      <div className="animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-white"></div>
                    ) : (
                      <>
                        <Download className="h-5 w-5 mr-2" />
                        {t('backup.createBackup')}
                      </>
                    )}
                  </Button>
                </CardContent>
              </Card>
            </motion.div>
          </div>

          {/* قسم عرض النسخ الاحتياطية الموجودة */}
          <motion.div variants={itemVariants} className="mt-8">
            <Card className="shadow-lg dark:bg-gray-800/80 border-gray-200 dark:border-gray-700/50 backdrop-blur-md">
              <CardHeader>
                <CardTitle className="text-xl text-gray-800 dark:text-white flex items-center">
                  <History className="h-5 w-5 mr-2" />
                  {t('backup.existingBackups') || "Existing Backups"}
                </CardTitle>
                <CardDescription>
                  {t('backup.existingBackupsDescription') || "View and manage your existing backups"}
                </CardDescription>
              </CardHeader>
              <CardContent>
                {backups.length === 0 ? (
                  <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                    <Database className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>{t('backup.noBackupsFound') || "No backups found"}</p>
                  </div>
                ) : (
                  <div className="overflow-x-auto">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>{t('backup.backupName') || "Name"}</TableHead>
                          <TableHead>{t('backup.backupDate') || "Date"}</TableHead>
                          <TableHead>{t('backup.backupSize') || "Size"}</TableHead>
                          <TableHead>{t('backup.actions') || "Actions"}</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {backups.map((backup) => (
                          <TableRow key={backup.id}>
                            <TableCell className="font-medium">{backup.name}</TableCell>
                            <TableCell>
                              {new Date(backup.created_at).toLocaleDateString(i18n.language === 'ar' ? 'ar-SA' : 'en-US')}
                            </TableCell>
                            <TableCell>
                              <Badge variant="secondary">
                                {backup.size ? `${backup.size} KB` : 'N/A'}
                              </Badge>
                            </TableCell>
                            <TableCell>
                              <div className="flex space-x-2">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => downloadBackup(backup.id)}
                                  className="text-blue-600 hover:text-blue-800"
                                >
                                  <Download className="h-4 w-4 mr-1" />
                                  {t('backup.download') || "Download"}
                                </Button>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => setConfirmDialog({ open: true, type: 'restore', backupId: backup.id })}
                                  className="text-green-600 hover:text-green-800"
                                >
                                  <Upload className="h-4 w-4 mr-1" />
                                  {t('backup.restore') || "Restore"}
                                </Button>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => setConfirmDialog({ open: true, type: 'delete', backupId: backup.id })}
                                  className="text-red-600 hover:text-red-800"
                                >
                                  <Trash2 className="h-4 w-4 mr-1" />
                                  {t('backup.delete') || "Delete"}
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                )}
              </CardContent>
            </Card>
          </motion.div>
        </TabsContent>

        <TabsContent value="restore">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <motion.div variants={itemVariants}>
              <Card className="shadow-lg dark:bg-gray-800/80 border-gray-200 dark:border-gray-700/50 backdrop-blur-md">
                <CardHeader>
                  <CardTitle className="text-xl text-gray-800 dark:text-white">
                    {t('backup.restoreFromFile') || "Restore from File"}
                  </CardTitle>
                  <CardDescription>
                    {t('backup.restoreFromFileDescription') || "Upload a backup file to restore your data"}
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="backup-file">{t('backup.selectFile') || "Select Backup File"}</Label>
                    <Input
                      id="backup-file"
                      type="file"
                      accept=".json"
                      onChange={handleFileChange}
                      className="mt-1"
                    />
                  </div>
                  {restoreFile && (
                    <div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                      <p className="text-sm text-blue-700 dark:text-blue-300">
                        <FileText className="h-4 w-4 inline mr-1" />
                        {restoreFile.name}
                      </p>
                    </div>
                  )}
                  <Button
                    onClick={restoreFromFile}
                    disabled={!restoreFile || loading}
                    className="w-full bg-green-500 hover:bg-green-600 text-white"
                  >
                    {loading ? (
                      <Loader2 className="h-5 w-5 mr-2 animate-spin" />
                    ) : (
                      <Upload className="h-5 w-5 mr-2" />
                    )}
                    {t('backup.uploadFile') || "Upload & Restore"}
                  </Button>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div variants={itemVariants}>
              <Card className="shadow-lg dark:bg-gray-800/80 border-gray-200 dark:border-gray-700/50 backdrop-blur-md">
                <CardHeader>
                  <CardTitle className="text-xl text-gray-800 dark:text-white">
                    {t('backup.restoreInstructions') || "Restore Instructions"}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    <div className="flex items-start space-x-3">
                      <div className="flex-shrink-0 w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold">
                        1
                      </div>
                      <p className="text-sm text-gray-600 dark:text-gray-300">
                        {t('backup.step1') || "Select a backup file (.json) from your computer"}
                      </p>
                    </div>
                    <div className="flex items-start space-x-3">
                      <div className="flex-shrink-0 w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold">
                        2
                      </div>
                      <p className="text-sm text-gray-600 dark:text-gray-300">
                        {t('backup.step2') || "Click 'Upload & Restore' to begin the restoration process"}
                      </p>
                    </div>
                    <div className="flex items-start space-x-3">
                      <div className="flex-shrink-0 w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold">
                        3
                      </div>
                      <p className="text-sm text-gray-600 dark:text-gray-300">
                        {t('backup.step3') || "Wait for the restoration to complete"}
                      </p>
                    </div>
                  </div>
                  <div className="mt-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                    <div className="flex items-center">
                      <AlertTriangle className="h-5 w-5 text-yellow-600 dark:text-yellow-400 mr-2" />
                      <p className="text-sm text-yellow-700 dark:text-yellow-300 font-medium">
                        {t('backup.warning') || "Warning"}
                      </p>
                    </div>
                    <p className="text-sm text-yellow-600 dark:text-yellow-400 mt-1">
                      {t('backup.restoreWarning') || "Restoring will replace all current data. Make sure to create a backup first."}
                    </p>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </TabsContent>

        <TabsContent value="email">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <motion.div variants={itemVariants}>
              <Card className="shadow-lg dark:bg-gray-800/80 border-gray-200 dark:border-gray-700/50 backdrop-blur-md">
                <CardHeader>
                  <CardTitle className="text-xl text-gray-800 dark:text-white">
                    {t('backup.emailNotifications')}
                  </CardTitle>
                  <CardDescription>
                    {t('backup.emailNotificationsDescription')}
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex space-x-2">
                    <Input
                      placeholder={t('backup.emailAddress')}
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      className="flex-1"
                    />
                    <Button onClick={addEmail}>
                      <Plus className="h-4 w-4 mr-2" />
                      {t('backup.addEmail')}
                    </Button>
                  </div>

                  {emails.length > 0 && (
                    <div className="space-y-2 mt-4">
                      <Label>{t('backup.emailAddress')}</Label>
                      <div className="space-y-2">
                        {emails.map((e, index) => (
                          <div key={index} className="flex justify-between items-center p-2 bg-gray-100 dark:bg-gray-800 rounded">
                            <span>{e}</span>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => removeEmail(e)}
                              className="text-red-500 hover:text-red-700"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        ))}
                      </div>
                      <Button
                        variant="outline"
                        className="mt-2"
                        onClick={sendTestEmail}
                      >
                        <Send className="h-4 w-4 mr-2" />
                        {t('backup.sendTestEmail')}
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>
            </motion.div>

            <motion.div variants={itemVariants}>
              <Card className="shadow-lg dark:bg-gray-800/80 border-gray-200 dark:border-gray-700/50 backdrop-blur-md">
                <CardHeader>
                  <CardTitle className="text-xl text-gray-800 dark:text-white">
                    {t('backup.emailSettings')}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="sendBackup"
                      checked={emailSettings.sendBackup}
                      onCheckedChange={(checked) => setEmailSettings({...emailSettings, sendBackup: checked})}
                      disabled={emails.length === 0}
                    />
                    <Label htmlFor="sendBackup">{t('backup.sendBackupToEmail')}</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="notifyComplete"
                      checked={emailSettings.notifyOnComplete}
                      onCheckedChange={(checked) => setEmailSettings({...emailSettings, notifyOnComplete: checked})}
                      disabled={emails.length === 0}
                    />
                    <Label htmlFor="notifyComplete">{t('backup.notifyOnBackupComplete')}</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="notifyFail"
                      checked={emailSettings.notifyOnFail}
                      onCheckedChange={(checked) => setEmailSettings({...emailSettings, notifyOnFail: checked})}
                      disabled={emails.length === 0}
                    />
                    <Label htmlFor="notifyFail">{t('backup.notifyOnBackupFail')}</Label>
                  </div>

                  <Button
                    onClick={saveEmailSettings}
                    className="w-full mt-4 bg-green-500 hover:bg-green-600 text-white"
                    disabled={emails.length === 0}
                  >
                    <Mail className="h-5 w-5 mr-2" />
                    {t('backup.saveSchedule')}
                  </Button>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </TabsContent>

        <TabsContent value="google">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <motion.div variants={itemVariants}>
              <Card className="shadow-lg dark:bg-gray-800/80 border-gray-200 dark:border-gray-700/50 backdrop-blur-md">
                <CardHeader>
                  <CardTitle className="text-xl text-gray-800 dark:text-white">
                    {t('backup.connectGoogleDrive')}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {!isConnectedToGoogle ? (
                    <Button
                      onClick={connectToGoogleDrive}
                      className="w-full bg-blue-500 hover:bg-blue-600 text-white"
                    >
                      <Cloud className="h-5 w-5 mr-2" />
                      {t('backup.connectGoogleDrive')}
                    </Button>
                  ) : (
                    <div className="space-y-4">
                      <div className="flex items-center justify-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                        <CheckCircle className="h-5 w-5 text-green-600 dark:text-green-400 mr-2" />
                        <span className="text-green-700 dark:text-green-300">
                          {t('backup.connectedToGoogleDrive') || "Connected to Google Drive"}
                        </span>
                      </div>
                      <Button
                        onClick={disconnectFromGoogleDrive}
                        variant="outline"
                        className="w-full border-red-500 text-red-500 hover:bg-red-500 hover:text-white"
                      >
                        <Cloud className="h-5 w-5 mr-2" />
                        {t('backup.disconnectGoogleDrive')}
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>
            </motion.div>

            <motion.div variants={itemVariants}>
              <Card className="shadow-lg dark:bg-gray-800/80 border-gray-200 dark:border-gray-700/50 backdrop-blur-md">
                <CardHeader>
                  <CardTitle className="text-xl text-gray-800 dark:text-white">
                    {t('backup.scheduleBackup')}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="frequency">{t('backup.frequency')}</Label>
                    <Select
                      value={backupFrequency}
                      onValueChange={setBackupFrequency}
                    >
                      <SelectTrigger id="frequency">
                        <SelectValue placeholder={t('backup.frequency')} />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="daily">{t('backup.daily')}</SelectItem>
                        <SelectItem value="weekly">{t('backup.weekly')}</SelectItem>
                        <SelectItem value="monthly">{t('backup.monthly')}</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="time">{t('backup.backupTime')}</Label>
                    <Input
                      id="time"
                      type="time"
                      value={backupTime}
                      onChange={(e) => setBackupTime(e.target.value)}
                    />
                  </div>

                  <Button
                    onClick={saveBackupSchedule}
                    className="w-full bg-green-500 hover:bg-green-600 text-white"
                  >
                    <Calendar className="h-5 w-5 mr-2" />
                    {t('backup.saveSchedule')}
                  </Button>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </TabsContent>
      </Tabs>

      {/* نوافذ التأكيد */}
      <AlertDialog open={confirmDialog.open} onOpenChange={(open) => setConfirmDialog({ ...confirmDialog, open })}>
        <AlertDialogContent className="dark:bg-gray-800 dark:text-white">
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center">
              {confirmDialog.type === 'delete' && (
                <>
                  <Trash2 className="text-red-500 mr-2" />
                  {t('backup.confirmDeleteTitle') || "Confirm Delete"}
                </>
              )}
              {confirmDialog.type === 'restore' && (
                <>
                  <Upload className="text-green-500 mr-2" />
                  {t('backup.confirmRestoreTitle') || "Confirm Restore"}
                </>
              )}
            </AlertDialogTitle>
            <AlertDialogDescription className="dark:text-gray-300">
              {confirmDialog.type === 'delete' && (
                t('backup.confirmDeleteMsg') || "Are you sure you want to delete this backup? This action cannot be undone."
              )}
              {confirmDialog.type === 'restore' && (
                t('backup.confirmRestoreMsg') || "Are you sure you want to restore this backup? This will replace all current data."
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel className="dark:bg-gray-700 dark:hover:bg-gray-600 dark:border-gray-600">
              {t('backup.cancel') || "Cancel"}
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={() => {
                if (confirmDialog.type === 'delete') {
                  deleteBackup(confirmDialog.backupId);
                } else if (confirmDialog.type === 'restore') {
                  restoreBackup(confirmDialog.backupId);
                }
              }}
              className={`${
                confirmDialog.type === 'delete'
                  ? 'bg-red-600 hover:bg-red-700 dark:bg-red-500 dark:hover:bg-red-600'
                  : 'bg-green-600 hover:bg-green-700 dark:bg-green-500 dark:hover:bg-green-600'
              } text-white`}
            >
              {loading ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : confirmDialog.type === 'delete' ? (
                <Trash2 className="h-4 w-4 mr-2" />
              ) : (
                <Upload className="h-4 w-4 mr-2" />
              )}
              {confirmDialog.type === 'delete'
                ? (t('backup.delete') || "Delete")
                : (t('backup.restore') || "Restore")
              }
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </motion.div>
  );
};

export default BackupPage;
