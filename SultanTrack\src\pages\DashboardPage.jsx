
import React, { useState, useEffect, use<PERSON>allback } from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Activity, AlertTriangle, CheckCircle2, Clock } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useToast } from '@/components/ui/use-toast';
import { getAllProjects } from '@/lib/supabaseDatabase';
import { getDashboardAnalytics, getMonthlyTrends } from '@/lib/analyticsService';
import { Wrench, Package, StopCircle, AlertCircle, TrendingUp, Plus, FileText, Settings } from 'lucide-react';
import AddVisitModal from '@/components/AddVisitModal';
import DashboardFilters from '@/components/DashboardFilters';
import { useNavigate } from 'react-router-dom';
import {
  <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid, Toolt<PERSON>, Legend, ResponsiveContainer, LineChart, Line
} from 'recharts';
import { applyFilters, getChartColors, getPieChartColors } from '@/lib/filterService';

const DashboardPage = () => {
  const { t, i18n } = useTranslation();
  const { toast } = useToast();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [timeFilter, setTimeFilter] = useState('month'); // 'week', 'month', 'year'
  const [showAddVisitModal, setShowAddVisitModal] = useState(false);
  const [currentFilters, setCurrentFilters] = useState({
    timeRange: 'month',
    startDate: '',
    endDate: '',
    city: 'all',
    projectStatus: 'all',
    materialType: 'all'
  });
  const [filteredData, setFilteredData] = useState(null);
  const [projectStats, setProjectStats] = useState({
    total: 0,
    notStarted: 0,
    inProgress: 0,
    delayed: 0,
    completed: 0
  });
  const [quantitiesData, setQuantitiesData] = useState([]);

  // إحصائيات جديدة
  const [maintenanceStats, setMaintenanceStats] = useState({
    total: 0,
    completed: 0,
    pending: 0,
    period: { total: 0, completed: 0, pending: 0 }
  });
  const [materialStats, setMaterialStats] = useState({
    stoppedDueToShortage: 0,
    partialShortage: 0,
    ongoingProjects: 0
  });
  const [monthlyTrends, setMonthlyTrends] = useState([]);

  // Test Supabase connection
  const testSupabaseConnection = async () => {
    try {
      console.log('Testing Supabase connection...');
      const projects = await getAllProjects();
      console.log('Supabase connection test result:', projects);
      console.log('Number of projects found:', projects?.length || 0);

      if (projects && projects.length > 0) {
        console.log('First project:', projects[0]);
        console.log('First project materials:', projects[0].materials);
      }

      toast({
        title: "Connection Test",
        description: `Found ${projects?.length || 0} projects in Supabase`,
        variant: "default"
      });

      return projects;
    } catch (error) {
      console.error('Supabase connection test failed:', error);
      toast({
        title: "Connection Test Failed",
        description: error.message,
        variant: "destructive"
      });
      throw error;
    }
  };

  // حساب الخامات المركبة من الزيارات
  const calculateInstalledMaterials = async () => {
    try {
      // جلب جميع الزيارات
      const { getAllVisits } = await import('../lib/visitsService');
      const allVisits = await getAllVisits();

      // حساب إجمالي الخامات المركبة
      const installedTotals = {
        pvc: 0,
        shutter: 0,
        sgs: 0,
        doors: 0,
        glass: 0
      };

      allVisits.forEach(visit => {
        if (visit.materials_installed) {
          installedTotals.pvc += visit.materials_installed.quantity_pvc || 0;
          installedTotals.shutter += visit.materials_installed.quantity_shutter || 0;
          installedTotals.sgs += visit.materials_installed.quantity_sgs || 0;
          installedTotals.doors += visit.materials_installed.quantity_doors || 0;
          installedTotals.glass += visit.materials_installed.quantity_glass || 0;
        }
      });

      return [
        { name: t('dashboard.pvc'), value: installedTotals.pvc, unit: "units" },
        { name: t('dashboard.shutter'), value: installedTotals.shutter, unit: "units" },
        { name: t('dashboard.sgs'), value: installedTotals.sgs, unit: "units" },
        { name: t('dashboard.doors'), value: installedTotals.doors, unit: "units" },
        { name: t('dashboard.glass'), value: installedTotals.glass, unit: "units" }
      ];
    } catch (error) {
      console.error('Error calculating installed materials:', error);
      return [
        { name: t('dashboard.pvc'), value: 0, unit: "units" },
        { name: t('dashboard.shutter'), value: 0, unit: "units" },
        { name: t('dashboard.sgs'), value: 0, unit: "units" },
        { name: t('dashboard.doors'), value: 0, unit: "units" },
        { name: t('dashboard.glass'), value: 0, unit: "units" }
      ];
    }
  };

  // جلب جميع الإحصائيات
  const fetchDashboardData = useCallback(async () => {
    setLoading(true);
    try {
      console.log('Dashboard: Fetching analytics data...');

      // جلب الإحصائيات الشاملة
      const currentLanguage = i18n.language || 'ar';
      const analytics = await getDashboardAnalytics(timeFilter, currentLanguage);
      console.log('Dashboard: Analytics data:', analytics);

      // جلب الاتجاهات الشهرية
      const trends = await getMonthlyTrends(currentLanguage);
      console.log('Dashboard: Monthly trends:', trends);

      // تحديث إحصائيات المشاريع
      setProjectStats({
        total: analytics.projects.total,
        notStarted: analytics.projects.pending,
        inProgress: analytics.projects.inProgress,
        delayed: 0, // يمكن إضافة هذا لاحقاً
        completed: analytics.projects.completed
      });

      // تحديث إحصائيات الصيانة
      setMaintenanceStats(analytics.maintenance);

      // تحديث إحصائيات المواد
      setMaterialStats(analytics.materials);

      // تحديث الاتجاهات الشهرية
      setMonthlyTrends(trends);

      // جلب المشاريع للكميات (الكود الأصلي)
      // تحديث بيانات الكميات من الزيارات (الخامات المركبة)
      const installedMaterials = await calculateInstalledMaterials();
      console.log('Dashboard: Installed materials from visits:', installedMaterials);
      setQuantitiesData(installedMaterials);

    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      toast({
        title: "خطأ في تحميل البيانات",
        description: `فشل في تحميل بيانات لوحة التحكم: ${error.message}`,
        variant: "destructive"
      });

      // في حالة الخطأ، اعرض قيم افتراضية
      setProjectStats({
        total: 0,
        notStarted: 0,
        inProgress: 0,
        delayed: 0,
        completed: 0
      });
      setMaintenanceStats({
        total: 0,
        completed: 0,
        pending: 0,
        period: { total: 0, completed: 0, pending: 0 }
      });
      setMaterialStats({
        stoppedDueToShortage: 0,
        partialShortage: 0,
        ongoingProjects: 0
      });
      setQuantitiesData([
        { name: t('dashboard.pvc'), value: 0, unit: "units" },
        { name: t('dashboard.shutter'), value: 0, unit: "units" },
        { name: t('dashboard.sgs'), value: 0, unit: "units" },
        { name: t('dashboard.doors'), value: 0, unit: "units" },
        { name: t('dashboard.glass'), value: 0, unit: "units" }
      ]);
    } finally {
      setLoading(false);
    }
  }, [t, toast, timeFilter, i18n.language]);

  useEffect(() => {
    console.log('Dashboard useEffect triggered, timeFilter:', timeFilter);
    fetchDashboardData();
  }, [fetchDashboardData, timeFilter]);

  // دوال معالجة الإجراءات السريعة
  const handleAddProject = () => {
    navigate('/add-project');
  };

  const handleAddMaintenanceRequest = () => {
    navigate('/warranty-maintenance');
  };

  const handleAddVisit = () => {
    setShowAddVisitModal(true);
  };

  const handleVisitSuccess = () => {
    // تحديث البيانات بعد إضافة زيارة جديدة
    fetchDashboardData();
  };

  // معالجة تغيير الفلاتر
  const handleFiltersChange = async (newFilters) => {
    setLoading(true);
    setCurrentFilters(newFilters);

    try {
      console.log('Applying new filters:', newFilters);
      const filtered = await applyFilters(newFilters);
      setFilteredData(filtered);

      // تحديث الإحصائيات بناءً على البيانات المفلترة
      setProjectStats({
        total: filtered.analytics.projects.total,
        notStarted: filtered.analytics.projects.pending,
        inProgress: filtered.analytics.projects.inProgress,
        delayed: 0,
        completed: filtered.analytics.projects.completed
      });

      setMaintenanceStats({
        total: filtered.analytics.maintenance.total,
        completed: filtered.analytics.maintenance.completed,
        pending: filtered.analytics.maintenance.pending,
        period: {
          total: filtered.analytics.maintenance.total,
          completed: filtered.analytics.maintenance.completed,
          pending: filtered.analytics.maintenance.pending
        }
      });

      setMaterialStats(filtered.analytics.materials);

      // تحديث بيانات الكميات
      const quantitiesData = filtered.analytics.quantities.map(item => ({
        name: t(`dashboard.${item.name}`),
        value: item.value,
        unit: item.unit
      }));
      setQuantitiesData(quantitiesData);

      toast({
        title: t('dashboard.filters.apply'),
        description: `تم تطبيق الفلاتر بنجاح. تم العثور على ${filtered.projects.length} مشروع و ${filtered.maintenance.length} طلب صيانة.`,
        className: "bg-blue-500 text-white dark:bg-blue-600",
        duration: 3000
      });

    } catch (error) {
      console.error('Error applying filters:', error);
      toast({
        title: "خطأ في تطبيق الفلاتر",
        description: error.message,
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  // Prepare data for charts with improved colors
  const colors = getChartColors();

  const pieChartData = [
    { name: t('dashboard.notStarted'), value: projectStats.notStarted, color: colors.warning },
    { name: t('dashboard.inProgress'), value: projectStats.inProgress, color: colors.primary },
    { name: t('dashboard.delayed'), value: projectStats.delayed, color: colors.danger },
    { name: t('dashboard.completed'), value: projectStats.completed, color: colors.secondary }
  ];

  const barChartData = quantitiesData.map((item, index) => ({
    name: item.name,
    value: item.value,
    fill: colors.gradient[index % colors.gradient.length]
  }));

  // Stats cards data - إحصائيات المشاريع
  const projectStatsCards = [
    {
      title: t('dashboard.totalProjects'),
      value: projectStats.total,
      icon: <Activity className="h-6 w-6 text-blue-500" />,
      color: "blue"
    },
    {
      title: t('dashboard.completed'),
      value: projectStats.completed,
      icon: <CheckCircle2 className="h-6 w-6 text-green-500" />,
      color: "green"
    },
    {
      title: t('dashboard.inProgress'),
      value: projectStats.inProgress,
      icon: <Activity className="h-6 w-6 text-yellow-500" />,
      color: "yellow"
    },
    {
      title: timeFilter === 'week' ? t('dashboard.analytics.completedDuringWeek') :
             timeFilter === 'month' ? t('dashboard.analytics.completedDuringMonth') :
             t('dashboard.analytics.completedDuringYear'),
      value: projectStats.completed, // يمكن تحديث هذا لاحقاً
      icon: <TrendingUp className="h-6 w-6 text-purple-500" />,
      color: "purple"
    }
  ];

  // إحصائيات الصيانة
  const maintenanceStatsCards = [
    {
      title: t('dashboard.analytics.totalMaintenanceRequests'),
      value: maintenanceStats.total,
      icon: <Wrench className="h-6 w-6 text-blue-500" />,
      color: "blue"
    },
    {
      title: t('dashboard.analytics.completedMaintenance'),
      value: maintenanceStats.completed,
      icon: <CheckCircle2 className="h-6 w-6 text-green-500" />,
      color: "green"
    },
    {
      title: t('dashboard.analytics.pendingMaintenance'),
      value: maintenanceStats.pending,
      icon: <Clock className="h-6 w-6 text-orange-500" />,
      color: "orange"
    },
    {
      title: timeFilter === 'week' ? t('dashboard.analytics.completedDuringWeek') :
             timeFilter === 'month' ? t('dashboard.analytics.completedDuringMonth') :
             t('dashboard.analytics.completedDuringYear'),
      value: maintenanceStats.period.completed,
      icon: <TrendingUp className="h-6 w-6 text-purple-500" />,
      color: "purple"
    }
  ];

  // إحصائيات نقص المواد
  const materialStatsCards = [
    {
      title: t('dashboard.analytics.stoppedDueToShortage'),
      value: materialStats.stoppedDueToShortage,
      icon: <StopCircle className="h-6 w-6 text-red-500" />,
      color: "red"
    },
    {
      title: t('dashboard.analytics.partialShortage'),
      value: materialStats.partialShortage,
      icon: <AlertCircle className="h-6 w-6 text-yellow-500" />,
      color: "yellow"
    },
    {
      title: t('dashboard.analytics.ongoingProjects'),
      value: materialStats.ongoingProjects,
      icon: <Package className="h-6 w-6 text-green-500" />,
      color: "green"
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: 'spring',
        stiffness: 100,
      },
    },
  };

  return (
    <motion.div
      className="p-2 sm:p-4 lg:p-6 space-y-4 sm:space-y-6"
      initial="hidden"
      animate="visible"
      variants={containerVariants}
      dir={i18n.dir()}
    >
      <motion.div variants={itemVariants} className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3 sm:gap-0">
        <div className="flex flex-col">
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-800 dark:text-white">{t('dashboard.title')}</h1>
          {filteredData && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              className="flex flex-wrap items-center gap-2 mt-2"
            >
              <div className="px-3 py-1 bg-gradient-to-r from-blue-100 to-indigo-100 dark:from-blue-900 dark:to-indigo-900 text-blue-800 dark:text-blue-200 rounded-full text-sm font-medium shadow-sm">
                📊 البيانات مفلترة: {filteredData.projects.length} مشروع، {filteredData.maintenance.length} طلب صيانة
              </div>
              {/* مؤشرات الفلاتر النشطة */}
              {currentFilters.city !== 'all' && (
                <span className="px-2 py-1 bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 rounded-full text-xs">
                  🏙️ {currentFilters.city}
                </span>
              )}
              {currentFilters.projectStatus !== 'all' && (
                <span className="px-2 py-1 bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 rounded-full text-xs">
                  📋 {t(`dashboard.filters.${currentFilters.projectStatus}`)}
                </span>
              )}
              {currentFilters.materialType !== 'all' && (
                <span className="px-2 py-1 bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200 rounded-full text-xs">
                  📦 {t(`dashboard.${currentFilters.materialType}`)}
                </span>
              )}
            </motion.div>
          )}
        </div>
        <div className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto">
          <Button
            onClick={fetchDashboardData}
            variant="outline"
            size="sm"
            disabled={loading}
            className="flex items-center gap-2 w-full sm:w-auto"
          >
            {loading ? (
              <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-blue-500"></div>
            ) : (
              <Activity className="h-4 w-4" />
            )}
            {t('dashboard.refresh') || 'تحديث'}
          </Button>
          <Button
            onClick={testSupabaseConnection}
            variant="outline"
            size="sm"
            className="flex items-center gap-2 w-full sm:w-auto"
          >
            Test Connection
          </Button>
        </div>
      </motion.div>

      {/* قسم الإجراءات السريعة */}
      <motion.div variants={itemVariants}>
        <Card className="shadow-lg dark:bg-gray-800 mb-6">
          <CardHeader>
            <CardTitle className="text-xl font-semibold text-gray-700 dark:text-white flex items-center gap-2">
              <Settings className="h-5 w-5" />
              {t('dashboard.quickActions.title')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4">
              {/* إضافة مشروع جديد */}
              <motion.div
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Button
                  className="w-full h-auto p-4 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-300"
                  onClick={handleAddProject}
                >
                  <div className="flex items-center gap-3 w-full">
                    <div className="p-2 bg-white/20 rounded-lg flex-shrink-0">
                      <Plus className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
                    </div>
                    <div className="min-w-0 flex-1 text-left">
                      <h3 className="font-semibold text-white text-sm sm:text-base">
                        {t('dashboard.quickActions.addProject')}
                      </h3>
                      <p className="text-xs sm:text-sm text-blue-100 truncate">
                        {t('dashboard.quickActions.addProjectDesc')}
                      </p>
                    </div>
                  </div>
                </Button>
              </motion.div>

              {/* إضافة طلب صيانة */}
              <motion.div
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Button
                  className="w-full h-auto p-4 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-300"
                  onClick={handleAddMaintenanceRequest}
                >
                  <div className="flex items-center gap-3 w-full">
                    <div className="p-2 bg-white/20 rounded-lg flex-shrink-0">
                      <Wrench className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
                    </div>
                    <div className="min-w-0 flex-1 text-left">
                      <h3 className="font-semibold text-white text-sm sm:text-base">
                        {t('dashboard.quickActions.addMaintenanceRequest')}
                      </h3>
                      <p className="text-xs sm:text-sm text-green-100 truncate">
                        {t('dashboard.quickActions.addMaintenanceDesc')}
                      </p>
                    </div>
                  </div>
                </Button>
              </motion.div>

              {/* إضافة زيارة جديدة */}
              <motion.div
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Button
                  className="w-full h-auto p-4 bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-300"
                  onClick={handleAddVisit}
                >
                  <div className="flex items-center gap-3 w-full">
                    <div className="p-2 bg-white/20 rounded-lg flex-shrink-0">
                      <FileText className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
                    </div>
                    <div className="min-w-0 flex-1 text-left">
                      <h3 className="font-semibold text-white text-sm sm:text-base">
                        {t('dashboard.quickActions.addVisit')}
                      </h3>
                      <p className="text-xs sm:text-sm text-purple-100 truncate">
                        {t('dashboard.quickActions.addVisitDesc')}
                      </p>
                    </div>
                  </div>
                </Button>
              </motion.div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* فلاتر البيانات */}
      <motion.div variants={itemVariants}>
        <DashboardFilters
          onFiltersChange={handleFiltersChange}
          currentFilters={currentFilters}
        />
      </motion.div>

      {/* إحصائيات المشاريع */}
      <motion.div variants={itemVariants}>
        <h2 className="text-xl sm:text-2xl font-bold text-gray-800 dark:text-white mb-3 sm:mb-4">📊 {t('dashboard.analytics.projectsStats')}</h2>
        <motion.div variants={containerVariants} className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 lg:gap-6 mb-6 sm:mb-8">
          {projectStatsCards.map((stat) => (
            <motion.div variants={itemVariants} key={stat.title}>
              <Card className={`shadow-lg hover:shadow-xl transition-shadow duration-300 border-l-4 border-${stat.color}-500 dark:bg-gray-800`}>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 p-3 sm:p-6">
                  <CardTitle className="text-xs sm:text-sm font-medium text-gray-600 dark:text-gray-300 leading-tight">{stat.title}</CardTitle>
                  <div className="flex-shrink-0">{stat.icon}</div>
                </CardHeader>
                <CardContent className="p-3 sm:p-6 pt-0">
                  <div className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white">{stat.value}</div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </motion.div>
      </motion.div>

      {/* إحصائيات الصيانة */}
      <motion.div variants={itemVariants}>
        <h2 className="text-xl sm:text-2xl font-bold text-gray-800 dark:text-white mb-3 sm:mb-4">🔧 {t('dashboard.analytics.maintenanceStats')}</h2>
        <motion.div variants={containerVariants} className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 lg:gap-6 mb-6 sm:mb-8">
          {maintenanceStatsCards.map((stat) => (
            <motion.div variants={itemVariants} key={stat.title}>
              <Card className={`shadow-lg hover:shadow-xl transition-shadow duration-300 border-l-4 border-${stat.color}-500 dark:bg-gray-800`}>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 p-3 sm:p-6">
                  <CardTitle className="text-xs sm:text-sm font-medium text-gray-600 dark:text-gray-300 leading-tight">{stat.title}</CardTitle>
                  <div className="flex-shrink-0">{stat.icon}</div>
                </CardHeader>
                <CardContent className="p-3 sm:p-6 pt-0">
                  <div className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white">{stat.value}</div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </motion.div>
      </motion.div>

      {/* إحصائيات نقص المواد */}
      <motion.div variants={itemVariants}>
        <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-4">📦 {t('dashboard.analytics.materialStats')}</h2>
        <motion.div variants={containerVariants} className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          {materialStatsCards.map((stat) => (
            <motion.div variants={itemVariants} key={stat.title}>
              <Card className={`shadow-lg hover:shadow-xl transition-shadow duration-300 border-l-4 border-${stat.color}-500 dark:bg-gray-800`}>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium text-gray-600 dark:text-gray-300">{stat.title}</CardTitle>
                  {stat.icon}
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-gray-900 dark:text-white">{stat.value}</div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </motion.div>
      </motion.div>

      <motion.div variants={containerVariants} className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <motion.div variants={itemVariants} className="lg:col-span-2">
          <Card className="shadow-lg dark:bg-gray-800 border-l-4 border-gradient-to-b from-blue-500 to-purple-500">
            <CardHeader>
              <CardTitle className="text-xl font-semibold text-gray-700 dark:text-white flex items-center gap-2">
                📊 {t('dashboard.installedMaterialsChart')}
                {currentFilters.materialType && (
                  <span className="text-sm bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 px-2 py-1 rounded-full">
                    {t(`dashboard.${currentFilters.materialType}`)}
                  </span>
                )}
              </CardTitle>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                {t('dashboard.installedMaterialsDescription')}
              </p>
            </CardHeader>
            <CardContent className="h-80">
              {loading ? (
                <div className="flex items-center justify-center h-full">
                  <div className="animate-spin rounded-full h-12 w-12 border-t-4 border-b-4 border-blue-500"></div>
                </div>
              ) : (
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={barChartData}
                    margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" opacity={0.5} />
                    <XAxis
                      dataKey="name"
                      tick={{ fontSize: 12, fill: '#6b7280' }}
                      axisLine={{ stroke: '#d1d5db' }}
                    />
                    <YAxis
                      tick={{ fontSize: 12, fill: '#6b7280' }}
                      axisLine={{ stroke: '#d1d5db' }}
                    />
                    <Tooltip
                      formatter={(value) => [`${value} ${t('dashboard.unit')}`, t('dashboard.installedQuantity')]}
                      labelStyle={{ color: '#374151', fontWeight: 'bold' }}
                      contentStyle={{
                        backgroundColor: 'rgba(255, 255, 255, 0.95)',
                        borderColor: '#e5e7eb',
                        borderRadius: '0.5rem',
                        boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
                        border: 'none'
                      }}
                    />
                    <Legend />
                    <Bar
                      dataKey="value"
                      name={t('dashboard.installedQuantity')}
                      radius={[4, 4, 0, 0]}
                    >
                      {barChartData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.fill} />
                      ))}
                    </Bar>
                  </BarChart>
                </ResponsiveContainer>
              )}
            </CardContent>
          </Card>
        </motion.div>

        <motion.div variants={itemVariants}>
          <Card className="shadow-lg dark:bg-gray-800 border-l-4 border-gradient-to-b from-indigo-500 to-purple-500">
            <CardHeader>
              <CardTitle className="text-xl font-semibold text-gray-700 dark:text-white flex items-center gap-2">
                📊 {t('dashboard.projectStatus')}
                {filteredData && (
                  <span className="text-xs bg-indigo-100 dark:bg-indigo-900 text-indigo-800 dark:text-indigo-200 px-2 py-1 rounded-full">
                    مفلتر
                  </span>
                )}
              </CardTitle>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                {t('dashboard.projectStatusDescription')}
              </p>
            </CardHeader>
            <CardContent>
              {/* إحصائيات سريعة */}
              <div className="grid grid-cols-2 gap-3 mb-6">
                <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 p-3 rounded-lg border border-blue-200 dark:border-blue-700">
                  <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">{projectStats.total}</div>
                  <div className="text-sm text-blue-700 dark:text-blue-300">{t('dashboard.totalProjects')}</div>
                </div>
                <div className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 p-3 rounded-lg border border-green-200 dark:border-green-700">
                  <div className="text-2xl font-bold text-green-600 dark:text-green-400">{projectStats.completed}</div>
                  <div className="text-sm text-green-700 dark:text-green-300">{t('dashboard.completed')}</div>
                </div>
              </div>

              {/* الرسم البياني الدائري المحسن */}
              <div className="h-80">
                {loading ? (
                  <div className="flex items-center justify-center h-full">
                    <div className="animate-spin rounded-full h-12 w-12 border-t-4 border-b-4 border-indigo-500"></div>
                  </div>
                ) : (
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <defs>
                        <linearGradient id="notStartedGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                          <stop offset="0%" stopColor="#fbbf24" />
                          <stop offset="100%" stopColor="#f59e0b" />
                        </linearGradient>
                        <linearGradient id="inProgressGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                          <stop offset="0%" stopColor="#3b82f6" />
                          <stop offset="100%" stopColor="#1d4ed8" />
                        </linearGradient>
                        <linearGradient id="completedGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                          <stop offset="0%" stopColor="#10b981" />
                          <stop offset="100%" stopColor="#059669" />
                        </linearGradient>
                        <linearGradient id="delayedGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                          <stop offset="0%" stopColor="#ef4444" />
                          <stop offset="100%" stopColor="#dc2626" />
                        </linearGradient>
                      </defs>
                      <Pie
                        data={pieChartData.filter(item => item.value > 0)}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        outerRadius={100}
                        innerRadius={40}
                        fill="#8884d8"
                        dataKey="value"
                        nameKey="name"
                        label={({ name, value, percent }) =>
                          percent > 0.05 ? `${value} (${(percent * 100).toFixed(0)}%)` : ''
                        }
                        labelStyle={{
                          fontSize: '12px',
                          fontWeight: 'bold',
                          fill: '#374151'
                        }}
                      >
                        {pieChartData.map((entry, index) => {
                          let fillColor = entry.color;
                          if (entry.name.includes('لم تبدأ') || entry.name.includes('Not Started')) {
                            fillColor = 'url(#notStartedGradient)';
                          } else if (entry.name.includes('جاري') || entry.name.includes('Progress')) {
                            fillColor = 'url(#inProgressGradient)';
                          } else if (entry.name.includes('منجزة') || entry.name.includes('Completed')) {
                            fillColor = 'url(#completedGradient)';
                          } else if (entry.name.includes('متأخر') || entry.name.includes('Delayed')) {
                            fillColor = 'url(#delayedGradient)';
                          }

                          return (
                            <Cell
                              key={`cell-${index}`}
                              fill={fillColor}
                              stroke="#ffffff"
                              strokeWidth={3}
                            />
                          );
                        })}
                      </Pie>
                      <Tooltip
                        formatter={(value, name) => [
                          `${value} ${t('dashboard.totalProjects').includes('مشروع') ? 'مشروع' : 'project'}`,
                          name
                        ]}
                        contentStyle={{
                          backgroundColor: 'rgba(255, 255, 255, 0.98)',
                          borderColor: '#e5e7eb',
                          borderRadius: '12px',
                          boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
                          border: 'none',
                          fontWeight: 'bold',
                          padding: '12px'
                        }}
                        labelStyle={{
                          color: '#1f2937',
                          fontWeight: 'bold',
                          marginBottom: '4px'
                        }}
                      />
                      <Legend
                        layout="horizontal"
                        verticalAlign="bottom"
                        align="center"
                        wrapperStyle={{
                          paddingTop: '20px',
                          fontSize: '14px',
                          fontWeight: '500'
                        }}
                        iconType="circle"
                      />
                    </PieChart>
                  </ResponsiveContainer>
                )}
              </div>

              {/* قائمة تفصيلية بالحالات */}
              <div className="mt-6 space-y-3">
                {pieChartData.filter(item => item.value > 0).map((item, index) => (
                  <motion.div
                    key={item.name}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="flex justify-between items-center p-3 bg-gradient-to-r from-gray-50 to-white dark:from-gray-700 dark:to-gray-600 rounded-lg shadow-sm hover:shadow-md transition-all duration-200 border border-gray-200 dark:border-gray-600"
                  >
                    <div className="flex items-center gap-3">
                      <div
                        className="w-4 h-4 rounded-full shadow-sm"
                        style={{ backgroundColor: item.color }}
                      ></div>
                      <span className="font-medium text-gray-700 dark:text-gray-200">{item.name}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="font-bold text-lg text-gray-900 dark:text-white">
                        {item.value}
                      </span>
                      <span className="text-sm text-gray-500 dark:text-gray-400">
                        ({projectStats.total > 0 ? ((item.value / projectStats.total) * 100).toFixed(1) : 0}%)
                      </span>
                    </div>
                  </motion.div>
                ))}
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </motion.div>

      {/* نافذة إضافة زيارة جديدة */}
      <AddVisitModal
        isOpen={showAddVisitModal}
        onClose={() => setShowAddVisitModal(false)}
        onSuccess={handleVisitSuccess}
      />
    </motion.div>
  );
};

export default DashboardPage;
