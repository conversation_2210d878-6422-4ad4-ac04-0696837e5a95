import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import { useToast } from '@/components/ui/use-toast';
import { getProjectById, updateProject } from '@/lib/supabaseDatabase';
import { getProjectFiles } from '@/lib/fileService';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ArrowLeft, Save, FileText, Download } from 'lucide-react';
import FileDisplayManager from '@/components/FileUpload/FileDisplayManager';
import FileUploadManager from '@/components/FileUpload/FileUploadManager';
import jsPDF from 'jspdf';
import 'jspdf-autotable';
import * as XLSX from 'xlsx';
import { saveAs } from 'file-saver';

const EditProjectPage = () => {
  const { projectId } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const { t, i18n } = useTranslation();
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);
  const [files, setFiles] = useState([]);
  const [showUploadArea, setShowUploadArea] = useState(false);
  const [project, setProject] = useState({
    contract_number: '',
    job_order: '',
    client_name: '',
    city: '',
    address: '',
    phone: '',
    email: '',
    start_date: '',
    completion_date: '',
    duration_weeks: 0,
    status: 'Not Started',
    quantity_pvc: 0,
    quantity_shutter: 0,
    quantity_sgs: 0,
    quantity_doors: 0,
    quantity_glass: 0,
    notes: '',
  });



  useEffect(() => {
    const fetchProject = async () => {
      setLoading(true);
      try {
        // استخدام معرف المشروع مباشرة (UUID)
        console.log('Fetching project with ID:', projectId);

        if (!projectId) {
          throw new Error('Invalid project ID');
        }

        const projectData = await getProjectById(projectId);
        console.log('Project data:', projectData);

        if (projectData) {
          // Format dates for input fields and extract materials data
          const formattedProject = {
            ...projectData,
            start_date: projectData.start_date ? new Date(projectData.start_date).toISOString().split('T')[0] : '',
            completion_date: projectData.completion_date || projectData.end_date ?
              new Date(projectData.completion_date || projectData.end_date).toISOString().split('T')[0] : '',
            contract_number: projectData.project_number || '',
            job_order: projectData.manufacturing_number || projectData.materials?.manufacturing_number || projectData.materials?.job_order || '',
            duration_weeks: projectData.duration_weeks || projectData.materials?.duration_weeks || 0,
            quantity_pvc: projectData.materials?.quantity_pvc || 0,
            quantity_shutter: projectData.materials?.quantity_shutter || 0,
            quantity_sgs: projectData.materials?.quantity_sgs || 0,
            quantity_doors: projectData.materials?.quantity_doors || 0,
            quantity_glass: projectData.materials?.quantity_glass || 0,
            // Map database fields to component fields
            city: projectData.city || '',
            address: projectData.address || projectData.city || projectData.project_location || '',
            phone: projectData.phone || projectData.client_phone || '',
            email: projectData.email || projectData.client_email || '',
          };
          setProject(formattedProject);

          // تحميل ملفات المشروع
          const filesResult = await getProjectFiles(projectId);
          if (filesResult.success) {
            const formattedFiles = filesResult.data.map(file => ({
              id: file.id,
              name: file.file_name,
              size: file.file_size,
              type: file.file_type,
              url: file.file_url,
              bucket: file.bucket_name,
              path: file.file_path,
              uploaded: true,
              uploadedAt: file.created_at
            }));
            setFiles(formattedFiles);
          }


        } else {
          toast({
            title: t('editProject.notFoundTitle'),
            description: t('editProject.notFoundMsg'),
            variant: "destructive"
          });
          navigate('/projects');
        }
      } catch (error) {
        console.error('Error fetching project:', error);
        toast({
          title: t('editProject.fetchErrorTitle'),
          description: t('editProject.fetchErrorMsg'),
          variant: "destructive"
        });
        navigate('/projects');
      } finally {
        setLoading(false);
      }
    };

    fetchProject();
  }, [projectId, navigate, toast, t]);



  const handleChange = (e) => {
    const { name, value } = e.target;
    setProject(prev => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (name, value) => {
    setProject(prev => ({ ...prev, [name]: value }));
  };

  const handleNumberChange = (e) => {
    const { name, value } = e.target;
    setProject(prev => ({ ...prev, [name]: value === '' ? 0 : Number(value) }));
  };

  // معالجة تغيير الملفات
  const handleFilesChange = (newFiles) => {
    setFiles(newFiles);
  };



  // وظيفة تصدير المشروع بصيغة PDF
  const handleExportToPDF = () => {
    try {
      // إنشاء مستند PDF جديد
      const doc = new jsPDF('landscape');

      // إضافة عنوان التقرير
      doc.setFontSize(18);
      doc.text("Project Details", doc.internal.pageSize.getWidth() / 2, 15, { align: 'center' });

      // إضافة التاريخ
      doc.setFontSize(10);
      const today = new Date().toLocaleDateString();
      doc.text(`Date: ${today}`, doc.internal.pageSize.getWidth() - 20, 10, { align: 'right' });

      // إضافة معلومات المشروع
      doc.setFontSize(12);
      doc.text("Project Information:", 14, 30);

      const projectInfo = [
        ["Contract Number", project.contract_number || ''],
        ["Job Order", project.job_order || ''],
        ["Client Name", project.client_name || ''],
        ["City", project.city || ''],
        ["Address", project.address || ''],
        ["Phone", project.phone || ''],
        ["Email", project.email || ''],
        ["Start Date", project.start_date ? new Date(project.start_date).toLocaleDateString() : ''],
        ["Completion Date", project.completion_date ? new Date(project.completion_date).toLocaleDateString() : ''],
        ["Status", project.status || ''],
        ["Notes", project.notes || '']
      ];

      // إنشاء جدول معلومات المشروع
      doc.autoTable({
        startY: 35,
        head: [["Property", "Value"]],
        body: projectInfo,
        theme: 'grid',
        headStyles: {
          fillColor: [41, 128, 185],
          textColor: 255,
          fontStyle: 'bold'
        },
        styles: {
          fontSize: 10,
          cellPadding: 3
        }
      });

      // إضافة معلومات الكميات
      const quantitiesY = doc.lastAutoTable.finalY + 15;
      doc.text("Quantities:", 14, quantitiesY);

      const quantitiesInfo = [
        ["PVC (m²)", project.quantity_pvc || '0'],
        ["Shutter (units)", project.quantity_shutter || '0'],
        ["SGS (m)", project.quantity_sgs || '0'],
        ["Doors (units)", project.quantity_doors || '0'],
        ["Glass (m²)", project.quantity_glass || '0']
      ];

      // إنشاء جدول الكميات
      doc.autoTable({
        startY: quantitiesY + 5,
        head: [["Material", "Quantity"]],
        body: quantitiesInfo,
        theme: 'grid',
        headStyles: {
          fillColor: [41, 128, 185],
          textColor: 255,
          fontStyle: 'bold'
        },
        styles: {
          fontSize: 10,
          cellPadding: 3
        }
      });

      // إضافة ترويسة وتذييل
      const pageCount = doc.internal.getNumberOfPages();
      for (let i = 1; i <= pageCount; i++) {
        doc.setPage(i);
        // تذييل الصفحة
        doc.setFontSize(8);
        doc.text(
          `Page ${i} of ${pageCount}`,
          doc.internal.pageSize.getWidth() / 2,
          doc.internal.pageSize.getHeight() - 10,
          { align: 'center' }
        );
        // ترويسة الصفحة - اسم التطبيق
        doc.text('SultanTrack', 20, 10);
      }

      // حفظ الملف
      doc.save(`Project_${project.contract_number}_${new Date().toISOString().split('T')[0]}.pdf`);

      toast({
        title: "PDF Export",
        description: "PDF file has been generated successfully.",
        className: "bg-green-500 text-white dark:bg-green-600"
      });
    } catch (error) {
      console.error('Error exporting to PDF:', error);
      toast({
        title: "Export Error",
        description: "Could not generate PDF file. Please try again.",
        variant: "destructive"
      });
    }
  };

  // وظيفة تصدير المشروع بصيغة Excel
  const handleExportToExcel = () => {
    try {
      // تحضير البيانات
      const projectData = {
        'Contract Number': project.contract_number || '',
        'Job Order': project.job_order || '',
        'Client Name': project.client_name || '',
        'City': project.city || '',
        'Address': project.address || '',
        'Phone': project.phone || '',
        'Email': project.email || '',
        'Start Date': project.start_date ? new Date(project.start_date).toLocaleDateString() : '',
        'Completion Date': project.completion_date ? new Date(project.completion_date).toLocaleDateString() : '',
        'Status': project.status || '',
        'Notes': project.notes || '',
        'PVC (units)': project.quantity_pvc || '0',
        'Shutter (units)': project.quantity_shutter || '0',
        'SGS (units)': project.quantity_sgs || '0',
        'Doors (units)': project.quantity_doors || '0',
        'Glass (units)': project.quantity_glass || '0'
      };

      // إنشاء ورقة عمل
      const worksheet = XLSX.utils.json_to_sheet([projectData]);

      // تعديل عرض الأعمدة
      const wscols = Object.keys(projectData).map(() => ({ wch: 20 }));
      worksheet['!cols'] = wscols;

      // إنشاء مصنف عمل
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Project Details');

      // تصدير المصنف
      const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
      const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      saveAs(blob, `Project_${project.contract_number}_${new Date().toISOString().split('T')[0]}.xlsx`);

      toast({
        title: "Excel Export",
        description: "Excel file has been generated successfully.",
        className: "bg-green-500 text-white dark:bg-green-600"
      });
    } catch (error) {
      console.error('Error exporting to Excel:', error);
      toast({
        title: "Export Error",
        description: "Could not generate Excel file. Please try again.",
        variant: "destructive"
      });
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      // استخدام معرف المشروع مباشرة (UUID)
      if (!projectId) {
        throw new Error('Invalid project ID');
      }

      // تحضير البيانات للحفظ - حفظ البيانات في الأعمدة الصحيحة
      const updatedData = {
        project_number: project.contract_number, // حفظ contract_number في project_number
        client_name: project.client_name,
        city: project.address, // حفظ قيمة address في عمود city
        address: project.address,
        phone: project.phone,
        email: project.email,
        start_date: project.start_date || null,
        completion_date: project.completion_date && project.completion_date !== 'yyyy-mm-dd' ? project.completion_date : null,
        end_date: project.completion_date && project.completion_date !== 'yyyy-mm-dd' ? project.completion_date : null,
        duration_weeks: parseInt(project.duration_weeks) || 0,
        manufacturing_number: project.job_order, // حفظ job_order في manufacturing_number
        status: project.status,
        notes: project.notes,
        // حفظ الكميات في عمود materials كـ JSON
        materials: {
          job_order: project.job_order,
          duration_weeks: parseInt(project.duration_weeks) || 0,
          quantity_pvc: parseInt(project.quantity_pvc) || 0, // وحدات - رقم صحيح
          quantity_shutter: parseInt(project.quantity_shutter) || 0, // وحدات - رقم صحيح
          quantity_sgs: parseInt(project.quantity_sgs) || 0, // وحدات - رقم صحيح
          quantity_doors: parseInt(project.quantity_doors) || 0, // وحدات - رقم صحيح
          quantity_glass: parseInt(project.quantity_glass) || 0, // وحدات - رقم صحيح
        }
      };

      await updateProject(projectId, updatedData);
      toast({
        title: t('editProject.updateSuccessTitle'),
        description: t('editProject.updateSuccessMsg'),
        className: "bg-green-500 text-white dark:bg-green-600"
      });
      navigate('/projects');
    } catch (error) {
      console.error('Error updating project:', error);
      toast({
        title: t('editProject.updateErrorTitle'),
        description: error.message || t('editProject.updateErrorMsg'),
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1, transition: { staggerChildren: 0.05 } },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: { y: 0, opacity: 1, transition: { type: 'spring', stiffness: 100 } },
  };

  return (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={containerVariants}
      className="p-4 md:p-6"
      dir={i18n.dir()}
    >
      <motion.div variants={itemVariants} className="flex items-center mb-6">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => navigate('/projects')}
          className="mr-4 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
        >
          <ArrowLeft className="h-5 w-5" />
        </Button>
        <h1 className="text-2xl font-bold text-gray-800 dark:text-white">
          {t('editProject.title')}
        </h1>

        <div className="ml-auto flex space-x-2">
          <Button
            onClick={handleExportToPDF}
            variant="outline"
            className="flex items-center gap-2 border-red-500 text-red-500 hover:bg-red-500 hover:text-white"
            size="sm"
            disabled={loading}
          >
            <FileText className="h-4 w-4" />
            {t('exportService.exportPDF')}
          </Button>

          <Button
            onClick={handleExportToExcel}
            variant="outline"
            className="flex items-center gap-2 border-green-500 text-green-500 hover:bg-green-500 hover:text-white"
            size="sm"
            disabled={loading}
          >
            <Download className="h-4 w-4" />
            {t('exportService.exportExcel')}
          </Button>
        </div>
      </motion.div>

      {loading ? (
        <motion.div variants={itemVariants} className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-4 border-b-4 border-blue-500"></div>
        </motion.div>
      ) : (
        <form onSubmit={handleSubmit}>
          <motion.div variants={itemVariants} className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card className="shadow-lg dark:bg-gray-800/80 border-gray-200 dark:border-gray-700/50 backdrop-blur-md">
              <CardHeader>
                <CardTitle className="text-xl text-gray-800 dark:text-white">
                  {t('editProject.basicInfo')}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="contract_number">{t('addProject.contractNumber') || "Contract Number"}</Label>
                  <Input
                    id="contract_number"
                    name="contract_number"
                    value={project.contract_number}
                    onChange={handleChange}
                    required
                    placeholder={t('addProject.contractNumberPlaceholder') || "Enter contract number"}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="job_order">{t('addProject.jobOrder') || "Job Order"}</Label>
                  <Input
                    id="job_order"
                    name="job_order"
                    value={project.job_order}
                    onChange={handleChange}
                    placeholder={t('addProject.jobOrderPlaceholder') || "Enter job order number"}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="client_name">{t('addProject.clientName') || "Client Name"}</Label>
                  <Input
                    id="client_name"
                    name="client_name"
                    value={project.client_name}
                    onChange={handleChange}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="address">{t('addProject.city') || "City"}</Label>
                  <Input
                    id="address"
                    name="address"
                    value={project.address}
                    onChange={handleChange}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="phone">{t('editProject.phone') || "Phone"}</Label>
                  <Input
                    id="phone"
                    name="phone"
                    value={project.phone}
                    onChange={handleChange}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="email">{t('editProject.email') || "Email"}</Label>
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    value={project.email}
                    onChange={handleChange}
                  />
                </div>
              </CardContent>
            </Card>

            <Card className="shadow-lg dark:bg-gray-800/80 border-gray-200 dark:border-gray-700/50 backdrop-blur-md">
              <CardHeader>
                <CardTitle className="text-xl text-gray-800 dark:text-white">
                  {t('editProject.projectDetails')}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="start_date">{t('addProject.startDate') || "Start Date"}</Label>
                  <Input
                    id="start_date"
                    name="start_date"
                    type="date"
                    value={project.start_date}
                    onChange={handleChange}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="completion_date">{t('editProject.completionDate') || "Completion Date"}</Label>
                  <Input
                    id="completion_date"
                    name="completion_date"
                    type="date"
                    value={project.completion_date && project.completion_date !== 'yyyy-mm-dd' ? project.completion_date : ''}
                    onChange={handleChange}
                    placeholder="yyyy-mm-dd"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="duration_weeks">{t('addProject.durationDays') || "Project Duration (Weeks)"}</Label>
                  <Input
                    id="duration_weeks"
                    name="duration_weeks"
                    type="number"
                    value={project.duration_weeks}
                    onChange={handleNumberChange}
                    placeholder="Enter duration in weeks"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="status">{t('editProject.status') || "Status"}</Label>
                  <Select
                    value={project.status}
                    onValueChange={(value) => handleSelectChange('status', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder={t('editProject.selectStatus') || "Select Status"} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Not Started">{t('dashboard.notStarted') || "Not Started"}</SelectItem>
                      <SelectItem value="In Progress">{t('dashboard.inProgress') || "In Progress"}</SelectItem>
                      <SelectItem value="Delayed">{t('dashboard.delayed') || "Delayed"}</SelectItem>
                      <SelectItem value="Completed">{t('dashboard.completed') || "Completed"}</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="notes">{t('addProject.notes') || "Notes"}</Label>
                  <Textarea
                    id="notes"
                    name="notes"
                    value={project.notes}
                    onChange={handleChange}
                    rows={3}
                  />
                </div>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div variants={itemVariants} className="grid grid-cols-1 gap-6 mt-6">
            <Card className="shadow-lg dark:bg-gray-800/80 border-gray-200 dark:border-gray-700/50 backdrop-blur-md">
              <CardHeader>
                <CardTitle className="text-xl text-gray-800 dark:text-white">
                  {t('editProject.quantities')}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="quantity_pvc">{t('addProject.quantity_pvc') || "PVC (units)"}</Label>
                    <Input
                      id="quantity_pvc"
                      name="quantity_pvc"
                      type="number"
                      min="0"
                      step="1"
                      value={project.quantity_pvc}
                      onChange={handleNumberChange}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="quantity_shutter">{t('addProject.quantity_shutter') || "Shutter (units)"}</Label>
                    <Input
                      id="quantity_shutter"
                      name="quantity_shutter"
                      type="number"
                      min="0"
                      step="1"
                      value={project.quantity_shutter}
                      onChange={handleNumberChange}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="quantity_sgs">{t('addProject.quantity_sgs') || "SGS (units)"}</Label>
                    <Input
                      id="quantity_sgs"
                      name="quantity_sgs"
                      type="number"
                      min="0"
                      step="1"
                      value={project.quantity_sgs}
                      onChange={handleNumberChange}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="quantity_doors">{t('addProject.quantity_doors') || "Doors (units)"}</Label>
                    <Input
                      id="quantity_doors"
                      name="quantity_doors"
                      type="number"
                      min="0"
                      step="1"
                      value={project.quantity_doors}
                      onChange={handleNumberChange}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="quantity_glass">{t('addProject.quantity_glass') || "Glass (units)"}</Label>
                    <Input
                      id="quantity_glass"
                      name="quantity_glass"
                      type="number"
                      min="0"
                      step="1"
                      value={project.quantity_glass}
                      onChange={handleNumberChange}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>



          {/* إدارة الملفات */}
          <motion.div variants={itemVariants} className="mt-6">
            <Card className="shadow-lg dark:bg-gray-800/80 border-gray-200 dark:border-gray-700/50 backdrop-blur-md">
              <CardHeader>
                <CardTitle className="text-xl text-gray-800 dark:text-white">
                  {t('editProject.uploadFiles') || "Upload Files (PDF, Images)"}
                </CardTitle>
              </CardHeader>
              <CardContent>
                {/* زر إضافة ملفات جديدة */}
                {!showUploadArea && (
                  <div className="mb-4">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setShowUploadArea(true)}
                      className="w-full border-dashed border-2 border-blue-300 text-blue-600 hover:bg-blue-50 dark:border-blue-600 dark:text-blue-400 dark:hover:bg-blue-900/20"
                    >
                      <FileText className="h-5 w-5 mr-2" />
                      {t('editProject.addNewFiles') || "Add New Files"}
                    </Button>
                  </div>
                )}

                {/* منطقة رفع الملفات الجديدة */}
                {showUploadArea && (
                  <div className="mb-6">
                    <div className="flex justify-between items-center mb-3">
                      <h5 className="text-sm font-medium text-gray-700 dark:text-gray-300">
                        {t('fileDisplay.uploadNewFiles') || "Upload New Files"}
                      </h5>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => setShowUploadArea(false)}
                        className="text-gray-500 hover:text-gray-700"
                      >
                        ✕
                      </Button>
                    </div>
                    <FileUploadManager
                      projectId={projectId}
                      files={[]} // ملفات جديدة فقط
                      onFilesChange={(newFiles) => {
                        // إضافة الملفات الجديدة للقائمة الموجودة
                        const updatedFiles = [...files, ...newFiles];
                        setFiles(updatedFiles);
                        if (newFiles.length > 0) {
                          setShowUploadArea(false); // إخفاء منطقة الرفع بعد الرفع
                        }
                      }}
                      maxFiles={10}
                      maxFileSize={10 * 1024 * 1024} // 10MB
                      allowedTypes={['.pdf', '.jpg', '.jpeg', '.png', '.doc', '.docx']}
                      showPreview={false}
                      disabled={loading}
                    />
                  </div>
                )}

                {/* عرض الملفات الموجودة */}
                <FileDisplayManager
                  projectId={projectId}
                  files={files}
                  onFilesChange={handleFilesChange}
                  showPreview={true}
                  disabled={loading}
                />
              </CardContent>
            </Card>
          </motion.div>

          <motion.div variants={itemVariants} className="mt-6 flex justify-end">
            <Button
              type="button"
              variant="outline"
              onClick={() => navigate('/projects')}
              className="mr-2"
            >
              {t('editProject.cancel')}
            </Button>
            <Button
              type="submit"
              disabled={loading}
              className="bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white shadow-md hover:shadow-lg transition-shadow"
            >
              {loading ? (
                <div className="animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-white"></div>
              ) : (
                <>
                  <Save className="h-5 w-5 mr-2" />
                  {t('editProject.saveChanges')}
                </>
              )}
            </Button>
          </motion.div>
        </form>
      )}


    </motion.div>
  );
};

export default EditProjectPage;
