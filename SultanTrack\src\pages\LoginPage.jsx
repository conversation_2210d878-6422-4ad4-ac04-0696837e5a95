import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/components/ui/use-toast';
import { useTranslation } from 'react-i18next';
import { Building2, LogIn, Eye, EyeOff } from 'lucide-react';
import { supabase } from '@/lib/supabase';

const LoginPage = ({ onLogin }) => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();

  const handleLoginSubmit = async (event) => {
    event.preventDefault();
    setLoading(true);

    console.log('Attempting login with email:', email);

    const { data, error } = await supabase.auth.signInWithPassword({
      email: email,
      password: password,
    });

    console.log('Login response:', { data, error });

    if (error) {
      console.error('Login error:', error);
      toast({
        title: t('login.authFailedTitle') || "Authentication Failed",
        description: `${error.message} - تأكد من إعداد Supabase وإنشاء المستخدمين التجريبيين`,
        variant: "destructive",
      });
    } else if (data.user && data.session) {
      const userRole = data.user.user_metadata?.role || 'technician';
      console.log('Login successful, user role:', userRole);
      console.log('User data:', data.user);
      onLogin(userRole);
      toast({
        title: t('login.loginSuccessTitle') || "Login Successful",
        description: t('login.welcomeMessage', { email: data.user.email }) || `Welcome back, ${data.user.email}!`,
      });
    }
    setLoading(false);
  };

  return (
    <div
      className="min-h-screen flex items-center justify-center p-4 bg-gradient-to-tr from-slate-900 via-sky-700 to-blue-600"
      dir={i18n.dir()}
    >
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5, ease: "circOut" }}
        className="w-full max-w-lg p-8 md:p-10 space-y-8 bg-white/10 dark:bg-gray-800/30 backdrop-blur-xl rounded-2xl shadow-2xl border border-white/20"
      >
        <div className="text-center">
          <motion.div
            initial={{ y: -20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.2, type: "spring", stiffness: 120 }}
          >
            <Building2 size={60} className="mx-auto text-sky-300 mb-4" />
            <h1 className="text-4xl md:text-5xl font-bold tracking-tight text-transparent bg-clip-text bg-gradient-to-r from-sky-300 via-blue-300 to-indigo-300">
              {t('appName')}
            </h1>
            <p className="mt-3 text-lg text-sky-100/80">{t('login.title')}</p>
          </motion.div>
        </div>
        <motion.form
          onSubmit={handleLoginSubmit}
          className="space-y-6"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.4, staggerChildren: 0.1 }}
        >
          <motion.div variants={{ hidden: { opacity: 0, y: 20 }, visible: { opacity: 1, y: 0 } }}>
            <Label htmlFor="email" className="text-sky-100/90 text-sm font-medium">{t('login.username')}</Label>
            <Input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder={t('login.emailPlaceholder') || "Enter your email address"}
              required
              className="mt-1 w-full bg-white/5 border-sky-300/30 text-white placeholder-sky-200/50 focus:ring-sky-400 focus:border-sky-400 rounded-lg shadow-sm"
              disabled={loading}
            />
          </motion.div>
          <motion.div variants={{ hidden: { opacity: 0, y: 20 }, visible: { opacity: 1, y: 0 } }} className="relative">
            <Label htmlFor="password" className="text-sky-100/90 text-sm font-medium">{t('login.password')}</Label>
            <Input
              id="password"
              type={showPassword ? "text" : "password"}
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder={t('login.passwordPlaceholder') || "Enter your password"}
              required
              className="mt-1 w-full bg-white/5 border-sky-300/30 text-white placeholder-sky-200/50 focus:ring-sky-400 focus:border-sky-400 rounded-lg shadow-sm pr-10"
              disabled={loading}
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute inset-y-0 end-0 top-7 flex items-center px-3 text-sky-200/70 hover:text-sky-100"
              aria-label={showPassword ? "Hide password" : "Show password"}
            >
              {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
            </button>
          </motion.div>
          <motion.div variants={{ hidden: { opacity: 0, y: 20 }, visible: { opacity: 1, y: 0 } }}>
            <Button
              type="submit"
              className="w-full bg-gradient-to-r from-sky-500 to-indigo-600 hover:from-sky-600 hover:to-indigo-700 text-white py-3 text-lg font-semibold rounded-lg shadow-lg transition-all duration-300 ease-in-out transform hover:scale-105 focus:ring-4 focus:ring-sky-300/50"
              disabled={loading}
            >
              {loading ?
                (<span className="flex items-center justify-center">
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  {t('login.loggingIn') || 'Logging in...'}
                </span>) :
                (<> <LogIn className={`h-5 w-5 ${i18n.language === 'ar' ? 'ml-2' : 'mr-2'}`} /> {t('login.loginButton')} </>)
              }
            </Button>
          </motion.div>
        </motion.form>



      </motion.div>
    </div>
  );
};

export default LoginPage;