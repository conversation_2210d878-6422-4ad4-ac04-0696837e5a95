
import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Search as SearchIcon, Download, FileText, RotateCcw, Loader2, Eye } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { useNavigate } from 'react-router-dom';
import { getAllProjects } from '@/lib/supabaseDatabase';
import { exportProjectsToPDF, exportProjectsToExcel } from '@/lib/exportService';

const SearchProjectPage = () => {
  const { t, i18n } = useTranslation();
  const [searchTerm, setSearchTerm] = useState('');
  const [searchType, setSearchType] = useState('project_number');
  const [searchResults, setSearchResults] = useState([]);
  const [loading, setLoading] = useState(false);
  const [hasSearched, setHasSearched] = useState(false);
  const { toast } = useToast();
  const navigate = useNavigate();

  const handleSearch = async (e) => {
    e.preventDefault();
    if (!searchTerm.trim()) {
      toast({
        title: t('searchProject.validationTitle') || "Search Term Required",
        description: t('searchProject.validationMsg') || "Please enter a search term.",
        variant: "destructive"
      });
      return;
    }

    setLoading(true);
    setHasSearched(true);
    setSearchResults([]);

    try {
      // الحصول على جميع المشاريع
      const allProjects = await getAllProjects();

      // تصفية المشاريع حسب معيار البحث
      let filteredProjects = [];
      const searchTermLower = searchTerm.toLowerCase();

      if (searchType === 'project_number') {
        filteredProjects = allProjects.filter(project =>
          project.project_number && project.project_number.toLowerCase().includes(searchTermLower)
        );
      } else if (searchType === 'client_name') {
        filteredProjects = allProjects.filter(project =>
          project.client_name && project.client_name.toLowerCase().includes(searchTermLower)
        );
      } else if (searchType === 'city') {
        filteredProjects = allProjects.filter(project =>
          project.city && project.city.toLowerCase().includes(searchTermLower)
        );
      }

      // ترتيب المشاريع حسب تاريخ الإنشاء (الأحدث أولاً)
      const sortedProjects = filteredProjects.sort((a, b) => {
        const dateA = a.created_at ? new Date(a.created_at) : new Date(0);
        const dateB = b.created_at ? new Date(b.created_at) : new Date(0);
        return dateB - dateA;
      });

      setSearchResults(sortedProjects);

      if (sortedProjects.length === 0) {
        toast({
          title: t('searchProject.noResultsTitle') || "No Results",
          description: t('searchProject.noResultsMsg') || "No projects found matching your criteria."
        });
      }
    } catch (error) {
      console.error('Error searching projects:', error);
      toast({
        title: t('searchProject.errorTitle') || "Search Error",
        description: error.message,
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDownloadPdf = async (projectId) => {
    try {
      // الحصول على تفاصيل المشروع
      const allProjects = await getAllProjects();
      const project = allProjects.find(p => p.id === projectId);

      if (!project) {
        toast({
          title: t('searchProject.errorTitle') || "Error",
          description: t('searchProject.projectNotFound') || "Project not found.",
          variant: "destructive"
        });
        return;
      }

      // تصدير المشروع بصيغة PDF
      exportProjectToPDF(project, [], t, i18n.language === 'ar');

      toast({
        title: t('searchProject.downloadTitle') || "Download PDF",
        description: t('searchProject.downloadSuccess') || "PDF file has been generated successfully.",
        className: "bg-green-500 text-white dark:bg-green-600"
      });
    } catch (error) {
      console.error('Error downloading PDF:', error);
      toast({
        title: t('searchProject.errorTitle') || "Error",
        description: t('searchProject.downloadError') || "Could not generate PDF file.",
        variant: "destructive"
      });
    }
  };

  const getStatusBadgeVariant = (status) => {
    switch (status?.toLowerCase()) {
      case 'not started':
      case 'لم يبدأ':
      case 'notstarted':
      case 'pending':
      case 'معلق':
        return 'secondary';
      case 'in progress':
      case 'قيد التنفيذ':
      case 'inprogress':
      case 'in_progress':
        return 'default';
      case 'delayed':
      case 'متأخر':
        return 'destructive';
      case 'completed':
      case 'مكتمل':
        return 'success';
      case 'on hold':
      case 'متوقف مؤقتاً':
      case 'onhold':
        return 'warning';
      default:
        return 'secondary';
    }
  };

  // دالة لترجمة حالة المشروع
  const getStatusTranslation = (status) => {
    const statusMap = {
      'completed': t('dashboard.completed') || 'مكتمل',
      'in progress': t('dashboard.inProgress') || 'قيد التنفيذ',
      'inprogress': t('dashboard.inProgress') || 'قيد التنفيذ',
      'in_progress': t('dashboard.inProgress') || 'قيد التنفيذ',
      'delayed': t('dashboard.delayed') || 'متأخر',
      'not started': t('dashboard.notStarted') || 'لم يبدأ',
      'notstarted': t('dashboard.notStarted') || 'لم يبدأ',
      'pending': t('dashboard.pending') || 'معلق',
      'on hold': t('dashboard.onHold') || 'متوقف مؤقتاً',
      'onhold': t('dashboard.onHold') || 'متوقف مؤقتاً',
      'مكتمل': t('dashboard.completed') || 'مكتمل',
      'قيد التنفيذ': t('dashboard.inProgress') || 'قيد التنفيذ',
      'متأخر': t('dashboard.delayed') || 'متأخر',
      'لم يبدأ': t('dashboard.notStarted') || 'لم يبدأ',
      'معلق': t('dashboard.pending') || 'معلق',
      'متوقف مؤقتاً': t('dashboard.onHold') || 'متوقف مؤقتاً'
    };
    return statusMap[status?.toLowerCase()] || status || t('common.notSpecified') || 'غير محدد';
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: { y: 0, opacity: 1, transition: { type: 'spring', stiffness: 100 } },
  };

  return (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={{ visible: { transition: { staggerChildren: 0.1 } } }}
      className="p-4 md:p-6 max-w-5xl mx-auto"
      dir={i18n.dir()}
    >
      <motion.h1 variants={itemVariants} className="text-3xl font-bold text-gray-800 dark:text-white mb-8 gradient-text text-center">
        {t('sidebar.searchProject')}
      </motion.h1>

      <motion.div variants={itemVariants}>
        <Card className="shadow-xl dark:bg-gray-800/80 border-gray-200 dark:border-gray-700/50 backdrop-blur-md">
          <CardHeader>
            <CardTitle className="text-xl text-gray-700 dark:text-gray-200">{t('searchProject.searchTitle') || "Find Project"}</CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSearch} className="space-y-4">
              <div className="flex flex-col sm:flex-row gap-4 items-end">
                <div className="flex-grow">
                  <Label htmlFor="searchTerm" className="text-gray-700 dark:text-gray-300">{t('searchProject.enterSearchTerm') || "Enter Search Term"}</Label>
                  <Input
                    id="searchTerm"
                    type="text"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    placeholder={
                        searchType === 'project_number' ? (t('addProject.projectNumber') || "Project Number") :
                        searchType === 'client_name' ? (t('addProject.clientName') || "Client Name") :
                        (t('addProject.city') || "City")
                    }
                    className="mt-1 dark:bg-gray-700 dark:border-gray-600 dark:text-white focus:ring-blue-500 dark:focus:ring-blue-400"
                  />
                </div>
                <div className="w-full sm:w-auto">
                  <Label htmlFor="searchType" className="text-gray-700 dark:text-gray-300">{t('searchProject.searchBy') || "Search By"}</Label>
                  <select
                    id="searchType"
                    value={searchType}
                    onChange={(e) => setSearchType(e.target.value)}
                    className="mt-1 block w-full p-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm h-10"
                  >
                    <option value="project_number">{t('addProject.projectNumber') || "Project Number"}</option>
                    <option value="client_name">{t('addProject.clientName') || "Client Name"}</option>
                    <option value="city">{t('addProject.city') || "City"}</option>
                  </select>
                </div>
                <Button type="submit" className="bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white h-10 w-full sm:w-auto" disabled={loading}>
                  {loading ? <Loader2 className={`h-5 w-5 animate-spin ${i18n.language === 'ar' ? 'ml-2' : 'mr-2'}`} /> : <SearchIcon className={`h-5 w-5 ${i18n.language === 'ar' ? 'ml-2' : 'mr-2'}`} />}
                  {loading ? (t('searchProject.searching') || "Searching...") : (t('searchProject.searchButton') || "Search")}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </motion.div>

      {loading && (
        <motion.div variants={itemVariants} className="text-center mt-8 flex flex-col items-center">
          <Loader2 className="h-12 w-12 text-blue-500 animate-spin mb-2" />
          <p className="text-lg text-gray-600 dark:text-gray-300">{t('searchProject.loadingResults') || "Loading results..."}</p>
        </motion.div>
      )}

      {!loading && hasSearched && searchResults.length > 0 && (
        <>
          <motion.div variants={itemVariants} className="mt-8 mb-4 flex justify-end gap-2">
            <Button
              onClick={() => {
                try {
                  exportProjectsToPDF(searchResults, `${t('searchProject.searchResults') || "Search Results"}: ${searchTerm}`, t, i18n.language === 'ar');
                } catch (error) {
                  console.error('Error exporting to PDF:', error);
                }
              }}
              variant="outline"
              className="flex items-center gap-2 border-red-500 text-red-500 hover:bg-red-500 hover:text-white"
              size="sm"
            >
              <FileText className="h-4 w-4" />
              Export PDF
            </Button>

            <Button
              onClick={() => {
                try {
                  exportProjectsToExcel(searchResults, `${t('searchProject.searchResults') || "Search Results"}: ${searchTerm}`, t);
                } catch (error) {
                  console.error('Error exporting to Excel:', error);
                }
              }}
              variant="outline"
              className="flex items-center gap-2 border-green-500 text-green-500 hover:bg-green-500 hover:text-white"
              size="sm"
            >
              <Download className="h-4 w-4" />
              Export Excel
            </Button>
          </motion.div>

          <motion.div
            variants={itemVariants}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
            initial="hidden"
            animate="visible"
            transition={{ staggerChildren: 0.1 }}
          >
          {searchResults.map(project => (
            <motion.div variants={itemVariants} key={project.id}>
              <Card className="shadow-lg hover:shadow-xl transition-shadow duration-300 dark:bg-gray-800/70 border-gray-200 dark:border-gray-700/40 backdrop-blur-sm h-full flex flex-col">
                <CardHeader>
                  <CardTitle className="text-lg text-blue-600 dark:text-blue-400 flex justify-between items-center">
                    <span className="truncate" title={project.project_number}>{project.project_number}</span>
                    <Badge variant={getStatusBadgeVariant(project.status)} className="capitalize text-xs whitespace-nowrap">
                      {getStatusTranslation(project.status)}
                    </Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-2 text-sm text-gray-600 dark:text-gray-300 flex-grow">
                  <p><strong>{t('addProject.clientName') || "Client"}:</strong> {project.client_name}</p>
                  <p><strong>{t('addProject.city') || "City"}:</strong> {project.city || project.project_location || project.address || 'غير محدد'}</p>
                  <p><strong>{t('addProject.startDate') || "Start Date"}:</strong> {new Date(project.start_date).toLocaleDateString()}</p>
                  <div>
                    <strong>{t('allProjects.progress') || "Progress"}:</strong> {project.progress_percentage || 0}%
                    <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2 mt-1">
                      <div className="bg-gradient-to-r from-green-400 to-blue-500 h-2 rounded-full" style={{ width: `${project.progress_percentage || 0}%` }}></div>
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="flex flex-col sm:flex-row gap-2 pt-4">
                    <Button
                        variant="outline"
                        size="sm"
                        className="w-full border-sky-500 text-sky-500 hover:bg-sky-500 hover:text-white dark:border-sky-400 dark:text-sky-400 dark:hover:bg-sky-400 dark:hover:text-gray-900"
                        onClick={() => navigate(`/track-project/${project.id}`)}
                      >
                        <Eye className={`h-4 w-4 ${i18n.language === 'ar' ? 'ml-2' : 'mr-2'}`} />
                        {t('trackProject.viewDetails') || "View Details"}
                    </Button>
                    <Button
                        variant="outline"
                        size="sm"
                        className="w-full border-blue-500 text-blue-500 hover:bg-blue-500 hover:text-white dark:border-blue-400 dark:text-blue-400 dark:hover:bg-blue-400 dark:hover:text-gray-900"
                        onClick={() => handleDownloadPdf(project.id)}
                    >
                        <Download className={`h-4 w-4 ${i18n.language === 'ar' ? 'ml-2' : 'mr-2'}`} />
                        {t('searchProject.downloadPdf') || "Download Project PDF"}
                    </Button>
                </CardFooter>
              </Card>
            </motion.div>
          ))}
        </motion.div>
        </>
      )}

      {!loading && hasSearched && searchResults.length === 0 && (
         <motion.div variants={itemVariants} className="mt-8 text-center text-gray-500 dark:text-gray-400 py-10">
          <FileText size={60} className="mx-auto mb-4 text-gray-400 dark:text-gray-500" />
          <p className="text-xl mb-1">{t('searchProject.noResultsFoundFor') || "No results found for:"} <strong>"{searchTerm}"</strong></p>
          <p className="text-sm">{t('searchProject.tryDifferentTerm') || "Try a different search term or criteria."}</p>
        </motion.div>
      )}

      {!loading && !hasSearched && (
        <motion.div variants={itemVariants} className="mt-8 text-center text-gray-500 dark:text-gray-400 py-10">
          <RotateCcw size={60} className="mx-auto mb-4 text-gray-400 dark:text-gray-500" />
          <p className="text-xl mb-1">{t('searchProject.startSearchingTitle') || "Start Your Search"}</p>
          <p className="text-sm">{t('searchProject.startSearching') || "Enter a project number, client name, or city to find projects."}</p>
        </motion.div>
      )}

    </motion.div>
  );
};

export default SearchProjectPage;
