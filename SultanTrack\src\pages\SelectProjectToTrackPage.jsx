import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import { Search, Eye, Calendar, MapPin, User, Building } from 'lucide-react';
import { getAllProjects } from '@/lib/supabaseDatabase';

const SelectProjectToTrackPage = () => {
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const { toast } = useToast();

  const [projects, setProjects] = useState([]);
  const [filteredProjects, setFilteredProjects] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchProjects();
  }, []);

  useEffect(() => {
    filterProjects();
  }, [searchTerm, projects]);

  const fetchProjects = async () => {
    setLoading(true);
    try {
      const projectsData = await getAllProjects();
      setProjects(projectsData || []);
    } catch (error) {
      console.error('Error fetching projects:', error);
      toast({
        title: t('allProjects.fetchErrorTitle') || "Error",
        description: t('allProjects.fetchErrorMsg') || "Could not fetch projects list.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const filterProjects = () => {
    if (!searchTerm.trim()) {
      setFilteredProjects(projects);
      return;
    }

    const filtered = projects.filter(project => {
      const city = project.city || project.project_location || project.address || '';
      return (
        project.project_number?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        project.client_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        city.toLowerCase().includes(searchTerm.toLowerCase()) ||
        project.status?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    });
    setFilteredProjects(filtered);
  };

  const handleTrackProject = (projectId) => {
    navigate(`/track-project/${projectId}`);
  };

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'completed':
      case 'مكتمل':
        return 'text-green-600 bg-green-100 dark:text-green-400 dark:bg-green-900/20';
      case 'in progress':
      case 'قيد التنفيذ':
      case 'inprogress':
        return 'text-blue-600 bg-blue-100 dark:text-blue-400 dark:bg-blue-900/20';
      case 'delayed':
      case 'متأخر':
        return 'text-red-600 bg-red-100 dark:text-red-400 dark:bg-red-900/20';
      case 'not started':
      case 'لم يبدأ':
      case 'notstarted':
        return 'text-gray-600 bg-gray-100 dark:text-gray-400 dark:bg-gray-900/20';
      case 'pending':
      case 'معلق':
        return 'text-yellow-600 bg-yellow-100 dark:text-yellow-400 dark:bg-yellow-900/20';
      case 'on hold':
      case 'متوقف مؤقتاً':
      case 'onhold':
        return 'text-orange-600 bg-orange-100 dark:text-orange-400 dark:bg-orange-900/20';
      default:
        return 'text-gray-600 bg-gray-100 dark:text-gray-400 dark:bg-gray-900/20';
    }
  };

  const getStatusTranslation = (status) => {
    const statusMap = {
      'completed': t('dashboard.completed') || 'مكتمل',
      'in progress': t('dashboard.inProgress') || 'قيد التنفيذ',
      'inprogress': t('dashboard.inProgress') || 'قيد التنفيذ',
      'delayed': t('dashboard.delayed') || 'متأخر',
      'not started': t('dashboard.notStarted') || 'لم يبدأ',
      'notstarted': t('dashboard.notStarted') || 'لم يبدأ',
      'pending': t('dashboard.pending') || 'معلق',
      'on hold': t('dashboard.onHold') || 'متوقف مؤقتاً',
      'onhold': t('dashboard.onHold') || 'متوقف مؤقتاً',
      'مكتمل': t('dashboard.completed') || 'مكتمل',
      'قيد التنفيذ': t('dashboard.inProgress') || 'قيد التنفيذ',
      'متأخر': t('dashboard.delayed') || 'متأخر',
      'لم يبدأ': t('dashboard.notStarted') || 'لم يبدأ',
      'معلق': t('dashboard.pending') || 'معلق',
      'متوقف مؤقتاً': t('dashboard.onHold') || 'متوقف مؤقتاً'
    };
    return statusMap[status?.toLowerCase()] || status || t('common.notSpecified') || 'غير محدد';
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: { y: 0, opacity: 1, transition: { type: 'spring', stiffness: 100 } },
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-4 border-b-4 border-blue-500"></div>
      </div>
    );
  }

  return (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={{ visible: { transition: { staggerChildren: 0.1 } } }}
      className="p-4 md:p-6"
      dir={i18n.dir()}
    >
      <motion.div variants={itemVariants} className="mb-8">
        <h1 className="text-3xl font-bold text-gray-800 dark:text-white gradient-text mb-2">
          {t('sidebar.trackProject')}
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          {t('trackProject.selectProjectDescription') || "Select a project to track its progress and manage visits"}
        </p>
      </motion.div>

      <motion.div variants={itemVariants} className="mb-6">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
          <Input
            type="text"
            placeholder={t('allProjects.searchPlaceholder') || "Search by number, client, city..."}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 dark:bg-gray-800 dark:border-gray-600 dark:text-white"
          />
        </div>
      </motion.div>

      {filteredProjects.length === 0 ? (
        <motion.div variants={itemVariants}>
          <Card className="shadow-md dark:bg-gray-800/60 border-gray-200 dark:border-gray-700/40">
            <CardContent className="flex flex-col items-center justify-center py-12">
              <Building className="h-16 w-16 text-gray-400 dark:text-gray-500 mb-4" />
              <h3 className="text-lg font-semibold text-gray-700 dark:text-gray-300 mb-2">
                {searchTerm ? t('allProjects.noProjectsFound') : t('allProjects.noProjectsMatchSearch')}
              </h3>
              <p className="text-gray-500 dark:text-gray-400 text-center">
                {searchTerm
                  ? t('allProjects.noProjectsMatchSearch')
                  : t('allProjects.tryAddingProjects')
                }
              </p>
            </CardContent>
          </Card>
        </motion.div>
      ) : (
        <motion.div variants={itemVariants} className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredProjects.map((project) => (
            <Card
              key={project.id}
              className="shadow-lg hover:shadow-xl transition-all duration-300 dark:bg-gray-800/80 border-gray-200 dark:border-gray-700/50 backdrop-blur-md hover:scale-105 cursor-pointer"
              onClick={() => handleTrackProject(project.id)}
            >
              <CardHeader className="pb-3">
                <CardTitle className="text-lg font-semibold text-gray-800 dark:text-white flex items-center justify-between">
                  <span className="flex items-center">
                    <Building className="h-5 w-5 text-blue-500 mr-2" />
                    {project.project_number}
                  </span>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(project.status)}`}>
                    {getStatusTranslation(project.status)}
                  </span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center text-sm text-gray-600 dark:text-gray-300">
                  <User className="h-4 w-4 mr-2 text-gray-400" />
                  <span className="font-medium">{project.client_name}</span>
                </div>

                <div className="flex items-center text-sm text-gray-600 dark:text-gray-300">
                  <MapPin className="h-4 w-4 mr-2 text-gray-400" />
                  <span>{project.city || project.project_location || project.address || 'غير محدد'}</span>
                </div>

                <div className="flex items-center text-sm text-gray-600 dark:text-gray-300">
                  <Calendar className="h-4 w-4 mr-2 text-gray-400" />
                  <span>{new Date(project.start_date).toLocaleDateString()}</span>
                </div>

                <div className="pt-3 border-t border-gray-200 dark:border-gray-700">
                  <Button
                    className="w-full bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600 text-white"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleTrackProject(project.id);
                    }}
                  >
                    <Eye className="h-4 w-4 mr-2" />
                    {t('trackProject.trackThisProject') || "Track This Project"}
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </motion.div>
      )}
    </motion.div>
  );
};

export default SelectProjectToTrackPage;
