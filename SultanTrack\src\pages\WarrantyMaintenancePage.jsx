
import React, { useState, useEffect, useCallback } from 'react';
import { motion } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { useToast } from '@/components/ui/use-toast';
import { PlusCircle, Edit, Trash2, Wrench, AlertTriangle, Loader2, ListX, FileText, Download } from 'lucide-react';
import {
  getAllProjects,
  addMaintenanceRequest,
  getAllMaintenanceRequests,
  updateMaintenanceRequest,
  deleteMaintenanceRequest
} from '@/lib/supabaseDatabase';
import {
  exportMaintenanceRequestsToExcel
} from '@/lib/warrantyExportService';
import {
  exportMaintenanceRequestsToPDF
} from '@/lib/simplePdfExport';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

const MaintenanceRequestFormModal = ({ isOpen, onClose, onSubmit, request, projects, isEditing, t, i18n }) => {
  const [formData, setFormData] = useState({
    project_id: '',
    issue_type: '',
    description: '',
    request_date: new Date().toISOString().split('T')[0],
    status: 'New',
    // حقول جديدة للطلبات الجديدة
    client_name: '',
    job_order_number: '',
    contract_date: '',
    client_phone: '',
    city: '',
    files: []
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isNewRequest, setIsNewRequest] = useState(false);

  useEffect(() => {
    if (isEditing && request) {
      setFormData({
        project_id: request.project_id || '',
        issue_type: request.issue_type || '',
        description: request.description || '',
        request_date: request.request_date ? new Date(request.request_date).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
        status: request.status || 'New',
        client_name: request.client_name || '',
        job_order_number: request.job_order_number || '',
        contract_date: request.contract_date ? new Date(request.contract_date).toISOString().split('T')[0] : '',
        client_phone: request.client_phone || '',
        city: request.city || '',
        files: request.files || []
      });
      setIsNewRequest(request.project_id === 'new');
    } else {
      setFormData({
        project_id: '', issue_type: '', description: '',
        request_date: new Date().toISOString().split('T')[0], status: 'New',
        client_name: '', job_order_number: '', contract_date: '',
        client_phone: '', city: '', files: []
      });
      setIsNewRequest(false);
    }
  }, [isEditing, request, isOpen]);

  const handleChange = (e) => {
    const { name, value } = e.target;

    // إذا تم تغيير المشروع إلى "جديد"
    if (name === 'project_id' && value === 'new') {
      setIsNewRequest(true);
    } else if (name === 'project_id' && value !== 'new') {
      setIsNewRequest(false);
    }

    setFormData({ ...formData, [name]: value });
  };

  const handleFileChange = (e) => {
    const files = Array.from(e.target.files);
    setFormData({ ...formData, files });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // التحقق من الحقول المطلوبة
    if (!formData.issue_type) {
      return;
    }

    // التحقق من الحقول المطلوبة للطلبات الجديدة
    if (isNewRequest) {
      if (!formData.client_name || !formData.job_order_number || !formData.client_phone) {
        return;
      }
    } else if (!formData.project_id) {
      return;
    }

    setIsSubmitting(true);
    await onSubmit(formData);
    setIsSubmitting(false);
  };

  if (!isOpen) return null;

  return (
    <motion.div
      initial={{ opacity: 0 }} animate={{ opacity: 1 }} exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-start justify-center p-4 pt-16 z-50"
      onClick={onClose}
    >
      <motion.div
        initial={{ scale: 0.9, opacity: 0 }} animate={{ scale: 1, opacity: 1 }} exit={{ scale: 0.9, opacity: 0 }}
        className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-xl w-full max-w-lg max-h-[80vh] overflow-y-auto"
        onClick={e => e.stopPropagation()}
        dir={i18n.dir()}
      >
        <h3 className="text-xl font-semibold mb-6 text-gray-800 dark:text-white">
          {isEditing ? t('warranty.editMaintenanceRequest') || "Edit Maintenance Request" : t('warranty.newMaintenanceRequest') || "New Maintenance Request"}
        </h3>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Label htmlFor="project_id" className="text-gray-700 dark:text-gray-300">{t('allProjects.projectNumber') || "Project Number"}</Label>
            <select
              id="project_id" name="project_id" value={formData.project_id} onChange={handleChange} required
              className="mt-1 block w-full p-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm h-10"
            >
              <option value="">{t('warranty.selectProject') || "-- Select Project --"}</option>
              <option value="new">{t('warranty.newRequest') || "جديد"}</option>
              {projects.map(p => (
                  <option key={p.id} value={p.id}>{p.project_number} - {p.client_name}</option>
              ))}
            </select>
          </div>

          {/* الحقول الجديدة للطلبات الجديدة */}
          {isNewRequest && (
            <>
              <div>
                <Label htmlFor="client_name" className="text-gray-700 dark:text-gray-300">{t('warranty.clientName') || "اسم العميل"}</Label>
                <Input id="client_name" name="client_name" value={formData.client_name} onChange={handleChange} required className="mt-1 dark:bg-gray-700 dark:border-gray-600 dark:text-white focus:ring-blue-500 dark:focus:ring-blue-400" />
              </div>

              <div>
                <Label htmlFor="job_order_number" className="text-gray-700 dark:text-gray-300">{t('warranty.jobOrderNumber') || "رقم Job order أو العقد"}</Label>
                <Input id="job_order_number" name="job_order_number" value={formData.job_order_number} onChange={handleChange} required className="mt-1 dark:bg-gray-700 dark:border-gray-600 dark:text-white focus:ring-blue-500 dark:focus:ring-blue-400" />
              </div>

              <div>
                <Label htmlFor="contract_date" className="text-gray-700 dark:text-gray-300">{t('warranty.contractDate') || "تاريخ التعاقد"}</Label>
                <Input type="date" id="contract_date" name="contract_date" value={formData.contract_date} onChange={handleChange} className="mt-1 dark:bg-gray-700 dark:border-gray-600 dark:text-white focus:ring-blue-500 dark:focus:ring-blue-400" />
              </div>

              <div>
                <Label htmlFor="client_phone" className="text-gray-700 dark:text-gray-300">{t('warranty.clientPhone') || "رقم جوال العميل"}</Label>
                <Input id="client_phone" name="client_phone" value={formData.client_phone} onChange={handleChange} required className="mt-1 dark:bg-gray-700 dark:border-gray-600 dark:text-white focus:ring-blue-500 dark:focus:ring-blue-400" />
              </div>

              <div>
                <Label htmlFor="city" className="text-gray-700 dark:text-gray-300">{t('warranty.city') || "المدينة"}</Label>
                <Input id="city" name="city" value={formData.city} onChange={handleChange} className="mt-1 dark:bg-gray-700 dark:border-gray-600 dark:text-white focus:ring-blue-500 dark:focus:ring-blue-400" />
              </div>

              <div>
                <Label htmlFor="files" className="text-gray-700 dark:text-gray-300">{t('warranty.attachFiles') || "إرفاق ملفات"}</Label>
                <Input type="file" id="files" name="files" onChange={handleFileChange} multiple accept=".pdf,.jpg,.jpeg,.png,.doc,.docx" className="mt-1 dark:bg-gray-700 dark:border-gray-600 dark:text-white focus:ring-blue-500 dark:focus:ring-blue-400" />
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">{t('warranty.fileTypes') || "PDF, صور, مستندات - حتى 10MB لكل ملف"}</p>
              </div>
            </>
          )}

          <div>
            <Label htmlFor="issue_type" className="text-gray-700 dark:text-gray-300">{t('warranty.issueType') || "Issue Type"}</Label>
            <Input id="issue_type" name="issue_type" value={formData.issue_type} onChange={handleChange} required className="mt-1 dark:bg-gray-700 dark:border-gray-600 dark:text-white focus:ring-blue-500 dark:focus:ring-blue-400" />
          </div>
          <div>
            <Label htmlFor="description" className="text-gray-700 dark:text-gray-300">{t('warranty.description') || "Description"}</Label>
            <Textarea id="description" name="description" value={formData.description} onChange={handleChange} className="mt-1 dark:bg-gray-700 dark:border-gray-600 dark:text-white min-h-[80px] focus:ring-blue-500 dark:focus:ring-blue-400" />
          </div>
          <div>
            <Label htmlFor="request_date" className="text-gray-700 dark:text-gray-300">{t('warranty.requestDate') || "Request Date"}</Label>
            <Input type="date" id="request_date" name="request_date" value={formData.request_date} onChange={handleChange} required className="mt-1 dark:bg-gray-700 dark:border-gray-600 dark:text-white focus:ring-blue-500 dark:focus:ring-blue-400" />
          </div>
           <div>
            <Label htmlFor="status" className="text-gray-700 dark:text-gray-300">{t('allProjects.status') || "Status"}</Label>
             <select id="status" name="status" value={formData.status} onChange={handleChange} className="mt-1 block w-full p-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm h-10">
                <option value="New">{t('warranty.status_new') || "New"}</option>
                <option value="In Progress">{t('warranty.status_inprogress') || "In Progress"}</option>
                <option value="Closed">{t('warranty.status_closed') || "Closed"}</option>
             </select>
          </div>
          <div className="flex justify-end space-x-3 rtl:space-x-reverse pt-4">
            <Button type="button" variant="ghost" onClick={onClose} className="dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700">{t('warranty.cancelButton') || "Cancel"}</Button>
            <Button type="submit" className="bg-blue-600 hover:bg-blue-700 text-white min-w-[120px]" disabled={isSubmitting}>
              {isSubmitting ? <Loader2 className="h-5 w-5 animate-spin" /> : (isEditing ? t('userManagement.saveChangesButton') || "Save Changes" : t('warranty.submitButton') || "Submit Request")}
            </Button>
          </div>
        </form>
      </motion.div>
    </motion.div>
  );
};


const WarrantyMaintenancePage = () => {
  const { t, i18n } = useTranslation();
  const { toast } = useToast();
  const [maintenanceRequests, setMaintenanceRequests] = useState([]);
  const [allProjectsForDropdown, setAllProjectsForDropdown] = useState([]);
  const [loadingMaintenance, setLoadingMaintenance] = useState(true);

  const [showRequestModal, setShowRequestModal] = useState(false);
  const [currentRequest, setCurrentRequest] = useState(null);
  const [isEditingRequest, setIsEditingRequest] = useState(false);

  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [requestToDelete, setRequestToDelete] = useState(null);




  const fetchMaintenanceRequests = useCallback(async () => {
    setLoadingMaintenance(true);
    try {
      // الحصول على جميع طلبات الصيانة
      const requests = await getAllMaintenanceRequests();

      // ترتيب الطلبات حسب تاريخ الطلب (الأحدث أولاً)
      const sortedRequests = requests.sort((a, b) => {
        const dateA = new Date(a.request_date || a.created_at);
        const dateB = new Date(b.request_date || b.created_at);
        return dateB - dateA;
      });

      setMaintenanceRequests(sortedRequests);

      // إزالة رسالة الخطأ إذا كانت موجودة
      if (sortedRequests.length > 0) {
        console.log('✅ تم جلب طلبات الصيانة بنجاح:', sortedRequests.length);
      }
    } catch (error) {
      console.error('Error fetching maintenance requests:', error);
      toast({
        title: t('warranty.fetchErrorTitle') || "Error",
        description: t('warranty.fetchMaintenanceErrorMsg') || "Could not fetch maintenance requests.",
        variant: "destructive"
      });
      // تعيين قائمة فارغة في حالة الخطأ
      setMaintenanceRequests([]);
    } finally {
      setLoadingMaintenance(false);
    }
  }, [toast, t]);

  const fetchAllProjects = useCallback(async () => {
    try {
      // الحصول على جميع المشاريع
      const allProjects = await getAllProjects();

      // ترتيب المشاريع حسب رقم المشروع
      const sortedProjects = allProjects.sort((a, b) =>
        a.project_number.localeCompare(b.project_number)
      );

      setAllProjectsForDropdown(sortedProjects);
    } catch (error) {
      console.error('Error fetching projects for dropdown:', error);
      toast({
        title: t('warranty.fetchErrorTitle') || "Error",
        description: t('warranty.fetchProjectsErrorMsg') || "Could not fetch projects for dropdown.",
        variant: "warning"
      });
    }
  }, [toast, t]);

  useEffect(() => {
    fetchMaintenanceRequests();
    fetchAllProjects();
  }, [fetchMaintenanceRequests, fetchAllProjects]);

  const handleAddRequest = () => {
    setCurrentRequest(null);
    setIsEditingRequest(false);
    setShowRequestModal(true);
  };

  const handleEditRequest = (request) => {
    setCurrentRequest(request);
    setIsEditingRequest(true);
    setShowRequestModal(true);
  };

  const handleSubmitRequestForm = async (formData) => {
    // التحقق من الحقول المطلوبة
    if (!formData.issue_type) {
      toast({
        title: t('warranty.validationErrorTitle') || "Validation Error",
        description: t('warranty.validationErrorMsg') || "Required fields must be filled.",
        variant: "destructive"
      });
      return;
    }

    // التحقق من الحقول المطلوبة للطلبات الجديدة
    if (formData.project_id === 'new') {
      if (!formData.client_name || !formData.job_order_number || !formData.client_phone) {
        toast({
          title: t('warranty.validationErrorTitle') || "Validation Error",
          description: t('warranty.validationErrorMsg') || "Required fields must be filled.",
          variant: "destructive"
        });
        return;
      }
    } else if (!formData.project_id) {
      toast({
        title: t('warranty.validationErrorTitle') || "Validation Error",
        description: t('warranty.validationErrorMsg') || "Required fields must be filled.",
        variant: "destructive"
      });
      return;
    }

    try {
      // تحضير البيانات للإرسال
      const requestData = {
        ...formData,
        // إذا كان طلب جديد، تعيين project_id إلى null
        project_id: formData.project_id === 'new' ? null : formData.project_id
      };

      if (isEditingRequest && currentRequest) {
        // تحديث طلب صيانة موجود
        await updateMaintenanceRequest(currentRequest.id, requestData);
      } else {
        // إضافة طلب صيانة جديد
        await addMaintenanceRequest(requestData);
      }

      toast({
        title: isEditingRequest
          ? (t('warranty.updateRequestSuccessTitle') || "Request Updated")
          : (t('warranty.addRequestSuccessTitle') || "Request Added"),
        description: isEditingRequest
          ? (t('warranty.updateRequestSuccessMsg') || "Maintenance request updated.")
          : (t('warranty.addRequestSuccessMsg') || "Maintenance request added successfully."),
        className: "bg-green-500 text-white dark:bg-green-600"
      });

      fetchMaintenanceRequests();
      setShowRequestModal(false);
    } catch (error) {
      console.error('Error submitting maintenance request:', error);
      toast({
        title: t('warranty.addRequestErrorTitle') || "Error Submitting Request",
        description: error.message,
        variant: "destructive"
      });
    }
  };

  const confirmDeleteRequest = (request) => {
    setRequestToDelete(request);
    setShowDeleteConfirm(true);
  };

  const handleDeleteRequest = async () => {
    if (!requestToDelete) return;

    try {
      // حذف طلب الصيانة
      await deleteMaintenanceRequest(requestToDelete.id);

      toast({
        title: "Request Deleted",
        description: `Maintenance request for ${requestToDelete.projects?.project_number || 'N/A'} has been deleted.`,
        className: "bg-green-500 text-white dark:bg-green-600"
      });

      fetchMaintenanceRequests();
    } catch (error) {
      console.error('Error deleting maintenance request:', error);
      toast({
        title: "Error Deleting Request",
        description: error.message,
        variant: "destructive"
      });
    } finally {
      setShowDeleteConfirm(false);
      setRequestToDelete(null);
    }
  };


  const getStatusBadgeVariant = (status) => {
    switch (status) {
      case 'New': return 'info';
      case 'In Progress': return 'warning';
      case 'Closed': return 'success';
      default: return 'secondary';
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: { y: 0, opacity: 1, transition: { type: 'spring', stiffness: 100 } },
  };

  return (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={{ visible: { transition: { staggerChildren: 0.1 } } }}
      className="p-4 md:p-6"
      dir={i18n.dir()}
    >
      <motion.h1 variants={itemVariants} className="text-3xl font-bold text-gray-800 dark:text-white mb-8 gradient-text text-center">
        {t('sidebar.warrantyMaintenance')}
      </motion.h1>


      <motion.section variants={itemVariants}>
        <div className="flex flex-col sm:flex-row justify-between items-center mb-4 gap-4">
          <h2 className="text-2xl font-semibold text-gray-700 dark:text-gray-200 flex items-center">
            <Wrench className={`h-7 w-7 text-blue-500 ${i18n.language === 'ar' ? 'ml-3' : 'mr-3'}`} />
            {t('warranty.maintenanceRequestsTitle') || "Maintenance Requests"}
          </h2>

          <div className="flex flex-wrap gap-2">
            <Button
              onClick={() => {
                try {
                  exportMaintenanceRequestsToPDF(maintenanceRequests);
                } catch (error) {
                  console.error('Error exporting to PDF:', error);
                }
              }}
              variant="outline"
              className="flex items-center gap-2 border-red-500 text-red-500 hover:bg-red-500 hover:text-white"
              size="sm"
              disabled={loadingMaintenance || maintenanceRequests.length === 0}
            >
              <FileText className="h-4 w-4" />
              Export PDF
            </Button>

            <Button
              onClick={() => {
                try {
                  exportMaintenanceRequestsToExcel(maintenanceRequests, t('warranty.maintenanceRequestsTitle') || "Maintenance Requests", t);
                } catch (error) {
                  console.error('Error exporting to Excel:', error);
                }
              }}
              variant="outline"
              className="flex items-center gap-2 border-green-500 text-green-500 hover:bg-green-500 hover:text-white"
              size="sm"
              disabled={loadingMaintenance || maintenanceRequests.length === 0}
            >
              <Download className="h-4 w-4" />
              Export Excel
            </Button>

            <Button
              onClick={handleAddRequest}
              className="bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white shadow-md hover:shadow-lg transition-shadow"
              size="sm"
            >
              <PlusCircle className={`h-5 w-5 ${i18n.language === 'ar' ? 'ml-2' : 'mr-2'}`} />
              {t('warranty.addRequestButton') || "Add New Request"}
            </Button>
          </div>
        </div>
        <Card className="shadow-lg dark:bg-gray-800/80 border-gray-200 dark:border-gray-700/50 backdrop-blur-md">
          <CardContent className="p-0">
            {loadingMaintenance ? (
              <div className="p-6 text-center flex flex-col items-center">
                <Loader2 className="h-10 w-10 text-blue-500 animate-spin mb-2" />
                <p className="text-lg text-gray-600 dark:text-gray-300">{t('warranty.loadingMaintenance') || "Loading maintenance requests..."}</p>
              </div>
            ) :
            maintenanceRequests.length === 0 ? (
              <div className="p-6 text-center text-gray-500 dark:text-gray-400">
                <ListX size={48} className="mx-auto mb-2" />
                <p className="text-lg">{t('warranty.noMaintenanceRequests') || "No maintenance requests."}</p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader className="bg-gray-50 dark:bg-gray-700/50">
                    <TableRow>
                      <TableHead className="px-4 py-3 text-gray-600 dark:text-gray-300">{t('allProjects.projectNumber') || "Project No."}</TableHead>
                      <TableHead className="px-4 py-3 text-gray-600 dark:text-gray-300">{t('warranty.issueType') || "Issue Type"}</TableHead>
                      <TableHead className="px-4 py-3 text-gray-600 dark:text-gray-300">{t('warranty.requestDate') || "Request Date"}</TableHead>
                      <TableHead className="px-4 py-3 text-gray-600 dark:text-gray-300">{t('allProjects.status') || "Status"}</TableHead>
                      <TableHead className="px-4 py-3 text-gray-600 dark:text-gray-300">{t('allProjects.actions') || "Actions"}</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {maintenanceRequests.map(req => (
                      <TableRow key={req.id} className="hover:bg-gray-50 dark:hover:bg-gray-700/30 border-b dark:border-gray-700/50">
                        <TableCell className="px-4 py-3 text-gray-800 dark:text-gray-100">{req.projects?.project_number || t('warranty.projectN_A')}</TableCell>
                        <TableCell className="px-4 py-3 text-gray-600 dark:text-gray-300">{req.issue_type}</TableCell>
                        <TableCell className="px-4 py-3 text-gray-600 dark:text-gray-300">{new Date(req.request_date).toLocaleDateString()}</TableCell>
                        <TableCell className="px-4 py-3"><Badge variant={getStatusBadgeVariant(req.status)} className="text-xs">{t(`warranty.status_${req.status.toLowerCase().replace(' ', '')}`) || req.status}</Badge></TableCell>
                        <TableCell className="space-x-1 rtl:space-x-reverse px-4 py-3">
                          <Button variant="ghost" size="icon" onClick={() => handleEditRequest(req)} className="text-yellow-500 hover:text-yellow-700 dark:text-yellow-400 dark:hover:text-yellow-300 h-8 w-8"><Edit className="h-4 w-4"/></Button>
                          <Button variant="ghost" size="icon" onClick={() => confirmDeleteRequest(req)} className="text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 h-8 w-8"><Trash2 className="h-4 w-4"/></Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </CardContent>
        </Card>
      </motion.section>

      <MaintenanceRequestFormModal
        isOpen={showRequestModal}
        onClose={() => setShowRequestModal(false)}
        onSubmit={handleSubmitRequestForm}
        request={currentRequest}
        projects={allProjectsForDropdown}
        isEditing={isEditingRequest}
        t={t}
        i18n={i18n}
      />

      <AlertDialog open={showDeleteConfirm} onOpenChange={setShowDeleteConfirm}>
        <AlertDialogContent className="dark:bg-gray-800 dark:text-white">
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center">
              <AlertTriangle className="text-red-500 mr-2" />
              {t('warranty.confirmDeleteRequestTitle') || "Confirm Deletion"}
            </AlertDialogTitle>
            <AlertDialogDescription className="dark:text-gray-300">
              {t('warranty.confirmDeleteRequestMsg', { issue: requestToDelete?.issue_type, project: requestToDelete?.projects?.project_number || 'N/A' }) || `Are you sure you want to delete the maintenance request for "${requestToDelete?.issue_type}" on project ${requestToDelete?.projects?.project_number || 'N/A'}? This action cannot be undone.`}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel className="dark:bg-gray-700 dark:hover:bg-gray-600 dark:border-gray-600">{t('userManagement.cancelButton') || "Cancel"}</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteRequest} className="bg-red-600 hover:bg-red-700 dark:bg-red-500 dark:hover:bg-red-600 text-white">
              {t('userManagement.deleteButton') || "Delete"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

    </motion.div>
  );
};

export default WarrantyMaintenancePage;
