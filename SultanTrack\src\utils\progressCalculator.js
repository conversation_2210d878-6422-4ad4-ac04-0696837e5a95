import { getProjectVisits } from '@/lib/supabaseDatabase';

/**
 * حساب إجمالي الخامات المركبة من جميع الزيارات
 * @param {Array} visits - قائمة الزيارات
 * @param {string} materialType - نوع المادة (pvc, shutter, sgs, doors, glass)
 * @returns {number} إجمالي الكمية المركبة
 */
export const calculateTotalInstalled = (visits, materialType) => {
  return visits.reduce((sum, visit) => {
    const materialsInstalled = visit.materials_installed || {};
    return sum + (materialsInstalled[`quantity_${materialType}`] || 0);
  }, 0);
};

/**
 * حساب نسبة التقدم الإجمالية للمشروع
 * @param {Object} project - بيانات المشروع
 * @param {Array} visits - قائمة الزيارات
 * @returns {number} نسبة التقدم (0-100)
 */
export const calculateProjectProgress = (project, visits) => {
  const materialTypes = ['pvc', 'shutter', 'sgs', 'doors', 'glass'];
  
  let totalProgress = 0;
  let validMaterials = 0;

  materialTypes.forEach(materialType => {
    const totalPlanned = project.materials?.[`quantity_${materialType}`] || project[`quantity_${materialType}`] || 0;
    
    if (totalPlanned > 0) {
      const totalInstalled = calculateTotalInstalled(visits, materialType);
      const materialProgress = Math.min((totalInstalled / totalPlanned) * 100, 100);
      totalProgress += materialProgress;
      validMaterials++;
    }
  });

  return validMaterials > 0 ? Math.round(totalProgress / validMaterials) : 0;
};

/**
 * حساب نسبة التقدم لمشروع واحد مع جلب الزيارات
 * @param {Object} project - بيانات المشروع
 * @returns {Promise<number>} نسبة التقدم
 */
export const calculateProjectProgressWithVisits = async (project) => {
  try {
    const visits = await getProjectVisits(project.id);
    return calculateProjectProgress(project, visits || []);
  } catch (error) {
    console.error('Error calculating project progress:', error);
    return 0;
  }
};

/**
 * حساب نسبة التقدم لعدة مشاريع
 * @param {Array} projects - قائمة المشاريع
 * @returns {Promise<Array>} المشاريع مع نسبة التقدم المحسوبة
 */
export const calculateProgressForProjects = async (projects) => {
  const projectsWithProgress = await Promise.all(
    projects.map(async (project) => {
      const progress = await calculateProjectProgressWithVisits(project);
      return {
        ...project,
        progress_percentage: progress
      };
    })
  );
  
  return projectsWithProgress;
};

/**
 * حساب تفاصيل التقدم لكل مادة
 * @param {Object} project - بيانات المشروع
 * @param {Array} visits - قائمة الزيارات
 * @returns {Object} تفاصيل التقدم لكل مادة
 */
export const calculateDetailedProgress = (project, visits) => {
  const materialTypes = ['pvc', 'shutter', 'sgs', 'doors', 'glass'];
  const progressDetails = {};

  materialTypes.forEach(materialType => {
    const totalPlanned = project.materials?.[`quantity_${materialType}`] || project[`quantity_${materialType}`] || 0;
    const totalInstalled = calculateTotalInstalled(visits, materialType);
    const percentage = totalPlanned > 0 ? Math.min((totalInstalled / totalPlanned) * 100, 100) : 0;
    const remaining = Math.max(totalPlanned - totalInstalled, 0);

    progressDetails[materialType] = {
      planned: totalPlanned,
      installed: totalInstalled,
      percentage: Math.round(percentage),
      remaining: remaining
    };
  });

  return progressDetails;
};
